/**
 * Simplified Script.js - User Interface JavaScript
 * 
 * This file contains user interface specific functions that use the consolidated
 * DMS utilities. Common functions (API, CSRF, toast) are now handled by dms-core.js
 * 
 * Dependencies: dms-core.js, utilities-consolidated.js
 */

// Loading state management
function showLoading() {
    const loading = document.getElementById("loading");
    if (loading) {
        loading.style.display = "block";
    }
}

function hideLoading() {
    const loading = document.getElementById("loading");
    if (loading) {
        loading.style.display = "none";
    }
}

// Category management
async function fetchCategories() {
    const select = document.getElementById("categorySelect");
    if (!select) return;
    
    select.disabled = true;
    select.innerHTML = '<option value="">Loading categories...</option>';
    
    try {
        const { ok, json } = await api('/api/categories');
        
        if (ok && json.categories) {
            select.innerHTML = '<option value="">- Select Category -</option>';
            json.categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category;
                option.textContent = category;
                select.appendChild(option);
            });
        } else {
            select.innerHTML = '<option value="">Error loading categories</option>';
            showToast('Failed to load categories', 'error');
        }
    } catch (error) {
        select.innerHTML = '<option value="">Error loading categories</option>';
        showToast('Failed to load categories', 'error');
    } finally {
        select.disabled = false;
    }
}

// Query submission
async function submitQuery() {
    const categorySelect = document.getElementById("category");
    const queryInput = document.getElementById("query");
    
    if (!categorySelect || !queryInput) {
        showToast("Required elements not found", "error");
        return;
    }
    
    const category = categorySelect.value;
    const query = queryInput.value.trim();
    
    if (!category) {
        showToast("Please select a category.", "error");
        return;
    }
    if (!query) {
        showToast("Query cannot be empty.", "error");
        return;
    }
    
    appendMessage("User", query);
    queryInput.value = "";
    showLoading();
    
    try {
        const { ok, json } = await api('/api/query', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ category, query })
        });
        
        hideLoading();
        
        if (ok && json.response) {
            appendMessage("AI", json.response);
            if (json.sources) {
                const sourcesHtml = json.sources.map(source => 
                    `<div class="source-item">${source}</div>`
                ).join('');
                appendMessage("AI", sourcesHtml);
            }
            showToast("Query processed successfully", "success");
        } else {
            appendMessage("AI", `Error: ${json.error || "Unknown error"}`);
            showToast("Error processing query", "error");
        }
    } catch (error) {
        hideLoading();
        appendMessage("AI", `Error: ${error.message}`);
        showToast("Error processing query", "error");
    }
}

// Message display
function appendMessage(sender, message) {
    const chatBox = document.getElementById("chat-box");
    if (!chatBox) return;
    
    const messageDiv = document.createElement("div");
    messageDiv.className = `message ${sender.toLowerCase()}-message`;
    
    const senderSpan = document.createElement("span");
    senderSpan.className = "message-sender";
    senderSpan.textContent = sender + ": ";
    
    const contentDiv = document.createElement("div");
    contentDiv.className = "message-content";
    contentDiv.innerHTML = message;
    
    messageDiv.appendChild(senderSpan);
    messageDiv.appendChild(contentDiv);
    chatBox.appendChild(messageDiv);
    
    // Scroll to bottom
    chatBox.scrollTop = chatBox.scrollHeight;
}

// Event handlers
function handleKeyPress(event) {
    if (event.key === "Enter" && !event.shiftKey) {
        event.preventDefault();
        submitQuery();
    }
}

// Session management
function clearAllSessionData() {
    // Clear localStorage
    const keysToKeep = ['theme']; // Keep theme preference
    const allKeys = Object.keys(localStorage);
    allKeys.forEach(key => {
        if (!keysToKeep.includes(key)) {
            localStorage.removeItem(key);
        }
    });
    
    // Clear sessionStorage
    sessionStorage.clear();
    
    // Clear chat box
    const chatBox = document.getElementById("chat-box");
    if (chatBox) {
        chatBox.innerHTML = "";
    }
    
    showToast("Session data cleared", "info");
}

// Model management
let availableModels = [];

async function loadAvailableModels() {
    try {
        const { ok, json } = await api('/api/models');
        
        if (ok && json.models) {
            availableModels = json.models;
            populateModelSelector();
        } else {
            showToast('Failed to load available models', 'error');
        }
    } catch (error) {
        showToast('Error loading models', 'error');
    }
}

function populateModelSelector() {
    const modelSelector = document.getElementById('model-selector');
    if (!modelSelector) return;
    
    modelSelector.innerHTML = '<option value="">-- Select Model --</option>';
    
    availableModels.forEach(model => {
        const option = document.createElement('option');
        option.value = model.id;
        option.textContent = model.name;
        option.dataset.description = model.description || '';
        option.dataset.size = model.size || '';
        modelSelector.appendChild(option);
    });
    
    // Set default model if available
    if (availableModels.length > 0) {
        modelSelector.value = availableModels[0].id;
        updateModelInfo(availableModels[0]);
    }
}

function updateModelInfo(model) {
    const modelInfo = document.getElementById('model-info');
    const modelDescription = document.getElementById('model-description');
    const modelSize = document.getElementById('model-size');
    
    if (modelInfo && modelDescription && modelSize) {
        if (model) {
            modelDescription.textContent = model.description || '';
            modelSize.textContent = model.size || '';
            modelInfo.style.display = 'block';
        } else {
            modelInfo.style.display = 'none';
        }
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Load categories
    fetchCategories();
    
    // Load models
    loadAvailableModels();
    
    // Set up model selector change handler
    const modelSelector = document.getElementById('model-selector');
    if (modelSelector) {
        modelSelector.addEventListener('change', function() {
            const selectedModel = availableModels.find(m => m.id === this.value);
            updateModelInfo(selectedModel);
        });
    }
    
    // Set up query input handler
    const queryInput = document.getElementById("query");
    if (queryInput) {
        queryInput.addEventListener("keypress", handleKeyPress);
    }
    
    // Set up submit button
    const submitButton = document.querySelector('button[onclick="sendQuery()"]');
    if (submitButton) {
        submitButton.onclick = submitQuery;
    }
    
    console.log('User interface initialized');
});

// Global functions for backward compatibility
window.showLoading = showLoading;
window.hideLoading = hideLoading;
window.submitQuery = submitQuery;
window.sendQuery = submitQuery; // Alias
window.appendMessage = appendMessage;
window.handleKeyPress = handleKeyPress;
window.clearAllSessionData = clearAllSessionData;
window.loadAvailableModels = loadAvailableModels;
