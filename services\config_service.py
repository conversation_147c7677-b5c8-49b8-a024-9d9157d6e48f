"""
Configuration Service

This service provides centralized configuration management with validation,
hot-reloading, and API endpoints for configuration updates.

Features:
- Configuration CRUD operations
- Real-time validation
- Configuration change notifications
- Backup and restore functionality
- Configuration templates

Version: 2.0.0
"""

import os
import json
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
from flask import current_app

from config.config_manager import (
    ConfigurationManager, 
    ApplicationConfig, 
    ConfigValidationService,
    get_config_manager,
    get_config
)

logger = logging.getLogger(__name__)

class ConfigService:
    """Service for configuration management operations"""
    
    def __init__(self):
        self.config_manager = get_config_manager()
        self.validation_service = ConfigValidationService()
    
    def get_current_config(self) -> Dict[str, Any]:
        """Get current configuration as dictionary"""
        try:
            config = self.config_manager.get_config()
            return {
                'success': True,
                'config': config.to_dict(),
                'last_updated': config.last_updated,
                'version': config.config_version
            }
        except Exception as e:
            logger.error(f"Error getting current configuration: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def update_config_section(self, section: str, updates: Dict[str, Any]) -> Dict[str, Any]:
        """Update a specific configuration section"""
        try:
            # Validate section name
            valid_sections = ['database', 'security', 'ai_models', 'processing']
            if section not in valid_sections:
                return {
                    'success': False,
                    'error': f'Invalid section. Must be one of: {valid_sections}'
                }
            
            # Get current configuration
            current_config = self.config_manager.get_config()
            
            # Apply updates
            section_updates = {section: updates}
            self.config_manager.update_config(section_updates)
            
            # Validate updated configuration
            validation_errors = self.validation_service.validate_full_config(
                self.config_manager.get_config()
            )
            
            if validation_errors:
                # Rollback changes if validation fails
                self.config_manager.config = current_config
                return {
                    'success': False,
                    'error': 'Configuration validation failed',
                    'validation_errors': validation_errors
                }
            
            # Save configuration
            self.config_manager.save_configuration()
            
            return {
                'success': True,
                'message': f'Configuration section "{section}" updated successfully',
                'updated_config': self.config_manager.get_config().to_dict()
            }
            
        except Exception as e:
            logger.error(f"Error updating configuration section {section}: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def validate_config(self, config_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Validate configuration data"""
        try:
            if config_data:
                # Validate provided configuration data
                temp_config = ApplicationConfig.from_dict(config_data)
                validation_errors = self.validation_service.validate_full_config(temp_config)
            else:
                # Validate current configuration
                validation_errors = self.validation_service.validate_full_config(
                    self.config_manager.get_config()
                )
            
            return {
                'success': True,
                'valid': len(validation_errors) == 0,
                'errors': validation_errors,
                'message': 'Configuration is valid' if not validation_errors else 'Configuration has validation errors'
            }
            
        except Exception as e:
            logger.error(f"Error validating configuration: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def backup_config(self, backup_name: Optional[str] = None) -> Dict[str, Any]:
        """Create a backup of current configuration"""
        try:
            if not backup_name:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_name = f"config_backup_{timestamp}"
            
            backup_dir = "config/backups"
            os.makedirs(backup_dir, exist_ok=True)
            
            backup_file = os.path.join(backup_dir, f"{backup_name}.json")
            
            # Save current configuration to backup file
            self.config_manager.save_configuration(backup_file)
            
            return {
                'success': True,
                'message': f'Configuration backed up successfully',
                'backup_file': backup_file,
                'backup_name': backup_name
            }
            
        except Exception as e:
            logger.error(f"Error backing up configuration: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def restore_config(self, backup_file: str) -> Dict[str, Any]:
        """Restore configuration from backup"""
        try:
            if not os.path.exists(backup_file):
                return {
                    'success': False,
                    'error': f'Backup file not found: {backup_file}'
                }
            
            # Load backup configuration
            with open(backup_file, 'r') as f:
                backup_data = json.load(f)
            
            # Validate backup configuration
            temp_config = ApplicationConfig.from_dict(backup_data)
            validation_errors = self.validation_service.validate_full_config(temp_config)
            
            if validation_errors:
                return {
                    'success': False,
                    'error': 'Backup configuration is invalid',
                    'validation_errors': validation_errors
                }
            
            # Create backup of current configuration before restore
            current_backup = self.backup_config("pre_restore_backup")
            
            # Restore configuration
            self.config_manager.config = temp_config
            self.config_manager.save_configuration()
            
            return {
                'success': True,
                'message': 'Configuration restored successfully',
                'current_backup': current_backup.get('backup_file'),
                'restored_from': backup_file
            }
            
        except Exception as e:
            logger.error(f"Error restoring configuration: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def list_backups(self) -> Dict[str, Any]:
        """List available configuration backups"""
        try:
            backup_dir = "config/backups"
            if not os.path.exists(backup_dir):
                return {
                    'success': True,
                    'backups': []
                }
            
            backups = []
            for file in os.listdir(backup_dir):
                if file.endswith('.json'):
                    file_path = os.path.join(backup_dir, file)
                    stat = os.stat(file_path)
                    
                    backups.append({
                        'name': file[:-5],  # Remove .json extension
                        'file': file_path,
                        'size': stat.st_size,
                        'created': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                        'modified': datetime.fromtimestamp(stat.st_mtime).isoformat()
                    })
            
            # Sort by creation time (newest first)
            backups.sort(key=lambda x: x['created'], reverse=True)
            
            return {
                'success': True,
                'backups': backups
            }
            
        except Exception as e:
            logger.error(f"Error listing backups: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_config_template(self, template_type: str = "default") -> Dict[str, Any]:
        """Get configuration template"""
        try:
            templates = {
                "default": ApplicationConfig(),
                "development": self._get_development_template(),
                "production": self._get_production_template(),
                "minimal": self._get_minimal_template()
            }
            
            if template_type not in templates:
                return {
                    'success': False,
                    'error': f'Invalid template type. Available: {list(templates.keys())}'
                }
            
            template_config = templates[template_type]
            
            return {
                'success': True,
                'template': template_config.to_dict(),
                'template_type': template_type,
                'description': self._get_template_description(template_type)
            }
            
        except Exception as e:
            logger.error(f"Error getting configuration template: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _get_development_template(self) -> ApplicationConfig:
        """Get development configuration template"""
        config = ApplicationConfig()
        
        # Development-specific settings
        config.security.csrf_enabled = False
        config.security.rate_limiting_enabled = False
        config.ai_models.request_timeout_seconds = 60
        config.processing.processing_threads = 2
        
        return config
    
    def _get_production_template(self) -> ApplicationConfig:
        """Get production configuration template"""
        config = ApplicationConfig()
        
        # Production-specific settings
        config.security.secret_key = "CHANGE-ME-IN-PRODUCTION"
        config.security.csrf_enabled = True
        config.security.rate_limiting_enabled = True
        config.security.session_timeout_minutes = 60
        config.ai_models.max_parallel_requests = 8
        config.processing.processing_threads = 8
        config.database.backup_enabled = True
        
        return config
    
    def _get_minimal_template(self) -> ApplicationConfig:
        """Get minimal configuration template"""
        config = ApplicationConfig()
        
        # Minimal settings
        config.ai_models.use_vision_model = False
        config.ai_models.use_vision_during_embedding = False
        config.processing.extract_images = False
        config.processing.extract_tables = False
        config.processing.extract_locations = False
        config.processing.processing_threads = 1
        
        return config
    
    def _get_template_description(self, template_type: str) -> str:
        """Get description for configuration template"""
        descriptions = {
            "default": "Default configuration with balanced settings for general use",
            "development": "Development configuration with relaxed security and debugging enabled",
            "production": "Production configuration with enhanced security and performance",
            "minimal": "Minimal configuration with basic features only"
        }
        
        return descriptions.get(template_type, "No description available")
    
    def reload_configuration(self) -> Dict[str, Any]:
        """Reload configuration from file"""
        try:
            self.config_manager.load_configuration()
            
            return {
                'success': True,
                'message': 'Configuration reloaded successfully',
                'config': self.config_manager.get_config().to_dict()
            }
            
        except Exception as e:
            logger.error(f"Error reloading configuration: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_config_status(self) -> Dict[str, Any]:
        """Get configuration system status"""
        try:
            config = self.config_manager.get_config()
            validation_errors = self.validation_service.validate_full_config(config)
            
            return {
                'success': True,
                'status': {
                    'config_file': self.config_manager.config_file,
                    'auto_reload_enabled': self.config_manager.auto_reload,
                    'last_updated': config.last_updated,
                    'config_version': config.config_version,
                    'validation_status': 'valid' if not validation_errors else 'invalid',
                    'validation_errors': validation_errors,
                    'file_watchers_active': len(self.config_manager.observers) > 0,
                    'change_callbacks_registered': len(self.config_manager.change_callbacks)
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting configuration status: {e}")
            return {
                'success': False,
                'error': str(e)
            }
