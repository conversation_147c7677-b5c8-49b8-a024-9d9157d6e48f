<!DOCTYPE html>
<html lang="en" class="h-100">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}ERDB Knowledge Hub{% endblock %}</title>
    
    <!-- Favicon -->
    <link href="/static/img/favicon.ico" rel="icon">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Toastify CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
    
    <!-- Custom CSS -->
    <link href="/static/css/user.css" rel="stylesheet">
    
    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <!-- Additional Head Content -->
    {% block head %}{% endblock %}
    
    <!-- Theme Variables -->
    <style>
        :root {
            --erdb-primary: #5BA85B;
            --erdb-primary-dark: #378C47;
            --erdb-secondary: #3CA6D6;
            --erdb-secondary-dark: #0267B6;
            --erdb-accent: #FFBD5C;
            --erdb-accent-dark: #FC762B;
            --erdb-danger: #C12323;
            --erdb-light: #f8f9fa;
            --erdb-dark: #212529;
            
            /* Theme-aware colors */
            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --bg-card: #ffffff;
            --text-primary: #212529;
            --text-secondary: #6c757d;
            --border-color: #dee2e6;
            --shadow: rgba(0, 0, 0, 0.1);
        }
        
        .dark-mode,
        .dark {
            --bg-primary: #1a1a1a;
            --bg-secondary: #2d2d2d;
            --bg-card: #2d2d2d;
            --text-primary: #ffffff;
            --text-secondary: #b0b0b0;
            --border-color: #404040;
            --shadow: rgba(0, 0, 0, 0.3);
        }
        
        body {
            background-color: var(--bg-primary);
            color: var(--text-primary);
            font-family: 'Inter', sans-serif;
            transition: background-color 0.3s ease, color 0.3s ease;
        }
        
        .bg-card {
            background-color: var(--bg-card) !important;
        }
        
        .border-standard {
            border-color: var(--border-color) !important;
        }
        
        .text-primary {
            color: var(--text-primary) !important;
        }
        
        .text-secondary {
            color: var(--text-secondary) !important;
        }
        
        .shadow-custom {
            box-shadow: 0 0.125rem 0.25rem var(--shadow);
        }
    </style>
</head>

<body class="h-100 d-flex flex-column">
    <!-- Navigation -->
    {% set navbar_type = navbar_type or 'user' %}
    {% include 'components/navbar.html' with context %}
    
    <!-- Main Content -->
    <main class="flex-grow-1 d-flex flex-column">
        <!-- Page Header -->
        {% block page_header %}
        {% if self.page_title() %}
        <div class="container-fluid py-3 bg-card border-bottom border-standard">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col">
                        <h1 class="h3 text-primary mb-0">{% block page_title %}{% endblock %}</h1>
                        {% if self.page_subtitle() %}
                        <p class="text-secondary mb-0">{% block page_subtitle %}{% endblock %}</p>
                        {% endif %}
                    </div>
                    <div class="col-auto">
                        {% block page_actions %}{% endblock %}
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
        {% endblock %}
        
        <!-- Flash Messages -->
        <div class="container mt-3">
            {% from 'components/form_components.html' import flash_messages %}
            {{ flash_messages() }}
        </div>
        
        <!-- Content Area -->
        <div class="flex-grow-1 d-flex flex-column">
            {% block content %}{% endblock %}
        </div>
    </main>
    
    <!-- Footer -->
    {% block footer %}
    <footer class="bg-card border-top border-standard py-3 mt-auto">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <small class="text-secondary">
                        &copy; {{ now.year }} ERDB Knowledge Hub. All rights reserved.
                    </small>
                </div>
                <div class="col-md-6 text-md-end">
                    <small class="text-secondary">
                        Powered by ERDB AI | Version 2.0.0
                    </small>
                </div>
            </div>
        </div>
    </footer>
    {% endblock %}
    
    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay d-none">
        <div class="loading-spinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <div class="mt-2 text-primary">Processing...</div>
        </div>
    </div>
    
    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Toastify JS -->
    <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
    
    <!-- Utilities Script -->
    <script src="/static/js/utilities.js"></script>
    
    <!-- User Interface JavaScript -->
    <script>
        // Initialize user interface
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize theme
            if (typeof DMSUtils !== 'undefined') {
                DMSUtils.initializeTheme();
            }
            
            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
            
            // Initialize popovers
            var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl);
            });
            
            // Auto-hide alerts after 5 seconds
            setTimeout(function() {
                $('.alert:not(.alert-permanent)').fadeOut();
            }, 5000);
        });
        
        // Loading overlay functions
        function showLoading(message = 'Processing...') {
            const overlay = document.getElementById('loading-overlay');
            const messageEl = overlay.querySelector('.mt-2');
            if (messageEl) {
                messageEl.textContent = message;
            }
            overlay.classList.remove('d-none');
        }
        
        function hideLoading() {
            const overlay = document.getElementById('loading-overlay');
            overlay.classList.add('d-none');
        }
        
        // Make functions globally available
        window.showLoading = showLoading;
        window.hideLoading = hideLoading;
    </script>
    
    <!-- Page-specific JavaScript -->
    {% block scripts %}{% endblock %}
    
    <!-- Additional Styles -->
    <style>
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
        
        .loading-spinner {
            text-align: center;
            background: var(--bg-card);
            padding: 2rem;
            border-radius: 0.5rem;
            box-shadow: 0 0.5rem 1rem var(--shadow);
        }
        
        .top-nav {
            background: var(--bg-card);
            border-bottom: 1px solid var(--border-color);
            padding: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .nav-links {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .nav-link {
            color: var(--text-primary);
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover {
            background: var(--bg-secondary);
            color: var(--erdb-primary);
        }
        
        .logout-button {
            background: var(--erdb-danger);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .logout-button:hover {
            background: #a01e1e;
            transform: translateY(-1px);
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
            .top-nav {
                flex-direction: column;
                gap: 1rem;
            }
            
            .nav-links {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</body>
</html>
