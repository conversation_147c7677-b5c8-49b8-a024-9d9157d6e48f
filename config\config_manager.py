"""
Enhanced Centralized Configuration Manager

This module provides a comprehensive configuration management system with:
- Hot-reloading capabilities
- Configuration validation
- Environment variable integration
- JSON/YAML configuration file support
- Configuration change notifications
- Type safety and validation

Version: 2.0.0
"""

import os
import json
import yaml
import logging
from typing import Dict, Any, Optional, Callable, List, Union
from dataclasses import dataclass, field, asdict
from pathlib import Path
from datetime import datetime
import threading
import time
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

logger = logging.getLogger(__name__)

@dataclass
class ConfigSection:
    """Base class for configuration sections"""
    
    def validate(self) -> List[str]:
        """Validate configuration section. Return list of errors."""
        return []
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]):
        """Create instance from dictionary"""
        return cls(**data)

@dataclass
class DatabaseConfig(ConfigSection):
    """Database configuration section"""
    db_path: str = "./database.db"
    user_db_path: str = "./user_management.db"
    scraped_db_path: str = "./scraped_pages.db"
    chroma_path: str = "./chroma"
    backup_enabled: bool = True
    backup_interval_hours: int = 24
    
    def validate(self) -> List[str]:
        errors = []
        
        # Validate paths
        for path_name, path_value in [
            ("db_path", self.db_path),
            ("user_db_path", self.user_db_path),
            ("scraped_db_path", self.scraped_db_path),
            ("chroma_path", self.chroma_path)
        ]:
            if not path_value:
                errors.append(f"{path_name} cannot be empty")
            elif not os.path.isabs(path_value):
                # Ensure parent directory exists for relative paths
                parent_dir = os.path.dirname(path_value)
                if parent_dir and not os.path.exists(parent_dir):
                    try:
                        os.makedirs(parent_dir, exist_ok=True)
                    except Exception as e:
                        errors.append(f"Cannot create directory for {path_name}: {e}")
        
        # Validate backup settings
        if self.backup_interval_hours < 1:
            errors.append("backup_interval_hours must be at least 1")
        
        return errors

@dataclass
class SecurityConfig(ConfigSection):
    """Security configuration section"""
    secret_key: str = "change-me-in-production"
    csrf_enabled: bool = True
    csrf_time_limit: int = 7200
    csrf_ssl_strict: bool = False
    session_timeout_minutes: int = 120
    max_login_attempts: int = 5
    password_expiry_days: int = 90
    rate_limiting_enabled: bool = True
    rate_limit_per_minute: int = 60
    
    def validate(self) -> List[str]:
        errors = []
        
        # Validate secret key
        if not self.secret_key or self.secret_key == "change-me-in-production":
            if os.getenv('FLASK_ENV') == 'production':
                errors.append("secret_key must be changed in production")
        
        if len(self.secret_key) < 16:
            errors.append("secret_key should be at least 16 characters long")
        
        # Validate timeouts and limits
        if self.csrf_time_limit < 300:  # 5 minutes minimum
            errors.append("csrf_time_limit should be at least 300 seconds")
        
        if self.session_timeout_minutes < 5:
            errors.append("session_timeout_minutes should be at least 5")
        
        if self.max_login_attempts < 1:
            errors.append("max_login_attempts must be at least 1")
        
        if self.password_expiry_days < 1:
            errors.append("password_expiry_days must be at least 1")
        
        if self.rate_limit_per_minute < 1:
            errors.append("rate_limit_per_minute must be at least 1")
        
        return errors

@dataclass
class AIModelConfig(ConfigSection):
    """AI model configuration section"""
    llm_model: str = "llama3.1:8b-instruct-q4_K_M"
    embedding_model: str = "mxbai-embed-large:latest"
    vision_model: str = "llama3.2-vision:11b-instruct-q4_K_M"
    ollama_base_url: str = "http://localhost:11434"
    use_vision_model: bool = True
    use_vision_during_embedding: bool = True
    vision_cache_enabled: bool = True
    max_parallel_requests: int = 4
    request_timeout_seconds: int = 120
    
    def validate(self) -> List[str]:
        errors = []
        
        # Validate model names
        if not self.llm_model:
            errors.append("llm_model cannot be empty")
        
        if not self.embedding_model:
            errors.append("embedding_model cannot be empty")
        
        if self.use_vision_model and not self.vision_model:
            errors.append("vision_model cannot be empty when use_vision_model is True")
        
        # Validate Ollama URL
        if not self.ollama_base_url:
            errors.append("ollama_base_url cannot be empty")
        elif not self.ollama_base_url.startswith(('http://', 'https://')):
            errors.append("ollama_base_url must start with http:// or https://")
        
        # Validate performance settings
        if self.max_parallel_requests < 1:
            errors.append("max_parallel_requests must be at least 1")
        
        if self.request_timeout_seconds < 10:
            errors.append("request_timeout_seconds should be at least 10")
        
        return errors

@dataclass
class ProcessingConfig(ConfigSection):
    """Processing configuration section"""
    temp_folder: str = "./_temp"
    max_file_size_mb: int = 64
    chunk_size: int = 1000
    chunk_overlap: int = 200
    extract_tables: bool = True
    extract_images: bool = True
    extract_locations: bool = True
    max_pdf_images: int = 30
    pdf_filter_sensitivity: str = "medium"
    batch_size: int = 10
    processing_threads: int = 4
    
    def validate(self) -> List[str]:
        errors = []
        
        # Validate temp folder
        if not self.temp_folder:
            errors.append("temp_folder cannot be empty")
        
        # Validate file size
        if self.max_file_size_mb < 1:
            errors.append("max_file_size_mb must be at least 1")
        
        # Validate chunking settings
        if self.chunk_size < 100:
            errors.append("chunk_size should be at least 100")
        
        if self.chunk_overlap >= self.chunk_size:
            errors.append("chunk_overlap must be less than chunk_size")
        
        # Validate processing settings
        if self.max_pdf_images < 1:
            errors.append("max_pdf_images must be at least 1")
        
        if self.pdf_filter_sensitivity not in ["low", "medium", "high"]:
            errors.append("pdf_filter_sensitivity must be 'low', 'medium', or 'high'")
        
        if self.batch_size < 1:
            errors.append("batch_size must be at least 1")
        
        if self.processing_threads < 1:
            errors.append("processing_threads must be at least 1")
        
        return errors

@dataclass
class ApplicationConfig:
    """Main application configuration"""
    database: DatabaseConfig = field(default_factory=DatabaseConfig)
    security: SecurityConfig = field(default_factory=SecurityConfig)
    ai_models: AIModelConfig = field(default_factory=AIModelConfig)
    processing: ProcessingConfig = field(default_factory=ProcessingConfig)
    
    # Metadata
    config_version: str = "2.0.0"
    last_updated: Optional[str] = None
    
    def validate(self) -> Dict[str, List[str]]:
        """Validate all configuration sections"""
        errors = {}
        
        for section_name, section in [
            ("database", self.database),
            ("security", self.security),
            ("ai_models", self.ai_models),
            ("processing", self.processing)
        ]:
            section_errors = section.validate()
            if section_errors:
                errors[section_name] = section_errors
        
        return errors
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "database": self.database.to_dict(),
            "security": self.security.to_dict(),
            "ai_models": self.ai_models.to_dict(),
            "processing": self.processing.to_dict(),
            "config_version": self.config_version,
            "last_updated": self.last_updated
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]):
        """Create instance from dictionary"""
        return cls(
            database=DatabaseConfig.from_dict(data.get("database", {})),
            security=SecurityConfig.from_dict(data.get("security", {})),
            ai_models=AIModelConfig.from_dict(data.get("ai_models", {})),
            processing=ProcessingConfig.from_dict(data.get("processing", {})),
            config_version=data.get("config_version", "2.0.0"),
            last_updated=data.get("last_updated")
        )

class ConfigFileHandler(FileSystemEventHandler):
    """File system event handler for configuration file changes"""
    
    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.last_modified = {}
    
    def on_modified(self, event):
        if event.is_directory:
            return
        
        file_path = event.src_path
        if not file_path.endswith(('.json', '.yaml', '.yml')):
            return
        
        # Debounce rapid file changes
        current_time = time.time()
        if file_path in self.last_modified:
            if current_time - self.last_modified[file_path] < 1.0:  # 1 second debounce
                return
        
        self.last_modified[file_path] = current_time
        
        logger.info(f"Configuration file changed: {file_path}")
        self.config_manager._reload_from_file()

class ConfigurationManager:
    """Enhanced configuration manager with hot-reloading and validation"""
    
    def __init__(self, config_file: Optional[str] = None, auto_reload: bool = True):
        self.config_file = config_file or "config/app_config.json"
        self.auto_reload = auto_reload
        self.config = ApplicationConfig()
        self.observers = []
        self.change_callbacks: List[Callable[[ApplicationConfig], None]] = []
        self._lock = threading.Lock()
        
        # Load initial configuration
        self.load_configuration()
        
        # Set up file watching if auto-reload is enabled
        if self.auto_reload:
            self._setup_file_watching()
    
    def load_configuration(self):
        """Load configuration from file and environment variables"""
        with self._lock:
            # Load from file if it exists
            if os.path.exists(self.config_file):
                self._load_from_file()
            
            # Override with environment variables
            self._load_from_environment()
            
            # Update timestamp
            self.config.last_updated = datetime.now().isoformat()
            
            # Validate configuration
            errors = self.config.validate()
            if errors:
                logger.warning(f"Configuration validation errors: {errors}")
            
            logger.info("Configuration loaded successfully")
    
    def _load_from_file(self):
        """Load configuration from file"""
        try:
            with open(self.config_file, 'r') as f:
                if self.config_file.endswith('.json'):
                    data = json.load(f)
                else:  # YAML
                    data = yaml.safe_load(f)
            
            self.config = ApplicationConfig.from_dict(data)
            logger.info(f"Configuration loaded from {self.config_file}")
            
        except Exception as e:
            logger.error(f"Error loading configuration from {self.config_file}: {e}")
    
    def _load_from_environment(self):
        """Load configuration from environment variables"""
        # Database configuration
        if os.getenv('DB_PATH'):
            self.config.database.db_path = os.getenv('DB_PATH')
        if os.getenv('USER_DB_PATH'):
            self.config.database.user_db_path = os.getenv('USER_DB_PATH')
        if os.getenv('CHROMA_PATH'):
            self.config.database.chroma_path = os.getenv('CHROMA_PATH')
        
        # Security configuration
        if os.getenv('FLASK_SECRET_KEY'):
            self.config.security.secret_key = os.getenv('FLASK_SECRET_KEY')
        if os.getenv('SESSION_TIMEOUT_MINUTES'):
            self.config.security.session_timeout_minutes = int(os.getenv('SESSION_TIMEOUT_MINUTES'))
        
        # AI model configuration
        if os.getenv('LLM_MODEL'):
            self.config.ai_models.llm_model = os.getenv('LLM_MODEL')
        if os.getenv('EMBEDDING_MODEL'):
            self.config.ai_models.embedding_model = os.getenv('EMBEDDING_MODEL')
        if os.getenv('VISION_MODEL'):
            self.config.ai_models.vision_model = os.getenv('VISION_MODEL')
        if os.getenv('OLLAMA_BASE_URL'):
            self.config.ai_models.ollama_base_url = os.getenv('OLLAMA_BASE_URL')
        
        # Processing configuration
        if os.getenv('TEMP_FOLDER'):
            self.config.processing.temp_folder = os.getenv('TEMP_FOLDER')
        if os.getenv('MAX_PDF_IMAGES'):
            self.config.processing.max_pdf_images = int(os.getenv('MAX_PDF_IMAGES'))
    
    def _setup_file_watching(self):
        """Set up file system watching for configuration changes"""
        if not os.path.exists(self.config_file):
            return
        
        config_dir = os.path.dirname(os.path.abspath(self.config_file))
        
        observer = Observer()
        event_handler = ConfigFileHandler(self)
        observer.schedule(event_handler, config_dir, recursive=False)
        observer.start()
        
        self.observers.append(observer)
        logger.info(f"File watching enabled for {config_dir}")
    
    def _reload_from_file(self):
        """Reload configuration from file (called by file watcher)"""
        try:
            old_config = self.config.to_dict()
            self.load_configuration()
            
            # Notify callbacks of configuration change
            for callback in self.change_callbacks:
                try:
                    callback(self.config)
                except Exception as e:
                    logger.error(f"Error in configuration change callback: {e}")
            
            logger.info("Configuration reloaded from file")
            
        except Exception as e:
            logger.error(f"Error reloading configuration: {e}")
    
    def save_configuration(self, config_file: Optional[str] = None):
        """Save current configuration to file"""
        file_path = config_file or self.config_file
        
        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            with open(file_path, 'w') as f:
                if file_path.endswith('.json'):
                    json.dump(self.config.to_dict(), f, indent=2)
                else:  # YAML
                    yaml.dump(self.config.to_dict(), f, default_flow_style=False)
            
            logger.info(f"Configuration saved to {file_path}")
            
        except Exception as e:
            logger.error(f"Error saving configuration to {file_path}: {e}")
            raise
    
    def get_config(self) -> ApplicationConfig:
        """Get current configuration"""
        return self.config
    
    def update_config(self, updates: Dict[str, Any]):
        """Update configuration with new values"""
        with self._lock:
            # Apply updates to configuration
            for section_name, section_updates in updates.items():
                if hasattr(self.config, section_name):
                    section = getattr(self.config, section_name)
                    for key, value in section_updates.items():
                        if hasattr(section, key):
                            setattr(section, key, value)
            
            # Update timestamp
            self.config.last_updated = datetime.now().isoformat()
            
            # Validate updated configuration
            errors = self.config.validate()
            if errors:
                logger.warning(f"Configuration validation errors after update: {errors}")
            
            # Notify callbacks
            for callback in self.change_callbacks:
                try:
                    callback(self.config)
                except Exception as e:
                    logger.error(f"Error in configuration change callback: {e}")
    
    def add_change_callback(self, callback: Callable[[ApplicationConfig], None]):
        """Add callback to be called when configuration changes"""
        self.change_callbacks.append(callback)
    
    def remove_change_callback(self, callback: Callable[[ApplicationConfig], None]):
        """Remove configuration change callback"""
        if callback in self.change_callbacks:
            self.change_callbacks.remove(callback)
    
    def validate_configuration(self) -> Dict[str, List[str]]:
        """Validate current configuration and return errors"""
        return self.config.validate()
    
    def shutdown(self):
        """Shutdown configuration manager and stop file watching"""
        for observer in self.observers:
            observer.stop()
            observer.join()
        
        self.observers.clear()
        logger.info("Configuration manager shutdown")

# Global configuration manager instance
_config_manager: Optional[ConfigurationManager] = None

def get_config_manager() -> ConfigurationManager:
    """Get global configuration manager instance"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigurationManager()
    return _config_manager

def get_config() -> ApplicationConfig:
    """Get current application configuration"""
    return get_config_manager().get_config()

def reload_config():
    """Reload configuration from file"""
    get_config_manager().load_configuration()

def shutdown_config_manager():
    """Shutdown global configuration manager"""
    global _config_manager
    if _config_manager:
        _config_manager.shutdown()
        _config_manager = None

# Configuration validation service
class ConfigValidationService:
    """Service for validating configuration changes and dependencies"""

    @staticmethod
    def validate_ai_model_compatibility(config: AIModelConfig) -> List[str]:
        """Validate AI model compatibility"""
        errors = []

        # Check if vision model is compatible with vision settings
        if config.use_vision_model and not config.vision_model:
            errors.append("Vision model must be specified when use_vision_model is enabled")

        if config.use_vision_during_embedding and not config.use_vision_model:
            errors.append("use_vision_model must be enabled when use_vision_during_embedding is enabled")

        return errors

    @staticmethod
    def validate_processing_dependencies(processing: ProcessingConfig, ai_models: AIModelConfig) -> List[str]:
        """Validate processing configuration dependencies"""
        errors = []

        # Check if image extraction is enabled but vision model is not
        if processing.extract_images and not ai_models.use_vision_model:
            errors.append("Vision model should be enabled when extract_images is True for better results")

        # Validate thread count vs batch size
        if processing.processing_threads > processing.batch_size:
            errors.append("processing_threads should not exceed batch_size for optimal performance")

        return errors

    @staticmethod
    def validate_security_production(security: SecurityConfig) -> List[str]:
        """Validate security configuration for production"""
        errors = []

        if os.getenv('FLASK_ENV') == 'production':
            if security.secret_key == "change-me-in-production":
                errors.append("Secret key must be changed in production environment")

            if not security.csrf_enabled:
                errors.append("CSRF protection should be enabled in production")

            if not security.rate_limiting_enabled:
                errors.append("Rate limiting should be enabled in production")

        return errors

    @classmethod
    def validate_full_config(cls, config: ApplicationConfig) -> Dict[str, List[str]]:
        """Perform comprehensive configuration validation"""
        all_errors = config.validate()

        # Add cross-section validations
        ai_model_errors = cls.validate_ai_model_compatibility(config.ai_models)
        if ai_model_errors:
            all_errors.setdefault("ai_models", []).extend(ai_model_errors)

        processing_errors = cls.validate_processing_dependencies(config.processing, config.ai_models)
        if processing_errors:
            all_errors.setdefault("processing", []).extend(processing_errors)

        security_errors = cls.validate_security_production(config.security)
        if security_errors:
            all_errors.setdefault("security", []).extend(security_errors)

        return all_errors
