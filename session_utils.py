"""
Session Management Utilities

Provides centralized session management functionality including:
- Session creation and management
- Device fingerprinting
- Session persistence
- Session analytics

This module extracts session management logic from the main application
to improve modularity and maintainability.
"""

import uuid
import logging
from datetime import datetime
from flask import session, request

# Configure logging
logger = logging.getLogger(__name__)

def get_or_create_session():
    """
    Get existing session or create a new one.
    
    Manages user sessions with device fingerprinting and persistence
    across requests. Creates new sessions when needed and maintains
    session continuity.
    
    Returns:
        Tuple of (session_id, session_start, device_fingerprint)
    """
    try:
        # Get device fingerprint from request
        device_fingerprint = request.json.get('device_fingerprint') if request.json else None
        
        # Check if we have an existing session
        session_id = session.get('session_id')
        session_start = session.get('session_start')
        stored_fingerprint = session.get('device_fingerprint')
        
        # Create new session if needed
        if not session_id or not session_start:
            session_id = str(uuid.uuid4())
            session_start = datetime.now().isoformat()
            
            # Store in Flask session
            session['session_id'] = session_id
            session['session_start'] = session_start
            session.permanent = True
            
            logger.info(f"Created new session: {session_id}")
        
        # Update device fingerprint if provided
        if device_fingerprint and device_fingerprint != stored_fingerprint:
            session['device_fingerprint'] = device_fingerprint
            stored_fingerprint = device_fingerprint
            logger.info(f"Updated device fingerprint for session {session_id}")
        
        return session_id, session_start, stored_fingerprint
        
    except Exception as e:
        logger.error(f"Error managing session: {str(e)}")
        # Return fallback values
        fallback_session_id = str(uuid.uuid4())
        fallback_session_start = datetime.now().isoformat()
        return fallback_session_id, fallback_session_start, None

def get_session_info():
    """
    Get current session information.
    
    Returns:
        Dictionary with session information
    """
    try:
        return {
            'session_id': session.get('session_id'),
            'session_start': session.get('session_start'),
            'device_fingerprint': session.get('device_fingerprint'),
            'client_name': session.get('client_name', 'Anonymous')
        }
    except Exception as e:
        logger.error(f"Error getting session info: {str(e)}")
        return {}

def update_session_data(key, value):
    """
    Update session data with a key-value pair.
    
    Args:
        key: Session key to update
        value: Value to store
        
    Returns:
        Boolean indicating success
    """
    try:
        session[key] = value
        logger.debug(f"Updated session data: {key}")
        return True
    except Exception as e:
        logger.error(f"Error updating session data: {str(e)}")
        return False

def clear_session_data():
    """
    Clear all session data.
    
    Returns:
        Boolean indicating success
    """
    try:
        session_id = session.get('session_id', 'unknown')
        session.clear()
        logger.info(f"Cleared session data for session: {session_id}")
        return True
    except Exception as e:
        logger.error(f"Error clearing session data: {str(e)}")
        return False

def is_session_valid():
    """
    Check if the current session is valid.
    
    Returns:
        Boolean indicating if session is valid
    """
    try:
        session_id = session.get('session_id')
        session_start = session.get('session_start')
        
        if not session_id or not session_start:
            return False
        
        # Additional validation could be added here
        # (e.g., session timeout, device fingerprint validation)
        
        return True
    except Exception as e:
        logger.error(f"Error validating session: {str(e)}")
        return False

def get_device_fingerprint():
    """
    Get the device fingerprint for the current session.
    
    Returns:
        Device fingerprint string or None
    """
    try:
        return session.get('device_fingerprint')
    except Exception as e:
        logger.error(f"Error getting device fingerprint: {str(e)}")
        return None

def set_client_name(client_name):
    """
    Set the client name for the current session.
    
    Args:
        client_name: Name of the client
        
    Returns:
        Boolean indicating success
    """
    try:
        session['client_name'] = client_name
        logger.info(f"Set client name: {client_name} for session: {session.get('session_id')}")
        return True
    except Exception as e:
        logger.error(f"Error setting client name: {str(e)}")
        return False

def get_client_name():
    """
    Get the client name for the current session.
    
    Returns:
        Client name string or 'Anonymous'
    """
    try:
        return session.get('client_name', 'Anonymous')
    except Exception as e:
        logger.error(f"Error getting client name: {str(e)}")
        return 'Anonymous'

def extend_session():
    """
    Extend the current session lifetime.
    
    Returns:
        Boolean indicating success
    """
    try:
        session.permanent = True
        logger.debug(f"Extended session: {session.get('session_id')}")
        return True
    except Exception as e:
        logger.error(f"Error extending session: {str(e)}")
        return False

def get_session_duration():
    """
    Get the duration of the current session.
    
    Returns:
        Session duration in seconds or None
    """
    try:
        session_start = session.get('session_start')
        if not session_start:
            return None
        
        start_time = datetime.fromisoformat(session_start)
        current_time = datetime.now()
        duration = (current_time - start_time).total_seconds()
        
        return duration
    except Exception as e:
        logger.error(f"Error calculating session duration: {str(e)}")
        return None

def log_session_activity(activity_type, details=None):
    """
    Log session activity for analytics.
    
    Args:
        activity_type: Type of activity (e.g., 'query', 'upload', 'login')
        details: Additional details about the activity
        
    Returns:
        Boolean indicating success
    """
    try:
        session_id = session.get('session_id')
        client_name = session.get('client_name', 'Anonymous')
        
        log_entry = {
            'session_id': session_id,
            'client_name': client_name,
            'activity_type': activity_type,
            'timestamp': datetime.now().isoformat(),
            'details': details
        }
        
        logger.info(f"Session activity: {log_entry}")
        
        # Here you could save to database for analytics
        # db_utils.save_session_activity(log_entry)
        
        return True
    except Exception as e:
        logger.error(f"Error logging session activity: {str(e)}")
        return False
