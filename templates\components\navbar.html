<!--
Reusable Navigation Bar Component

This component provides a centralized navigation structure that can be used
across different templates to eliminate duplication. It supports both admin
and user interfaces with appropriate permission checking.

Usage:
{% include 'components/navbar.html' with context %}

Parameters:
- navbar_type: 'admin' or 'user' (default: 'admin')
- show_sidebar_toggle: boolean (default: true)
- show_search: boolean (default: true)
- show_notifications: boolean (default: true)
- show_quick_actions: boolean (default: true)
- show_user_profile: boolean (default: true)
-->

{% set navbar_type = navbar_type or 'admin' %}
{% set show_sidebar_toggle = show_sidebar_toggle if show_sidebar_toggle is defined else true %}
{% set show_search = show_search if show_search is defined else true %}
{% set show_notifications = show_notifications if show_notifications is defined else true %}
{% set show_quick_actions = show_quick_actions if show_quick_actions is defined else true %}
{% set show_user_profile = show_user_profile if show_user_profile is defined else true %}

{% if navbar_type == 'admin' %}
<!-- Admin Navigation Bar -->
<nav class="navbar navbar-expand bg-dark navbar-dark sticky-top px-4 py-0">
    <a href="{{ url_for('admin.admin_dashboard') }}" class="navbar-brand d-flex d-lg-none me-4">
        <h2 class="text-danger mb-0"><i class="fa fa-book-reader"></i></h2>
    </a>
    
    {% if show_sidebar_toggle %}
    <a href="#" class="sidebar-toggler flex-shrink-0" id="sidebarToggle">
        <i class="fa fa-bars"></i>
    </a>
    {% endif %}
    
    {% if show_search %}
    <form class="d-none d-md-flex ms-4">
        <input class="form-control bg-dark border-0" type="search" placeholder="Search">
    </form>
    {% endif %}
    
    <div class="navbar-nav align-items-center ms-auto">
        <!-- Theme Toggle -->
        {% include 'components/theme_toggle.html' %}
        
        {% if show_notifications %}
        <!-- Notifications -->
        <div class="nav-item dropdown">
            <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="dropdown">
                <i class="fa fa-bell me-lg-2"></i>
                <span class="d-none d-lg-inline-flex">Notifications</span>
                <span class="position-absolute badge rounded-pill bg-danger notification-badge">
                    <span class="visually-hidden">New notifications</span>
                </span>
            </a>
            <div class="dropdown-menu dropdown-menu-end bg-dark border-0 rounded-0 rounded-bottom m-0">
                <a href="#" class="dropdown-item">
                    <h6 class="fw-normal mb-0 text-light">No new notifications</h6>
                </a>
            </div>
        </div>
        {% endif %}
        
        {% if show_quick_actions %}
        <!-- Quick Actions -->
        <div class="nav-item dropdown">
            <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="dropdown">
                <i class="fa fa-bolt me-lg-2"></i>
                <span class="d-none d-lg-inline-flex">Quick Actions</span>
            </a>
            <div class="dropdown-menu dropdown-menu-end bg-dark border-0 rounded-0 rounded-bottom m-0">
                {% if current_user.is_authenticated and current_user.has_dashboard_permission('upload_content') %}
                <a href="{{ url_for('file_management.upload_file') }}" class="dropdown-item">
                    <h6 class="fw-normal mb-0 text-light"><i class="fas fa-upload me-2"></i> Upload Content</h6>
                </a>
                <hr class="dropdown-divider">
                {% endif %}
                {% if current_user.is_authenticated and current_user.has_dashboard_permission('manage_files') %}
                <a href="{{ url_for('file_management.list_files') }}" class="dropdown-item">
                    <h6 class="fw-normal mb-0 text-light"><i class="fas fa-file-alt me-2"></i> Manage Files</h6>
                </a>
                <hr class="dropdown-divider">
                {% endif %}
                <a href="{{ url_for('core.index') }}" target="_blank" class="dropdown-item">
                    <h6 class="fw-normal mb-0 text-light"><i class="fas fa-comments me-2"></i> Chat Interface</h6>
                </a>
            </div>
        </div>
        {% endif %}
        
        {% if show_user_profile %}
        <!-- User Profile -->
        <div class="nav-item dropdown">
            <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="dropdown">
                <div class="user-avatar rounded-circle me-lg-2">
                    {% if current_user.is_authenticated %}
                        {{ current_user.username[0].upper() }}
                    {% else %}
                        ?
                    {% endif %}
                </div>
                <span class="d-none d-lg-inline-flex">
                    {% if current_user.is_authenticated %}
                        {{ current_user.username }}
                    {% else %}
                        Guest
                    {% endif %}
                </span>
            </a>
            <div class="dropdown-menu dropdown-menu-end bg-dark border-0 rounded-0 rounded-bottom m-0">
                {% if current_user.is_authenticated %}
                    {% if current_user.has_dashboard_permission('edit_own_profile') %}
                    <a href="{{ url_for('user.profile') }}" class="dropdown-item">
                        <h6 class="fw-normal mb-0 text-light"><i class="fas fa-user me-2"></i> Profile</h6>
                    </a>
                    <hr class="dropdown-divider">
                    {% endif %}
                    <a href="#" onclick="showUserSettings()" class="dropdown-item">
                        <h6 class="fw-normal mb-0 text-light"><i class="fas fa-cog me-2"></i> Settings</h6>
                    </a>
                    <hr class="dropdown-divider">
                    <a href="{{ url_for('user.logout') }}" class="dropdown-item">
                        <h6 class="fw-normal mb-0 text-light"><i class="fas fa-sign-out-alt me-2"></i> Logout</h6>
                    </a>
                {% else %}
                    <a href="{{ url_for('user.login') }}" class="dropdown-item">
                        <h6 class="fw-normal mb-0 text-light"><i class="fas fa-sign-in-alt me-2"></i> Login</h6>
                    </a>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
</nav>

{% elif navbar_type == 'user' %}
<!-- User Interface Navigation Bar -->
<div class="top-nav">
    <div class="nav-links">
        <a href="{{ url_for('admin.admin_dashboard') }}" class="nav-link">&larr; Admin Dashboard</a>
    </div>
    <div class="nav-links">
        <button onclick="clearAllSessionData(); window.location.reload();" class="logout-button" title="Logout and clear chat history">
            <span id="logoutButton">Logout</span>
        </button>
        {% if show_voice_settings %}
        <button id="voice-settings-toggle" onclick="VoiceInterface.toggleSettingsPanel()" class="voice-settings-button" title="Voice Settings">
            <span class="voice-settings-icon">🔊</span>
        </button>
        {% endif %}
        {% include 'components/theme_toggle.html' with context %}
    </div>
</div>

{% elif navbar_type == 'chat' %}
<!-- Chat Interface Navigation Bar -->
<div class="card-header bg-card border-bottom border-standard d-flex justify-content-between align-items-center">
    <h1 class="h4 text-primary mb-0">ERDB Knowledge Hub - Powered by ERDB AI</h1>
    <div class="d-flex align-items-center gap-3">
        <a href="{{ url_for('admin.admin_dashboard') }}" class="text-primary">
            <i class="fas fa-tachometer-alt me-1"></i>Admin Dashboard
        </a>
        <button id="logout-button" class="btn btn-sm btn-outline-danger" title="Logout and clear chat history">
            <i class="fas fa-sign-out-alt me-1"></i><span id="logout-text">Logout</span>
        </button>
        {% include 'components/theme_toggle.html' with context %}
    </div>
</div>

{% endif %}
