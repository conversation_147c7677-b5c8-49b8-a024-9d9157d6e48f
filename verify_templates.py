#!/usr/bin/env python3
"""
Simple verification script to check if prompt templates are loaded correctly
and contain scientific name formatting instructions
"""

import json
import os

def verify_prompt_templates():
    """Verify that prompt templates contain scientific name formatting instructions"""

    print("🔍 Verifying Prompt Templates for Scientific Name Formatting")
    print("=" * 60)

    # Check default_models.json
    try:
        with open('default_models.json', 'r', encoding='utf-8') as f:
            config = json.load(f)

        print("✅ Successfully loaded default_models.json")

        # Check system prompt
        system_prompt = config.get('system_prompt', '')
        if 'scientific names' in system_prompt.lower():
            print("✅ System prompt contains scientific name instructions")
        else:
            print("❌ System prompt missing scientific name instructions")

        # Check prompt templates
        templates = config.get('prompt_templates', {})
        modes = ['strict', 'balanced', 'off', 'general', 'document_specific']

        for mode in modes:
            template = templates.get(mode, '')
            if 'SCIENTIFIC NAME FORMATTING' in template or 'scientific names' in template.lower():
                print(f"✅ {mode.upper()} template contains scientific name formatting section")

                # Check for specific instructions
                if '*Pterocarpus indicus*' in template:
                    print(f"   ✅ Contains Philippine species examples")
                if 'markdown italics' in template:
                    print(f"   ✅ Contains markdown italics instructions")
                if 'binomial nomenclature' in template:
                    print(f"   ✅ Contains binomial nomenclature instructions")
            else:
                print(f"❌ {mode.upper()} template missing scientific name formatting section")

        # Check follow-up question templates
        followup_templates = config.get('followup_question_templates', {})
        default_followup = followup_templates.get('default', '')
        if 'scientific names' in default_followup.lower():
            print("✅ Follow-up question template contains scientific name instructions")
        else:
            print("❌ Follow-up question template missing scientific name instructions")

    except FileNotFoundError:
        print("❌ default_models.json not found")
    except json.JSONDecodeError as e:
        print(f"❌ Error parsing default_models.json: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def check_environment_variables():
    """Check if environment variables are set correctly"""

    print("\n🌍 Checking Environment Variables")
    print("=" * 40)

    # Check if PROMPT_TEMPLATES is set
    prompt_templates = os.environ.get('PROMPT_TEMPLATES')
    if prompt_templates:
        print("✅ PROMPT_TEMPLATES environment variable is set")
        try:
            templates = json.loads(prompt_templates)
            print(f"✅ Contains {len(templates)} template(s)")

            # Check if templates contain scientific name formatting
            for mode, template in templates.items():
                if 'SCIENTIFIC NAME FORMATTING' in template:
                    print(f"   ✅ {mode} template has scientific name formatting")
                else:
                    print(f"   ❌ {mode} template missing scientific name formatting")

        except json.JSONDecodeError:
            print("❌ PROMPT_TEMPLATES contains invalid JSON")
    else:
        print("ℹ️  PROMPT_TEMPLATES environment variable not set (will use defaults)")

def test_template_examples():
    """Test template examples with scientific names"""

    print("\n🧪 Testing Template Examples")
    print("=" * 30)

    test_examples = [
        "*Pterocarpus indicus*",
        "*Homo sapiens*",
        "*Escherichia coli* (Migula 1895)",
        "*Bambusa blumeana*",
        "*Pinus sp.*"
    ]

    try:
        with open('default_models.json', 'r', encoding='utf-8') as f:
            config = json.load(f)

        templates = config.get('prompt_templates', {})

        for mode, template in templates.items():
            print(f"\n📋 {mode.upper()} template:")
            found_examples = []

            for example in test_examples:
                if example in template:
                    found_examples.append(example)

            if found_examples:
                print(f"   ✅ Found examples: {', '.join(found_examples)}")
            else:
                print(f"   ❌ No scientific name examples found")

    except Exception as e:
        print(f"❌ Error testing examples: {e}")

if __name__ == "__main__":
    print("🚀 Starting Prompt Template Verification")
    print()

    try:
        verify_prompt_templates()
        check_environment_variables()
        test_template_examples()

        print("\n" + "=" * 60)
        print("🏁 Verification completed!")

    except KeyboardInterrupt:
        print("\n⏹️  Verification interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
