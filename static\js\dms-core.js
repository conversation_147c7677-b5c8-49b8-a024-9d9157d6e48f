/**
 * DMS Core JavaScript Utilities
 * 
 * Consolidated JavaScript utilities for the Document Management System.
 * This file eliminates duplication across script.js, admin.js, and utilities.js
 * by providing a centralized set of common functions.
 * 
 * Features:
 * - Unified API request handling with CSRF protection
 * - Centralized toast notification system
 * - Theme management utilities
 * - Common UI interaction helpers
 * - Error handling and logging
 * 
 * Version: 2.0.0
 * Dependencies: Bootstrap 5, Toastify.js
 */

(function(window) {
    'use strict';

    // Core DMS utilities namespace
    const DMSCore = {
        // Configuration
        config: {
            toastDuration: 3000,
            apiTimeout: 30000,
            retryAttempts: 3,
            debugMode: false
        },

        // Initialize the core utilities
        init: function() {
            this.initializeCSRFHandling();
            this.initializeTheme();
            this.initializeErrorHandling();
            this.log('DMS Core utilities initialized');
        },

        // Logging utility
        log: function(message, type = 'info') {
            if (this.config.debugMode || type === 'error') {
                console[type](message);
            }
        },

        /**
         * Enhanced API request function with CSRF protection and retry logic
         * @param {string} path - API endpoint path
         * @param {Object} options - Request options
         * @returns {Promise<Object>} Response object with ok, json, status
         */
        api: async function(path, options = {}) {
            // Get CSRF token from meta tag
            let csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

            // Set default options
            const defaultOptions = {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                },
                timeout: this.config.apiTimeout
            };

            // Merge options
            const opts = { ...defaultOptions, ...options };
            opts.headers = { ...defaultOptions.headers, ...options.headers };

            // Add CSRF token for state-changing requests
            if (['POST', 'PUT', 'DELETE', 'PATCH'].includes(opts.method.toUpperCase())) {
                if (csrfToken) {
                    opts.headers['X-CSRFToken'] = csrfToken;
                }
            }

            try {
                // Make the request
                const response = await fetch(path, opts);
                const json = await response.json().catch(() => ({}));

                // Handle CSRF token expiration
                if (response.status === 400 && json.error && json.error.includes('CSRF')) {
                    this.log('CSRF token expired, attempting to refresh...', 'warn');
                    
                    const refreshed = await this.refreshCSRFToken();
                    if (refreshed) {
                        // Retry with new token
                        const newToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
                        if (newToken) {
                            opts.headers['X-CSRFToken'] = newToken;
                        }
                        
                        // Retry the request
                        const retryResponse = await fetch(path, opts);
                        const retryJson = await retryResponse.json().catch(() => ({}));
                        
                        return {
                            ok: retryResponse.ok,
                            status: retryResponse.status,
                            json: retryJson,
                            response: retryResponse
                        };
                    }
                }

                return {
                    ok: response.ok,
                    status: response.status,
                    json: json,
                    response: response
                };

            } catch (error) {
                this.log(`API request failed: ${error.message}`, 'error');
                return {
                    ok: false,
                    status: 0,
                    json: { error: error.message },
                    response: null
                };
            }
        },

        /**
         * Refresh CSRF token
         * @returns {Promise<boolean>} Success status
         */
        refreshCSRFToken: async function() {
            try {
                const response = await fetch('/api/csrf-token', {
                    method: 'GET',
                    headers: { 'Content-Type': 'application/json' }
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.csrf_token) {
                        const metaTag = document.querySelector('meta[name="csrf-token"]');
                        if (metaTag) {
                            metaTag.setAttribute('content', data.csrf_token);
                            this.log('CSRF token refreshed successfully');
                            return true;
                        }
                    }
                }

                this.log('Failed to refresh CSRF token', 'error');
                return false;
            } catch (error) {
                this.log(`Error refreshing CSRF token: ${error.message}`, 'error');
                return false;
            }
        },

        /**
         * Initialize CSRF handling
         */
        initializeCSRFHandling: function() {
            // Set up global CSRF token handling
            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
            if (csrfToken) {
                // Set default headers for jQuery if available
                if (window.$ && $.ajaxSetup) {
                    $.ajaxSetup({
                        beforeSend: function(xhr, settings) {
                            if (!/^(GET|HEAD|OPTIONS|TRACE)$/i.test(settings.type) && !this.crossDomain) {
                                xhr.setRequestHeader("X-CSRFToken", csrfToken);
                            }
                        }
                    });
                }
            }
        },

        /**
         * Enhanced toast notification system
         * @param {string} message - Message to display
         * @param {string} type - Type of notification (success, error, warning, info)
         * @param {number} duration - Duration in milliseconds
         * @param {Object} options - Additional options
         */
        showToast: function(message, type = 'success', duration = null, options = {}) {
            // Use configured duration if not specified
            duration = duration || this.config.toastDuration;

            // Check if Toastify is available
            if (typeof Toastify !== 'function') {
                this.log('Toastify library not loaded, falling back to alert', 'warn');
                alert(message);
                return;
            }

            // Set background color based on type
            const colors = {
                success: '#00C851',
                error: '#ff4444',
                warning: '#ffbb33',
                info: '#33b5e5'
            };

            const backgroundColor = colors[type] || colors.info;

            // Default toast options
            const defaultOptions = {
                text: message,
                duration: duration,
                close: true,
                gravity: "top",
                position: "right",
                backgroundColor: backgroundColor,
                stopOnFocus: true,
                className: `toast-${type}`,
                onClick: function() {
                    // Optional click handler
                }
            };

            // Merge with custom options
            const toastOptions = { ...defaultOptions, ...options };

            // Show the toast
            Toastify(toastOptions).showToast();
        },

        /**
         * Show success toast
         * @param {string} message - Success message
         * @param {Object} options - Additional options
         */
        showSuccess: function(message, options = {}) {
            this.showToast(message, 'success', null, options);
        },

        /**
         * Show error toast
         * @param {string} message - Error message
         * @param {Object} options - Additional options
         */
        showError: function(message, options = {}) {
            this.showToast(message, 'error', null, options);
        },

        /**
         * Show warning toast
         * @param {string} message - Warning message
         * @param {Object} options - Additional options
         */
        showWarning: function(message, options = {}) {
            this.showToast(message, 'warning', null, options);
        },

        /**
         * Show info toast
         * @param {string} message - Info message
         * @param {Object} options - Additional options
         */
        showInfo: function(message, options = {}) {
            this.showToast(message, 'info', null, options);
        },

        /**
         * Theme management utilities
         */
        theme: {
            // Get current theme
            getCurrent: function() {
                return localStorage.getItem('theme') || 'light';
            },

            // Set theme
            set: function(theme) {
                localStorage.setItem('theme', theme);
                document.documentElement.classList.toggle('dark-mode', theme === 'dark');
                document.documentElement.classList.toggle('dark', theme === 'dark');
                
                // Update theme toggle icons
                DMSCore.updateThemeIcon();
                
                // Dispatch theme change event
                window.dispatchEvent(new CustomEvent('themeChanged', { detail: { theme } }));
            },

            // Toggle theme
            toggle: function() {
                const currentTheme = this.getCurrent();
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                this.set(newTheme);
                return newTheme;
            }
        },

        /**
         * Initialize theme on page load
         */
        initializeTheme: function() {
            const savedTheme = this.theme.getCurrent();
            this.theme.set(savedTheme);
        },

        /**
         * Update theme toggle icon
         */
        updateThemeIcon: function() {
            const themeIcon = document.getElementById('theme-icon');
            if (!themeIcon) return;

            const isDarkMode = this.theme.getCurrent() === 'dark';

            if (themeIcon.tagName === 'I') {
                // Font Awesome icon
                themeIcon.className = themeIcon.className.replace(/fa-(sun|moon)/, '');
                themeIcon.classList.add(isDarkMode ? 'fa-sun' : 'fa-moon');
            } else {
                // Emoji or text
                themeIcon.textContent = isDarkMode ? '☀️' : '🌙';
            }
        },

        /**
         * Initialize global error handling
         */
        initializeErrorHandling: function() {
            // Global error handler
            window.addEventListener('error', (event) => {
                this.log(`Global error: ${event.error?.message || event.message}`, 'error');
            });

            // Unhandled promise rejection handler
            window.addEventListener('unhandledrejection', (event) => {
                this.log(`Unhandled promise rejection: ${event.reason}`, 'error');
            });
        },

        /**
         * Utility functions
         */
        utils: {
            // Debounce function
            debounce: function(func, wait, immediate) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        timeout = null;
                        if (!immediate) func(...args);
                    };
                    const callNow = immediate && !timeout;
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                    if (callNow) func(...args);
                };
            },

            // Throttle function
            throttle: function(func, limit) {
                let inThrottle;
                return function(...args) {
                    if (!inThrottle) {
                        func.apply(this, args);
                        inThrottle = true;
                        setTimeout(() => inThrottle = false, limit);
                    }
                };
            },

            // Format file size
            formatFileSize: function(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            },

            // Format date
            formatDate: function(date, options = {}) {
                const defaultOptions = {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                };
                return new Date(date).toLocaleDateString('en-US', { ...defaultOptions, ...options });
            }
        }
    };

    // Make DMSCore globally available
    window.DMSCore = DMSCore;

    // Backward compatibility aliases
    window.api = DMSCore.api.bind(DMSCore);
    window.showToast = DMSCore.showToast.bind(DMSCore);
    window.refreshCSRFToken = DMSCore.refreshCSRFToken.bind(DMSCore);
    window.toggleTheme = DMSCore.theme.toggle.bind(DMSCore.theme);

    // Auto-initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => DMSCore.init());
    } else {
        DMSCore.init();
    }

})(window);
