"""
File Service

Handles all file-related business logic including:
- File upload processing
- File validation and duplicate checking
- Category management
- File metadata extraction
- Vector data management
- File deletion and cleanup

This service extracts file processing business logic from route handlers
to improve testability and maintainability.
"""

import os
import logging
import shutil
from werkzeug.utils import secure_filename
import utils
import db_utils
from db_embed import embed_file_db_first, scrape_url_db_first
from get_vector_db import get_vector_db
from utils import delete_file, check_duplicate_pdf
from services import ServiceError, FileProcessingError
from services.cache_service import get_cache_service, cached

# Configure logging
logger = logging.getLogger(__name__)

class FileService:
    """Service class for file processing business logic."""
    
    def __init__(self):
        """Initialize the file service."""
        self.initialized = False
        self.temp_folder = os.getenv("TEMP_FOLDER", "./_temp")
        self.chroma_path = os.getenv("CHROMA_PATH", "./chroma")
    
    def initialize(self):
        """Initialize the service with required dependencies."""
        try:
            # Ensure directories exist
            os.makedirs(self.temp_folder, exist_ok=True)
            os.makedirs(self.chroma_path, exist_ok=True)
            
            self.initialized = True
            logger.info("File service initialized successfully")
            return True
        except Exception as e:
            logger.error(f"Error initializing file service: {str(e)}")
            return False
    
    def process_file_upload(self, file, category, source_url=None, vision_settings=None):
        """
        Process a file upload with embedding and vision analysis.
        
        Args:
            file: Uploaded file object
            category: Document category
            source_url: Optional source URL
            vision_settings: Dictionary with vision processing settings
            
        Returns:
            Tuple of (success, message, file_info)
        """
        try:
            if not file or not file.filename:
                return False, "No file provided", None
            
            if not category:
                return False, "Category is required", None
            
            # Extract vision settings
            vision_settings = vision_settings or {}
            use_vision = vision_settings.get('use_vision', False)
            filter_sensitivity = vision_settings.get('filter_sensitivity', 'medium')
            max_images = vision_settings.get('max_images', 30)
            force_update = vision_settings.get('force_update', False)
            
            # Log processing settings
            if use_vision:
                logger.info(f"Processing file with vision analysis enabled. "
                           f"Sensitivity: {filter_sensitivity}, Max images: {max_images}")
            else:
                logger.info("Processing file with vision analysis disabled")
            
            # Process the file using database-first approach
            success, message = embed_file_db_first(
                file,
                category,
                source_url=source_url,
                use_vision=use_vision,
                filter_sensitivity=filter_sensitivity,
                max_images=max_images,
                force_update=force_update
            )
            
            if success:
                # Get file information
                file_info = self._get_file_metadata(file.filename, category)
                logger.info(f"File processed successfully: {file.filename}")
                return True, message, file_info
            else:
                logger.warning(f"File processing failed: {message}")
                return False, message, None
                
        except Exception as e:
            logger.error(f"Error processing file upload: {str(e)}")
            raise FileProcessingError(f"File upload processing failed: {str(e)}")
    
    def process_url_scraping(self, url, category, vision_settings=None):
        """
        Process URL scraping with embedding and vision analysis.
        
        Args:
            url: URL to scrape
            category: Document category
            vision_settings: Dictionary with vision processing settings
            
        Returns:
            Tuple of (success, message, url_info)
        """
        try:
            if not url:
                return False, "URL is required", None
            
            if not category:
                return False, "Category is required", None
            
            # Extract vision settings
            vision_settings = vision_settings or {}
            use_vision = vision_settings.get('use_vision', False)
            filter_sensitivity = vision_settings.get('filter_sensitivity', 'medium')
            max_images = vision_settings.get('max_images', 30)
            force_update = vision_settings.get('force_update', False)
            
            # Log processing settings
            logger.info(f"Processing URL: {url} with vision: {use_vision}")
            
            # Process the URL using database-first approach
            success, message = scrape_url_db_first(
                url,
                category,
                use_vision=use_vision,
                filter_sensitivity=filter_sensitivity,
                max_images=max_images,
                force_update=force_update
            )
            
            if success:
                # Get URL information
                url_info = {'url': url, 'category': category}
                logger.info(f"URL processed successfully: {url}")
                return True, message, url_info
            else:
                logger.warning(f"URL processing failed: {message}")
                return False, message, None
                
        except Exception as e:
            logger.error(f"Error processing URL scraping: {str(e)}")
            raise FileProcessingError(f"URL scraping processing failed: {str(e)}")
    
    def check_duplicate_file(self, filename, category):
        """
        Check if a file is a duplicate.
        
        Args:
            filename: Name of the file to check
            category: Document category
            
        Returns:
            Tuple of (is_duplicate, existing_file_info)
        """
        try:
            if not filename or not category:
                return False, None
            
            # Check for duplicate using existing utility
            is_duplicate, existing_file = check_duplicate_pdf(filename, category)
            
            logger.debug(f"Duplicate check for {filename} in {category}: {is_duplicate}")
            return is_duplicate, existing_file
            
        except Exception as e:
            logger.error(f"Error checking duplicate file: {str(e)}")
            return False, None
    
    @cached(ttl=300)  # Cache for 5 minutes
    def get_files_by_category(self):
        """
        Get all files organized by category.

        Returns:
            Dictionary with files organized by category
        """
        try:
            files_data = {}
            categories = sorted(utils.list_categories())
            
            # Query SQLite database for URLs
            scraped_pages = db_utils.get_scraped_pages()
            url_map = {(page['category'], page['filename']): page['url'] for page in scraped_pages}
            
            for category in categories:
                category_path = os.path.join(self.temp_folder, category)
                if os.path.isdir(category_path):
                    files = []
                    
                    # Check for files in both old and new directory structures
                    for filename in os.listdir(category_path):
                        file_path = os.path.join(category_path, filename)
                        
                        if os.path.isfile(file_path) and filename.lower().endswith('.pdf'):
                            # Get file info
                            file_info = self._get_detailed_file_info(category, filename, url_map)
                            files.append(file_info)
                        elif os.path.isdir(file_path):
                            # Check subdirectories for PDFs
                            pdf_file = f"{filename}.pdf"
                            pdf_path = os.path.join(category_path, pdf_file)
                            if os.path.exists(pdf_path):
                                file_info = self._get_detailed_file_info(category, pdf_file, url_map)
                                files.append(file_info)
                    
                    files_data[category] = sorted(files, key=lambda x: x['filename'])
            
            logger.debug(f"Retrieved files for {len(categories)} categories")
            return files_data
            
        except Exception as e:
            logger.error(f"Error getting files by category: {str(e)}")
            return {}
    
    def delete_file_and_resources(self, category, filename):
        """
        Delete a file and all its associated resources.
        
        Args:
            category: File category
            filename: File name to delete
            
        Returns:
            Tuple of (success, message)
        """
        try:
            # Use the existing delete_file function which handles all cleanup
            success, message = delete_file(category, filename)
            
            if success:
                logger.info(f"Successfully deleted file {category}/{filename}")
            else:
                logger.warning(f"Failed to delete file {category}/{filename}: {message}")
            
            return success, message
            
        except Exception as e:
            logger.error(f"Error deleting file {category}/{filename}: {str(e)}")
            return False, f"Error deleting file: {str(e)}"
    
    def get_vector_data(self, category, filename):
        """
        Get vector data for a specific file.
        
        Args:
            category: Document category
            filename: Document filename
            
        Returns:
            Vector data results or None
        """
        try:
            db = get_vector_db(category)
            
            # Get vector data for the specific file
            results = db.get(where={"source": filename})
            
            logger.debug(f"Retrieved vector data for {category}/{filename}")
            return results
            
        except Exception as e:
            logger.error(f"Error getting vector data for {category}/{filename}: {str(e)}")
            return None
    
    def _get_file_metadata(self, filename, category):
        """
        Get basic file metadata.
        
        Args:
            filename: File name
            category: File category
            
        Returns:
            Dictionary with file metadata
        """
        try:
            file_path = os.path.join(self.temp_folder, category, filename)
            
            if os.path.exists(file_path):
                file_stats = os.stat(file_path)
                return {
                    'filename': filename,
                    'category': category,
                    'size': file_stats.st_size,
                    'modified': file_stats.st_mtime,
                    'path': file_path
                }
            else:
                return {
                    'filename': filename,
                    'category': category,
                    'size': 0,
                    'modified': 0,
                    'path': None
                }
                
        except Exception as e:
            logger.error(f"Error getting file metadata: {str(e)}")
            return {
                'filename': filename,
                'category': category,
                'size': 0,
                'modified': 0,
                'path': None
            }
    
    def _get_detailed_file_info(self, category, filename, url_map):
        """
        Get detailed file information including thumbnails and source URLs.
        
        Args:
            category: File category
            filename: File name
            url_map: Mapping of files to source URLs
            
        Returns:
            Dictionary with detailed file information
        """
        try:
            file_path = os.path.join(self.temp_folder, category, filename)
            file_stats = os.stat(file_path)
            
            # Get source URL if available
            source_url = url_map.get((category, filename))
            
            # Check for thumbnail
            pdf_name = os.path.splitext(filename)[0]
            thumbnail_path = os.path.join(self.temp_folder, category, pdf_name, 'pdf_images', 'cover_image')
            thumbnail_url = None
            
            if os.path.exists(thumbnail_path):
                # Find the first image in the cover_image directory
                for img_file in os.listdir(thumbnail_path):
                    if img_file.lower().endswith(('.png', '.jpg', '.jpeg')):
                        thumbnail_url = f"/{category}/{pdf_name}/pdf_images/cover_image/{img_file}"
                        break
            
            return {
                'filename': filename,
                'category': category,
                'size': file_stats.st_size,
                'modified': file_stats.st_mtime,
                'source_url': source_url,
                'thumbnail_url': thumbnail_url
            }
            
        except Exception as e:
            logger.error(f"Error getting detailed file info for {category}/{filename}: {str(e)}")
            return {
                'filename': filename,
                'category': category,
                'size': 0,
                'modified': 0,
                'source_url': None,
                'thumbnail_url': None
            }

    def create_category(self, category_name):
        """
        Create a new document category.

        Args:
            category_name: Name of the category to create

        Returns:
            Tuple of (success, message)
        """
        try:
            if not category_name:
                return False, "Category name is required"

            # Secure the category name
            secure_category = secure_filename(category_name)

            # Create category directory in Chroma path
            category_path = os.path.join(self.chroma_path, secure_category)

            if os.path.exists(category_path):
                return False, "Category already exists"

            os.makedirs(category_path, exist_ok=True)

            # Also create temp folder structure
            temp_category_path = os.path.join(self.temp_folder, secure_category)
            os.makedirs(temp_category_path, exist_ok=True)

            logger.info(f"Created new category: {secure_category}")
            return True, f'Category "{secure_category}" created successfully'

        except Exception as e:
            logger.error(f"Error creating category: {str(e)}")
            return False, f"Error creating category: {str(e)}"

    def delete_category(self, category_name):
        """
        Delete a category and all its associated resources.

        Args:
            category_name: Name of the category to delete

        Returns:
            Tuple of (success, message)
        """
        try:
            if not category_name:
                return False, "Category name is required"

            # Delete vector database
            chroma_path = os.path.join(self.chroma_path, category_name)
            if os.path.exists(chroma_path):
                shutil.rmtree(chroma_path)

            # Delete temp files
            temp_path = os.path.join(self.temp_folder, category_name)
            if os.path.exists(temp_path):
                shutil.rmtree(temp_path)

            # Delete database entries
            db_utils.delete_category_data(category_name)

            logger.info(f"Successfully deleted category: {category_name}")
            return True, f'Category "{category_name}" deleted successfully'

        except Exception as e:
            logger.error(f"Error deleting category {category_name}: {str(e)}")
            return False, f"Error deleting category: {str(e)}"

    @cached(ttl=3600)  # Cache for 1 hour
    def list_categories(self):
        """
        Get list of all available categories.

        Returns:
            List of category names
        """
        try:
            categories = sorted(utils.list_categories())
            logger.debug(f"Retrieved {len(categories)} categories")
            return categories
        except Exception as e:
            logger.error(f"Error listing categories: {str(e)}")
            return []

    def get_category_stats(self, category_name):
        """
        Get statistics for a specific category.

        Args:
            category_name: Name of the category

        Returns:
            Dictionary with category statistics
        """
        try:
            category_path = os.path.join(self.temp_folder, category_name)

            if not os.path.exists(category_path):
                return {
                    'name': category_name,
                    'file_count': 0,
                    'total_size': 0,
                    'last_modified': None
                }

            file_count = 0
            total_size = 0
            last_modified = 0

            for filename in os.listdir(category_path):
                file_path = os.path.join(category_path, filename)
                if os.path.isfile(file_path) and filename.lower().endswith('.pdf'):
                    file_stats = os.stat(file_path)
                    file_count += 1
                    total_size += file_stats.st_size
                    last_modified = max(last_modified, file_stats.st_mtime)

            return {
                'name': category_name,
                'file_count': file_count,
                'total_size': total_size,
                'last_modified': last_modified
            }

        except Exception as e:
            logger.error(f"Error getting category stats for {category_name}: {str(e)}")
            return {
                'name': category_name,
                'file_count': 0,
                'total_size': 0,
                'last_modified': None
            }
