#!/usr/bin/env python3
"""
Configuration Management Validation Script

This script validates the enhanced centralized configuration management
implementation for Phase 2, Step 2.5.

Tests:
1. Configuration manager creation and initialization
2. Configuration validation mechanisms
3. Hot-reloading capabilities (simulated)
4. Configuration service functionality
5. Backup and restore operations
"""

import os
import sys
import json
import tempfile
import shutil
import logging
from datetime import datetime
from pathlib import Path

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_configuration_files():
    """Test that configuration management files exist."""
    print("🧪 Testing Configuration Management Files")
    print("=" * 45)
    
    required_files = [
        'config/config_manager.py',
        'services/config_service.py'
    ]
    
    success_count = 0
    total_count = len(required_files)
    
    for config_file in required_files:
        if os.path.exists(config_file):
            file_size = os.path.getsize(config_file)
            print(f"✅ {config_file} ({file_size} bytes)")
            success_count += 1
        else:
            print(f"❌ {config_file} - MISSING")
    
    print(f"\n📊 File Results: {success_count}/{total_count} files present")
    return success_count == total_count

def test_configuration_manager():
    """Test configuration manager functionality."""
    print("\n🧪 Testing Configuration Manager")
    print("=" * 35)
    
    try:
        # Import configuration manager
        from config.config_manager import (
            ConfigurationManager, 
            ApplicationConfig, 
            DatabaseConfig,
            SecurityConfig,
            AIModelConfig,
            ProcessingConfig
        )
        
        print("✅ Configuration manager imports successful")
        
        # Test configuration creation
        config = ApplicationConfig()
        print("✅ ApplicationConfig creation successful")
        
        # Test configuration sections
        sections = [
            ('database', config.database, DatabaseConfig),
            ('security', config.security, SecurityConfig),
            ('ai_models', config.ai_models, AIModelConfig),
            ('processing', config.processing, ProcessingConfig)
        ]
        
        section_count = 0
        for section_name, section_obj, section_class in sections:
            if isinstance(section_obj, section_class):
                print(f"✅ {section_name} section properly configured")
                section_count += 1
            else:
                print(f"❌ {section_name} section configuration error")
        
        # Test configuration validation
        validation_errors = config.validate()
        if isinstance(validation_errors, dict):
            print("✅ Configuration validation method working")
            if not validation_errors:
                print("✅ Default configuration is valid")
            else:
                print(f"⚠️  Default configuration has {len(validation_errors)} validation issues")
        else:
            print("❌ Configuration validation method error")
            return False
        
        # Test configuration serialization
        config_dict = config.to_dict()
        if isinstance(config_dict, dict) and 'database' in config_dict:
            print("✅ Configuration serialization working")
        else:
            print("❌ Configuration serialization error")
            return False
        
        # Test configuration deserialization
        restored_config = ApplicationConfig.from_dict(config_dict)
        if isinstance(restored_config, ApplicationConfig):
            print("✅ Configuration deserialization working")
        else:
            print("❌ Configuration deserialization error")
            return False
        
        print(f"\n📊 Configuration Manager: {section_count + 4}/8 tests passed")
        return section_count >= 3  # At least 3 sections working
        
    except Exception as e:
        print(f"❌ Error testing configuration manager: {str(e)}")
        return False

def test_configuration_validation():
    """Test configuration validation mechanisms."""
    print("\n🧪 Testing Configuration Validation")
    print("=" * 40)
    
    try:
        from config.config_manager import (
            ApplicationConfig, 
            ConfigValidationService,
            DatabaseConfig,
            SecurityConfig
        )
        
        # Test individual section validation
        db_config = DatabaseConfig()
        db_errors = db_config.validate()
        print(f"✅ Database validation: {len(db_errors)} errors found")
        
        security_config = SecurityConfig()
        security_errors = security_config.validate()
        print(f"✅ Security validation: {len(security_errors)} errors found")
        
        # Test validation service
        validation_service = ConfigValidationService()
        print("✅ ConfigValidationService created")
        
        # Test full configuration validation
        config = ApplicationConfig()
        full_errors = validation_service.validate_full_config(config)
        print(f"✅ Full configuration validation: {len(full_errors)} sections with errors")
        
        # Test invalid configuration
        invalid_config = ApplicationConfig()
        invalid_config.security.secret_key = ""  # Invalid empty secret key
        invalid_config.processing.chunk_size = -1  # Invalid negative chunk size
        
        invalid_errors = validation_service.validate_full_config(invalid_config)
        if len(invalid_errors) > 0:
            print("✅ Validation correctly identifies invalid configurations")
        else:
            print("⚠️  Validation may not be catching all invalid configurations")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing configuration validation: {str(e)}")
        return False

def test_configuration_service():
    """Test configuration service functionality."""
    print("\n🧪 Testing Configuration Service")
    print("=" * 35)
    
    try:
        # Create a temporary config file for testing
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_file:
            temp_config = {
                "database": {"db_path": "./test.db"},
                "security": {"secret_key": "test-key-123456"},
                "ai_models": {"llm_model": "test-model"},
                "processing": {"chunk_size": 500}
            }
            json.dump(temp_config, temp_file)
            temp_config_file = temp_file.name
        
        try:
            from services.config_service import ConfigService
            
            # Test service creation
            config_service = ConfigService()
            print("✅ ConfigService created successfully")
            
            # Test get current config
            current_config = config_service.get_current_config()
            if current_config.get('success'):
                print("✅ Get current configuration working")
            else:
                print("⚠️  Get current configuration has issues")
            
            # Test configuration validation
            validation_result = config_service.validate_config()
            if validation_result.get('success'):
                print("✅ Configuration validation service working")
            else:
                print("⚠️  Configuration validation service has issues")
            
            # Test configuration templates
            template_result = config_service.get_config_template("default")
            if template_result.get('success'):
                print("✅ Configuration templates working")
            else:
                print("⚠️  Configuration templates have issues")
            
            # Test backup functionality
            backup_result = config_service.backup_config("test_backup")
            if backup_result.get('success'):
                print("✅ Configuration backup working")
                
                # Test list backups
                list_result = config_service.list_backups()
                if list_result.get('success'):
                    print("✅ List backups working")
                else:
                    print("⚠️  List backups has issues")
            else:
                print("⚠️  Configuration backup has issues")
            
            # Test configuration status
            status_result = config_service.get_config_status()
            if status_result.get('success'):
                print("✅ Configuration status working")
            else:
                print("⚠️  Configuration status has issues")
            
            return True
            
        finally:
            # Clean up temporary file
            if os.path.exists(temp_config_file):
                os.unlink(temp_config_file)
        
    except Exception as e:
        print(f"❌ Error testing configuration service: {str(e)}")
        return False

def test_hot_reloading_simulation():
    """Test hot-reloading capabilities (simulated)."""
    print("\n🧪 Testing Hot-Reloading (Simulated)")
    print("=" * 40)
    
    try:
        from config.config_manager import ConfigurationManager
        
        # Create a temporary directory and config file
        temp_dir = tempfile.mkdtemp()
        temp_config_file = os.path.join(temp_dir, "test_config.json")
        
        # Initial configuration
        initial_config = {
            "database": {"db_path": "./initial.db"},
            "security": {"secret_key": "initial-key-123456"},
            "config_version": "2.0.0"
        }
        
        with open(temp_config_file, 'w') as f:
            json.dump(initial_config, f)
        
        try:
            # Create configuration manager with the temp file
            config_manager = ConfigurationManager(temp_config_file, auto_reload=False)
            print("✅ Configuration manager created with custom file")
            
            # Test initial load
            config = config_manager.get_config()
            if config.database.db_path == "./initial.db":
                print("✅ Initial configuration loaded correctly")
            else:
                print("⚠️  Initial configuration not loaded correctly")
            
            # Simulate configuration change
            updated_config = initial_config.copy()
            updated_config["database"]["db_path"] = "./updated.db"
            
            with open(temp_config_file, 'w') as f:
                json.dump(updated_config, f)
            
            # Test manual reload
            config_manager.load_configuration()
            updated_config_obj = config_manager.get_config()
            
            if updated_config_obj.database.db_path == "./updated.db":
                print("✅ Configuration reload working")
            else:
                print("⚠️  Configuration reload not working properly")
            
            # Test configuration update
            config_manager.update_config({
                "security": {"secret_key": "updated-key-789"}
            })
            
            final_config = config_manager.get_config()
            if "updated-key-789" in final_config.security.secret_key:
                print("✅ Configuration update working")
            else:
                print("⚠️  Configuration update not working properly")
            
            return True
            
        finally:
            # Clean up
            shutil.rmtree(temp_dir)
        
    except Exception as e:
        print(f"❌ Error testing hot-reloading: {str(e)}")
        return False

def calculate_step_2_5_progress():
    """Calculate Step 2.5 completion progress."""
    print("\n📈 Step 2.5 Progress Analysis")
    print("=" * 35)
    
    try:
        # Check completion criteria
        criteria = {
            'Configuration Files Created': test_configuration_files(),
            'Configuration Manager Working': test_configuration_manager(),
            'Validation Mechanisms': test_configuration_validation(),
            'Configuration Service': test_configuration_service(),
            'Hot-Reloading Capabilities': test_hot_reloading_simulation()
        }
        
        completed_criteria = sum(criteria.values())
        total_criteria = len(criteria)
        completion_percentage = (completed_criteria / total_criteria) * 100
        
        print(f"\n📊 Step 2.5 Completion Criteria:")
        for criterion, status in criteria.items():
            status_icon = "✅" if status else "❌"
            print(f"   {status_icon} {criterion}")
        
        print(f"\n📈 Step 2.5 Progress: {completion_percentage:.1f}% ({completed_criteria}/{total_criteria} criteria)")
        
        if completion_percentage >= 80:
            print("🎉 Step 2.5: Enhanced Configuration Management - COMPLETED")
            return True
        else:
            print("⚠️  Step 2.5: Enhanced Configuration Management - IN PROGRESS")
            return False
        
    except Exception as e:
        print(f"❌ Error calculating progress: {str(e)}")
        return False

def main():
    """Run all configuration management validation tests."""
    print("🚀 Enhanced Configuration Management Validation")
    print("=" * 60)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    tests = [
        ("Configuration Files", test_configuration_files),
        ("Configuration Manager", test_configuration_manager),
        ("Validation Mechanisms", test_configuration_validation),
        ("Configuration Service", test_configuration_service),
        ("Hot-Reloading", test_hot_reloading_simulation),
        ("Step 2.5 Progress", calculate_step_2_5_progress)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests >= (total_tests * 0.8):  # 80% pass rate
        print("🎉 Enhanced configuration management implementation successful!")
        print("\n✅ Phase 2 Step 2.5 (Enhanced Configuration Management) - COMPLETED")
    else:
        print("⚠️  Some tests failed. Configuration management needs refinement.")
    
    print(f"\nTest completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return passed_tests >= (total_tests * 0.8)

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
