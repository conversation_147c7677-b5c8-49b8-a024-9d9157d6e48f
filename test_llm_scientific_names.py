#!/usr/bin/env python3
"""
Test script for LLM-based scientific name formatting
Tests the updated prompt templates to ensure scientific names are properly formatted
"""

import requests
import time

def test_scientific_name_formatting():
    """Test the LLM-based scientific name formatting with various test cases"""

    base_url = "http://localhost:5000"

    # Test cases with scientific names
    test_cases = [
        {
            "query": "What information do you have about Pterocarpus indicus?",
            "category": "Forest Research",
            "expected_patterns": ["*Pterocarpus indicus*"]
        },
        {
            "query": "Tell me about bamboo species like Bambusa blumeana and Dendrocalamus asper",
            "category": "Forest Research",
            "expected_patterns": ["*Bambusa blumeana*", "*Dendrocalamus asper*"]
        },
        {
            "query": "What are the characteristics of Homo sapiens compared to other species?",
            "category": "General",
            "expected_patterns": ["*Homo sapiens*"]
        },
        {
            "query": "Research on Escherichia coli (Migula 1895) and its applications",
            "category": "General",
            "expected_patterns": ["*Escherichia coli*", "(<PERSON><PERSON><PERSON> 1895)"]
        },
        {
            "query": "Information about Swietenia macrophylla King timber properties",
            "category": "Forest Research",
            "expected_patterns": ["*Swietenia macrophylla*", "King"]
        }
    ]

    print("🧪 Testing LLM-based Scientific Name Formatting")
    print("=" * 60)

    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 Test Case {i}: {test_case['query']}")
        print(f"📂 Category: {test_case['category']}")

        try:
            # Send query to the system
            response = requests.post(f"{base_url}/query", json={
                "query": test_case["query"],
                "category": test_case["category"],
                "anti_hallucination_mode": "balanced"
            }, timeout=30)

            if response.status_code == 200:
                result = response.json()
                answer = result.get("answer", "")
                followup_questions = result.get("followup_questions", [])

                print(f"✅ Response received")
                print(f"📄 Answer preview: {answer[:200]}...")

                # Check for expected scientific name patterns
                patterns_found = []
                patterns_missing = []

                for pattern in test_case["expected_patterns"]:
                    if pattern in answer:
                        patterns_found.append(pattern)
                    else:
                        patterns_missing.append(pattern)

                if patterns_found:
                    print(f"✅ Scientific names found: {', '.join(patterns_found)}")

                if patterns_missing:
                    print(f"❌ Expected patterns missing: {', '.join(patterns_missing)}")

                # Check follow-up questions for scientific names
                scientific_names_in_followup = []
                for question in followup_questions:
                    if "*" in question and "*" in question:
                        scientific_names_in_followup.append(question)

                if scientific_names_in_followup:
                    print(f"✅ Scientific names in follow-up questions: {len(scientific_names_in_followup)}")
                    for q in scientific_names_in_followup:
                        print(f"   📋 {q}")

            else:
                print(f"❌ Request failed with status {response.status_code}")
                print(f"   Error: {response.text}")

        except requests.exceptions.RequestException as e:
            print(f"❌ Request error: {e}")
        except Exception as e:
            print(f"❌ Unexpected error: {e}")

        # Small delay between requests
        time.sleep(1)

    print("\n" + "=" * 60)
    print("🏁 Testing completed!")

def test_anti_hallucination_modes():
    """Test scientific name formatting across different anti-hallucination modes"""

    base_url = "http://localhost:5000"

    test_query = "What do you know about Pterocarpus indicus and Gmelina arborea?"
    modes = ["strict", "balanced", "off"]

    print("\n🔬 Testing Scientific Name Formatting Across Anti-Hallucination Modes")
    print("=" * 70)

    for mode in modes:
        print(f"\n🎯 Testing mode: {mode.upper()}")

        try:
            response = requests.post(f"{base_url}/query", json={
                "query": test_query,
                "category": "Forest Research",
                "anti_hallucination_mode": mode
            }, timeout=30)

            if response.status_code == 200:
                result = response.json()
                answer = result.get("answer", "")

                # Extract scientific names in italics
                scientific_names = []

                # Extract text between asterisks
                parts = answer.split("*")
                for i in range(1, len(parts), 2):
                    if i < len(parts):
                        scientific_names.append(f"*{parts[i]}*")

                print(f"✅ Response received")
                print(f"🔬 Scientific names found: {len(scientific_names)//2 if len(scientific_names) > 0 else 0}")
                for name in scientific_names:
                    print(f"   📋 {name}")

            else:
                print(f"❌ Request failed with status {response.status_code}")

        except Exception as e:
            print(f"❌ Error: {e}")

        time.sleep(1)

if __name__ == "__main__":
    print("🚀 Starting LLM-based Scientific Name Formatting Tests")
    print("📡 Make sure the application is running on http://localhost:5000")
    print()

    try:
        # Test basic functionality
        test_scientific_name_formatting()

        # Test across different modes
        test_anti_hallucination_modes()

    except KeyboardInterrupt:
        print("\n⏹️  Testing interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
