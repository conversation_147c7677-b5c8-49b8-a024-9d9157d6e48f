"""
File Management Blueprint

Handles file upload, processing, and management routes including:
- File upload interface
- Category management
- File listing and deletion
- Vector data viewing
- Duplicate checking

This blueprint manages all file-related operations that were
previously in the main app.py file.
"""

import os
import logging
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from werkzeug.utils import secure_filename
import utils
import db_utils
from blueprints.auth import admin_required, function_permission_required
from services import get_service

# Configure logging
logger = logging.getLogger(__name__)

# Create blueprint
file_management_bp = Blueprint('file_management', __name__)

# Get file service instance
file_service = get_service('file')

# Configuration
TEMP_FOLDER = os.getenv("TEMP_FOLDER", "./_temp")
CHROMA_PATH = os.getenv("CHROMA_PATH", "./chroma")

@file_management_bp.route('/admin/upload', methods=['GET', 'POST'])
@admin_required
@function_permission_required('upload_content')
def upload_file():
    """
    File upload interface and processing.
    
    Handles both GET requests to display the upload form
    and POST requests to process file uploads and URL scraping.
    
    Returns:
        Rendered upload template or redirect after processing
    """
    if request.method == 'GET':
        try:
            categories = sorted(utils.list_categories())
            return render_template('upload.html', categories=categories)
        except Exception as e:
            logger.error(f"Error loading upload page: {str(e)}")
            flash('Error loading upload page. Please try again.', 'error')
            return render_template('upload.html', categories=[])
    
    elif request.method == 'POST':
        try:
            # Get form data
            category = request.form.get('category', '').strip()
            pdf_url = request.form.get('pdf_url', '').strip()

            # Validate category
            if not category:
                flash('Category is required.', 'error')
                return redirect(url_for('file_management.upload_file'))

            # Prepare vision settings
            vision_settings = {
                'use_vision': request.form.get('use_vision', 'false').lower() == 'true',
                'filter_sensitivity': request.form.get('filter_sensitivity', 'medium'),
                'max_images': int(request.form.get('max_images', 30)),
                'force_update': request.form.get('force_update', 'false').lower() == 'true'
            }

            # Handle file upload
            if 'file' in request.files:
                file = request.files['file']
                if file and file.filename:
                    success, message, _ = file_service.process_file_upload(
                        file, category, pdf_url, vision_settings
                    )
                    flash(message, 'success' if success else 'error')
                    return redirect(url_for('file_management.upload_file'))

            # Handle URL scraping
            if pdf_url:
                success, message, _ = file_service.process_url_scraping(
                    pdf_url, category, vision_settings
                )
                flash(message, 'success' if success else 'error')
                return redirect(url_for('file_management.upload_file'))

            flash('Please select a file or enter a URL.', 'error')
            return redirect(url_for('file_management.upload_file'))

        except Exception as e:
            logger.error(f"Error during upload: {str(e)}")
            flash('Error during upload. Please try again.', 'error')
            return redirect(url_for('file_management.upload_file'))

# Helper functions removed - business logic moved to file_service

@file_management_bp.route('/api/check_duplicate', methods=['POST'])
def check_duplicate():
    """
    API endpoint to check if a file is a duplicate before uploading.
    
    Checks for duplicate files based on filename and content hash
    to prevent unnecessary processing.
    
    Returns:
        JSON response with duplicate check results
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        filename = data.get('filename')
        category = data.get('category')
        
        if not filename or not category:
            return jsonify({'error': 'Filename and category are required'}), 400
        
        # Check for duplicate using file service
        is_duplicate, existing_file = file_service.check_duplicate_file(filename, category)
        
        return jsonify({
            'is_duplicate': is_duplicate,
            'existing_file': existing_file,
            'message': 'Duplicate check completed'
        }), 200
        
    except Exception as e:
        logger.error(f"Error checking duplicate: {str(e)}")
        return jsonify({'error': f'Error checking duplicate: {str(e)}'}), 500

@file_management_bp.route('/admin/files')
@admin_required
@function_permission_required('manage_files')
def list_files():
    """
    Display list of uploaded files organized by category.
    
    Shows files with their metadata, thumbnails, and management options.
    
    Returns:
        Rendered files listing template
    """
    try:
        # Use file service to get files by category
        files_data = file_service.get_files_by_category()
        return render_template('files.html', files_data=files_data)
        
    except Exception as e:
        logger.error(f"Error listing files: {str(e)}")
        flash('Error loading files. Please try again.', 'error')
        return render_template('files.html', files_data={})

# Helper function removed - business logic moved to file_service

@file_management_bp.route('/admin/delete/<category>/<filename>', methods=['POST', 'DELETE'])
@admin_required
def delete_file_route(category, filename):
    """
    Delete a file and all its associated resources.

    Removes the file, images, tables, vector embeddings, and database entries.

    Args:
        category: File category
        filename: File name to delete

    Returns:
        JSON response with deletion status
    """
    try:
        # Use file service for deletion
        success, message = file_service.delete_file_and_resources(category, filename)

        if success:
            logger.info(f"Successfully deleted file {category}/{filename}")
            return jsonify({
                'success': True,
                'message': message
            }), 200
        else:
            logger.warning(f"Failed to delete file {category}/{filename}: {message}")
            return jsonify({
                'success': False,
                'error': message
            }), 500

    except Exception as e:
        logger.error(f"Error deleting file {category}/{filename}: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Error deleting file: {str(e)}'
        }), 500

@file_management_bp.route('/admin/vector_data/<category>/<filename>')
def view_vector_data(category, filename):
    """
    View vector data for a specific file.

    Displays the vector embeddings and metadata for a document.

    Args:
        category: Document category
        filename: Document filename

    Returns:
        Rendered vector data template
    """
    try:
        # Use file service to get vector data
        results = file_service.get_vector_data(category, filename)

        return render_template('vector_data.html',
                             vector_data=results,
                             category=category,
                             filename=filename)

    except Exception as e:
        logger.error(f"Error viewing vector data for {category}/{filename}: {str(e)}")
        flash('Error loading vector data. Please try again.', 'error')
        return render_template('vector_data.html',
                             vector_data=None,
                             category=category,
                             filename=filename)

@file_management_bp.route('/admin/categories', methods=['GET', 'POST', 'DELETE'])
def list_categories_route():
    """
    Handle category management operations.

    Supports GET (list), POST (create), and DELETE (remove) operations
    for document categories.

    Returns:
        JSON response for API calls or rendered template for GET
    """
    if request.method == 'POST':
        try:
            # Handle category creation
            new_category = request.form.get('new_category', '').strip()

            if not new_category:
                return jsonify({'error': 'Category name is required'}), 400

            # Create category directory
            category_path = os.path.join(CHROMA_PATH, new_category)

            if os.path.exists(category_path):
                return jsonify({'error': 'Category already exists'}), 400

            os.makedirs(category_path, exist_ok=True)

            # Also create temp folder structure
            temp_category_path = os.path.join(TEMP_FOLDER, new_category)
            os.makedirs(temp_category_path, exist_ok=True)

            logger.info(f"Created new category: {new_category}")
            return jsonify({
                'success': True,
                'message': f'Category "{new_category}" created successfully'
            }), 200

        except Exception as e:
            logger.error(f"Error creating category: {str(e)}")
            return jsonify({'error': f'Error creating category: {str(e)}'}), 500

    elif request.method == 'DELETE':
        try:
            # Handle category deletion
            category = request.args.get('category', '').strip()

            if not category:
                return jsonify({'error': 'Category name is required'}), 400

            # Delete category and all associated resources
            success, message = _delete_category_completely(category)

            if success:
                return jsonify({
                    'success': True,
                    'message': message
                }), 200
            else:
                return jsonify({'error': message}), 500

        except Exception as e:
            logger.error(f"Error deleting category: {str(e)}")
            return jsonify({'error': f'Error deleting category: {str(e)}'}), 500

    else:  # GET request
        try:
            categories = sorted(utils.list_categories())

            # Return JSON for API requests
            if request.headers.get('Accept') == 'application/json':
                return jsonify({'categories': categories}), 200

            # Return template for web requests
            return render_template('categories.html', categories=categories)

        except Exception as e:
            logger.error(f"Error listing categories: {str(e)}")
            return jsonify({'error': 'Error listing categories'}), 500

def _delete_category_completely(category):
    """
    Delete a category and all its associated resources.

    Args:
        category: Category name to delete

    Returns:
        Tuple of (success, message)
    """
    try:
        import shutil

        # Delete vector database
        chroma_path = os.path.join(CHROMA_PATH, category)
        if os.path.exists(chroma_path):
            shutil.rmtree(chroma_path)

        # Delete temp files
        temp_path = os.path.join(TEMP_FOLDER, category)
        if os.path.exists(temp_path):
            shutil.rmtree(temp_path)

        # Delete database entries
        db_utils.delete_category_data(category)

        logger.info(f"Successfully deleted category: {category}")
        return True, f'Category "{category}" deleted successfully'

    except Exception as e:
        logger.error(f"Error deleting category {category}: {str(e)}")
        return False, f'Error deleting category: {str(e)}'
