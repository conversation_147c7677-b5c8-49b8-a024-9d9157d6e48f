<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Session Conversation</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked@4.0.0/marked.min.js"></script>
    <style>
        /* Style for cover images */
        .cover-image {
            order: -1; /* Display cover images first */
            border: 2px solid #3b82f6; /* Blue border to highlight cover images */
            border-radius: 0.5rem;
            padding: 0.25rem;
            background-color: #eff6ff; /* Light blue background */
        }

        /* Make the grid container use flexbox ordering */
        .document-thumbnails-container {
            display: flex;
            flex-wrap: wrap;
            gap: 0.75rem;
        }
    </style>
    <script>
        // Configure marked.js to properly handle links and other markdown elements
        marked.setOptions({
            breaks: true,  // Add line breaks
            gfm: true,     // Enable GitHub Flavored Markdown
            headerIds: false,
            mangle: false,
            sanitize: false, // Allow HTML in markdown
            renderer: (function() {
                const renderer = new marked.Renderer();

                // Override the link renderer to add target="_blank" and other attributes
                renderer.link = function(href, title, text) {
                    const link = marked.Renderer.prototype.link.call(this, href, title, text);
                    return link.replace('<a ', '<a target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline" ');
                };

                return renderer;
            })()
        });
    </script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex justify-between items-center mb-6">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">Session Conversation</h1>
                    {% if session %}
                        <div class="mt-2">
                            <span class="px-3 py-1 bg-blue-100 text-blue-800 text-xs font-semibold rounded-full">
                                Device ID: {{ (session.device_fingerprint[:8] + '...') if session.device_fingerprint else 'Unknown' }}
                            </span>
                            <span class="px-3 py-1 bg-indigo-100 text-indigo-800 text-xs font-semibold rounded-full">
                                User: {{ session.client_name if session.client_name else 'Anonymous' }}
                            </span>
                            <span class="ml-2 px-3 py-1 bg-purple-100 text-purple-800 text-xs font-semibold rounded-full">
                                Category: {{ session.category }}
                            </span>
                            <span class="ml-2 px-3 py-1 bg-green-100 text-green-800 text-xs font-semibold rounded-full">
                                Messages: {{ session.message_count }}
                            </span>
                            <span class="ml-2 text-sm text-gray-500">
                                Started: {{ session.start_time }}
                            </span>
                            {% if session.end_time %}
                                <span class="ml-2 text-sm text-gray-500">
                                    Ended: {{ session.end_time }}
                                </span>
                            {% else %}
                                <span class="ml-2 px-3 py-1 bg-yellow-100 text-yellow-800 text-xs font-semibold rounded-full">
                                    Active
                                </span>
                            {% endif %}
                        </div>
                    {% endif %}
                </div>
                <div class="flex space-x-4">
                    <a href="{{ url_for('view_sessions') }}" class="text-blue-600 hover:underline">&larr; Back to Sessions</a>
                    {% if session and not session.end_time %}
                        <form action="{{ url_for('close_session', session_id=session.session_id) }}" method="post" class="inline">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                            <button type="submit" class="text-red-600 hover:underline">Close Session</button>
                        </form>
                    {% endif %}
                </div>
            </div>

            {% if history %}
                <div class="space-y-8">
                    {% for entry in history %}
                        {% set entry_index = loop.index %}
                        <div class="bg-gray-50 rounded-lg p-6 border border-gray-200">
                            <div class="flex justify-between items-start mb-4">
                                <div>
                                    <span class="ml-2 text-sm text-gray-500">
                                        {{ entry.timestamp }}
                                    </span>
                                </div>
                            </div>

                            <div class="mb-4">
                                <h3 class="text-md font-medium text-gray-700 mb-2">
                                    {% if entry.device_fingerprint or entry.client_name %}
                                    Question from
                                    {% if entry.client_name %}
                                    <span class="text-indigo-700">{{ entry.client_name }}</span>
                                    {% endif %}
                                    {% if entry.device_fingerprint and entry.client_name %}
                                    <span class="text-gray-500">(</span>
                                    {% endif %}
                                    {% if entry.device_fingerprint %}
                                    <span class="text-blue-700">Device {{ entry.device_fingerprint[:8] }}...</span>
                                    {% endif %}
                                    {% if entry.device_fingerprint and entry.client_name %}
                                    <span class="text-gray-500">)</span>
                                    {% endif %}:
                                    {% else %}
                                    Question:
                                    {% endif %}
                                </h3>
                                <div class="bg-blue-50 p-3 rounded-lg border border-blue-100">
                                    {{ entry.question }}
                                </div>
                            </div>

                            {% if entry.document_thumbnails %}
                                <div class="mb-4">
                                    <h3 class="text-md font-medium text-gray-700 mb-2">Document Thumbnails:</h3>
                                    <div class="document-thumbnails-container">
                                        {% for img in entry.document_thumbnails %}
                                            {% if img.startswith('<div class="image-container">') or img.startswith('<div class="image-container cover-image">') %}
                                                <div id="doc-thumbnail-{{ entry_index }}-{{ loop.index }}"></div>
                                                <script>
                                                    document.getElementById('doc-thumbnail-{{ entry_index }}-{{ loop.index }}').innerHTML = `{{ img | safe }}`;
                                                </script>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                </div>
                            {% endif %}

                            {% if entry.url_images %}
                                <div class="mb-4">
                                    <h3 class="text-md font-medium text-gray-700 mb-2">URL Images:</h3>
                                    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                                        {% for img in entry.url_images %}
                                            {% if img.startswith('<div class="image-container">') %}
                                                <div id="url-image-{{ entry_index }}-{{ loop.index }}"></div>
                                                <script>
                                                    document.getElementById('url-image-{{ entry_index }}-{{ loop.index }}').innerHTML = `{{ img | safe }}`;
                                                </script>
                                            {% elif img is string and img.startswith('http') and "'" not in img and "{" not in img and "}" not in img %}
                                                <div class="image-container">
                                                    <a href="{{ img }}" target="_blank" rel="noopener noreferrer">
                                                        <img src="{{ img }}" alt="Image from URL" class="w-full h-48 object-cover rounded-lg shadow-md hover:opacity-90 transition-opacity" />
                                                    </a>
                                                    <div class="text-xs text-gray-500 mt-1">Image from URL</div>
                                                </div>
                                            {% elif img is string %}
                                                <div class="text-sm">
                                                    <a href="{{ img }}" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline">{{ img }}</a>
                                                </div>
                                            {% else %}
                                                <div class="text-sm text-red-500">Invalid image URL format</div>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                </div>
                            {% endif %}

                            <div class="mb-4">
                                <h3 class="text-md font-medium text-gray-700 mb-2">Answer:</h3>
                                <div class="bg-white p-4 rounded-lg border border-gray-200 prose max-w-none">
                                    {% if entry.client_name %}
                                    <p class="font-medium text-green-700">Hello {{ entry.client_name }},</p>
                                    {% elif entry.device_fingerprint %}
                                    <p class="font-medium text-green-700">Hello User,</p>
                                    {% endif %}

                                    <!-- Render the answer content with markdown formatting -->
                                    <div class="markdown-content">
                                        {{ entry.answer|markdown|safe }}
                                    </div>

                                    <!-- Fallback if the answer is empty -->
                                    {% if not entry.answer or entry.answer.strip() == '' %}
                                    <p class="text-red-500">No answer content available</p>
                                    {% endif %}

                                    <!-- Follow-up Questions Section (if available in metadata) -->
                                    {% if entry.metadata and entry.metadata.get('followup_questions') %}
                                    <div class="mt-4 border-t border-gray-200 pt-3">
                                        <p class="text-sm font-medium text-gray-700 mb-2">Follow-up Questions:</p>
                                        <div class="flex flex-wrap gap-2">
                                            {% for question in entry.metadata.get('followup_questions') %}
                                                <div class="px-3 py-2 bg-blue-50 text-blue-700 text-sm rounded-lg border border-blue-200">
                                                    {{ question }}
                                                </div>
                                            {% endfor %}
                                        </div>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>

                            {% if entry.sources %}
                                <div class="mb-4">
                                    <h3 class="text-md font-medium text-gray-700 mb-2">Sources:</h3>
                                    <div class="bg-white p-3 rounded-lg border border-gray-200">
                                        <div class="space-y-3">
                                            {% for source in entry.sources %}
                                                {% if source is not string %}
                                                    <div class="bg-gray-50 rounded-lg p-3 border border-gray-200">
                                                        <div class="flex flex-wrap items-center gap-2">
                                                            <div class="font-medium">
                                                                {% if source.original_url %}
                                                                    <a href="{{ source.original_url }}" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline font-medium">{{ source.display_name }}</a>
                                                                {% elif source.file_path %}
                                                                    <a href="{{ source.file_path }}" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline font-medium">{{ source.display_name }}</a>
                                                                {% else %}
                                                                    {{ source.display_name }}
                                                                {% endif %}
                                                            </div>
                                                            {% if source.type %}
                                                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">{{ source.type }}</span>
                                                            {% endif %}
                                                            {% if source.page %}
                                                                <span class="text-sm text-gray-500">Page: {{ source.page }}</span>
                                                            {% endif %}
                                                        </div>
                                                    </div>
                                                {% else %}
                                                    <div class="bg-gray-50 rounded-lg p-3 border border-gray-200">
                                                        <div class="text-sm text-gray-700">{{ source }}</div>
                                                    </div>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                            {% endif %}

                            {% if entry.pdf_images %}
                                <div class="mb-4">
                                    <h3 class="text-md font-medium text-gray-700 mb-2">Images from PDFs:</h3>
                                    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                                        {% for img in entry.pdf_images %}
                                            {% if img.startswith('<div class="image-container">') %}
                                                <div id="pdf-image-{{ entry_index }}-{{ loop.index }}"></div>
                                                <script>
                                                    document.getElementById('pdf-image-{{ entry_index }}-{{ loop.index }}').innerHTML = `{{ img | safe }}`;
                                                </script>
                                            {% elif img is string and (img.startswith('http') or img.startswith('/')) and "'" not in img and "{" not in img and "}" not in img %}
                                                <div class="image-container">
                                                    <a href="{{ img }}" target="_blank" rel="noopener noreferrer">
                                                        <img src="{{ img }}" alt="Image from PDF" class="w-full h-48 object-cover rounded-lg shadow-md hover:opacity-90 transition-opacity" />
                                                    </a>
                                                    <div class="text-xs text-gray-500 mt-1">{{ img.split('/')[-1] if '/' in img else img }}</div>
                                                </div>
                                            {% elif img is string %}
                                                <div class="text-sm">
                                                    <a href="{{ img }}" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline">{{ img.split('/')[-1] if '/' in img else img }}</a>
                                                </div>
                                            {% else %}
                                                <div class="text-sm text-red-500">Invalid image URL format</div>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                </div>
                            {% elif entry.images %}
                                <!-- Fallback for backward compatibility -->
                                <div class="mb-4">
                                    <h3 class="text-md font-medium text-gray-700 mb-2">Images:</h3>
                                    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                                        {% for img in entry.images %}
                                            {% if img.startswith('<div class="image-container">') %}
                                                <div id="fallback-image-{{ entry_index }}-{{ loop.index }}"></div>
                                                <script>
                                                    document.getElementById('fallback-image-{{ entry_index }}-{{ loop.index }}').innerHTML = `{{ img | safe }}`;
                                                </script>
                                            {% elif img is string and (img.startswith('http') or img.startswith('/')) and "'" not in img and "{" not in img and "}" not in img %}
                                                <div class="image-container">
                                                    <a href="{{ img }}" target="_blank" rel="noopener noreferrer">
                                                        <img src="{{ img }}" alt="Image" class="w-full h-48 object-cover rounded-lg shadow-md hover:opacity-90 transition-opacity" />
                                                    </a>
                                                    <div class="text-xs text-gray-500 mt-1">{{ img.split('/')[-1] if '/' in img else img }}</div>
                                                </div>
                                            {% elif img is string %}
                                                <div class="text-sm">
                                                    <a href="{{ img }}" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline">{{ img.split('/')[-1] if '/' in img else img }}</a>
                                                </div>
                                            {% else %}
                                                <div class="text-sm text-red-500">Invalid image URL format</div>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                </div>
                            {% endif %}
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-yellow-700">
                                No messages found for this session.
                            </p>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</body>
</html>
