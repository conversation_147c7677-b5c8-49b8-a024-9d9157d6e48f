"""
Core Blueprint

Handles basic application routes including:
- Index/home page
- Documentation
- Static file serving
- Basic utility routes

This blueprint contains routes that don't require authentication
and provide core application functionality.
"""

import os
import logging
from flask import Blueprint, render_template, send_file, abort, current_app
from werkzeug.utils import secure_filename

# Configure logging
logger = logging.getLogger(__name__)

# Create blueprint
core_bp = Blueprint('core', __name__)

# Get configuration values
TEMP_FOLDER = os.getenv("TEMP_FOLDER", "./_temp")
CHROMA_PATH = os.getenv("CHROMA_PATH", "./chroma")

@core_bp.route('/')
def index():
    """
    Main index page showing available categories.
    
    Returns:
        Rendered index.html template with available categories
    """
    try:
        categories = sorted(os.listdir(CHROMA_PATH)) if os.path.exists(CHROMA_PATH) else []
        return render_template('index.html', categories=categories)
    except Exception as e:
        logger.error(f"Error loading index page: {str(e)}")
        return render_template('index.html', categories=[])

@core_bp.route('/docs')
def documentation():
    """
    Simple documentation page.
    
    Returns:
        Rendered documentation.html template
    """
    return render_template('documentation.html')

@core_bp.route('/<category>/<filename>')
def serve_file(category, filename):
    """
    Serve PDF files directly from the app.
    
    Args:
        category: Document category
        filename: File name to serve
        
    Returns:
        File response or 404 if not found
    """
    try:
        # Secure the filename to prevent directory traversal
        secure_category = secure_filename(category)
        secure_file = secure_filename(filename)
        
        # Construct file path
        file_path = os.path.join(TEMP_FOLDER, secure_category, secure_file)
        
        # Check if file exists
        if not os.path.exists(file_path):
            logger.warning(f"File not found: {file_path}")
            abort(404)
            
        # Serve the file
        return send_file(file_path, as_attachment=False)
        
    except Exception as e:
        logger.error(f"Error serving file {category}/{filename}: {str(e)}")
        abort(500)

@core_bp.route('/<category>/<pdf_name>/pdf_images/<filename>')
def serve_pdf_image_new(category, pdf_name, filename):
    """
    Serve extracted images from PDFs using the new directory structure.
    
    Args:
        category: Document category
        pdf_name: PDF name (without extension)
        filename: Image filename
        
    Returns:
        Image file response or 404 if not found
    """
    try:
        # Secure all path components
        secure_category = secure_filename(category)
        secure_pdf_name = secure_filename(pdf_name)
        secure_filename_param = secure_filename(filename)
        
        # Construct file path using new directory structure
        file_path = os.path.join(TEMP_FOLDER, secure_category, secure_pdf_name, 'pdf_images', secure_filename_param)
        
        # Check if file exists
        if not os.path.exists(file_path):
            logger.warning(f"PDF image not found: {file_path}")
            abort(404)
            
        # Serve the image
        return send_file(file_path, as_attachment=False)
        
    except Exception as e:
        logger.error(f"Error serving PDF image {category}/{pdf_name}/{filename}: {str(e)}")
        abort(500)

@core_bp.route('/<category>/<pdf_name>/pdf_images/cover_image/<filename>')
def serve_pdf_cover_image(category, pdf_name, filename):
    """
    Serve cover images/thumbnails from PDFs using the new directory structure.
    
    Args:
        category: Document category
        pdf_name: PDF name (without extension)
        filename: Cover image filename
        
    Returns:
        Cover image file response or 404 if not found
    """
    try:
        # Secure all path components
        secure_category = secure_filename(category)
        secure_pdf_name = secure_filename(pdf_name)
        secure_filename_param = secure_filename(filename)
        
        # Construct file path for cover image
        file_path = os.path.join(TEMP_FOLDER, secure_category, secure_pdf_name, 'pdf_images', 'cover_image', secure_filename_param)
        
        # Check if file exists
        if not os.path.exists(file_path):
            logger.warning(f"PDF cover image not found: {file_path}")
            abort(404)
            
        # Serve the cover image
        return send_file(file_path, as_attachment=False)
        
    except Exception as e:
        logger.error(f"Error serving PDF cover image {category}/{pdf_name}/{filename}: {str(e)}")
        abort(500)

@core_bp.route('/<category>/<pdf_name>/pdf_tables/<filename>')
def serve_pdf_table_new(category, pdf_name, filename):
    """
    Serve extracted tables from PDFs using the new directory structure.
    
    Args:
        category: Document category
        pdf_name: PDF name (without extension)
        filename: Table filename
        
    Returns:
        Table file response or 404 if not found
    """
    try:
        # Secure all path components
        secure_category = secure_filename(category)
        secure_pdf_name = secure_filename(pdf_name)
        secure_filename_param = secure_filename(filename)
        
        # Construct file path for table
        file_path = os.path.join(TEMP_FOLDER, secure_category, secure_pdf_name, 'pdf_tables', secure_filename_param)
        
        # Check if file exists
        if not os.path.exists(file_path):
            logger.warning(f"PDF table not found: {file_path}")
            abort(404)
            
        # Serve the table file
        return send_file(file_path, as_attachment=False)
        
    except Exception as e:
        logger.error(f"Error serving PDF table {category}/{pdf_name}/{filename}: {str(e)}")
        abort(500)

# Legacy routes for backward compatibility
@core_bp.route('/pdf_images/<category>/<filename>')
def serve_pdf_image(category, filename):
    """
    Serve extracted images from PDFs using the old directory structure.
    Legacy route for backward compatibility.
    
    Args:
        category: Document category
        filename: Image filename
        
    Returns:
        Image file response or 404 if not found
    """
    try:
        # Secure path components
        secure_category = secure_filename(category)
        secure_filename_param = secure_filename(filename)
        
        # Try new structure first
        pdf_name = secure_filename_param.split('_')[0] if '_' in secure_filename_param else secure_filename_param.split('.')[0]
        new_path = os.path.join(TEMP_FOLDER, secure_category, pdf_name, 'pdf_images', secure_filename_param)
        
        if os.path.exists(new_path):
            return send_file(new_path, as_attachment=False)
        
        # Fall back to old structure
        old_path = os.path.join(TEMP_FOLDER, 'pdf_images', secure_category, secure_filename_param)
        
        if os.path.exists(old_path):
            return send_file(old_path, as_attachment=False)
            
        logger.warning(f"PDF image not found in either structure: {secure_filename_param}")
        abort(404)
        
    except Exception as e:
        logger.error(f"Error serving legacy PDF image {category}/{filename}: {str(e)}")
        abort(500)

@core_bp.route('/pdf_tables/<category>/<filename>')
def serve_pdf_table(category, filename):
    """
    Serve extracted tables from PDFs using the old directory structure.
    Legacy route for backward compatibility.
    
    Args:
        category: Document category
        filename: Table filename
        
    Returns:
        Table file response or 404 if not found
    """
    try:
        # Secure path components
        secure_category = secure_filename(category)
        secure_filename_param = secure_filename(filename)
        
        # Try new structure first
        pdf_name = secure_filename_param.split('_')[0] if '_' in secure_filename_param else secure_filename_param.split('.')[0]
        new_path = os.path.join(TEMP_FOLDER, secure_category, pdf_name, 'pdf_tables', secure_filename_param)
        
        if os.path.exists(new_path):
            return send_file(new_path, as_attachment=False)
        
        # Fall back to old structure
        old_path = os.path.join(TEMP_FOLDER, 'pdf_tables', secure_category, secure_filename_param)
        
        if os.path.exists(old_path):
            return send_file(old_path, as_attachment=False)
            
        logger.warning(f"PDF table not found in either structure: {secure_filename_param}")
        abort(404)
        
    except Exception as e:
        logger.error(f"Error serving legacy PDF table {category}/{filename}: {str(e)}")
        abort(500)
