<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Management System - Technical Presentation</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        :root {
            /* ERDB Brand Colors */
            --primary-dark-green: #378C47;
            --secondary-dark-blue: #0267B6;
            --light-green: #5BA85B;
            --light-blue: #3CA6D6;
            --orange: #FFBD5C;
            --orange-dark: #FC762B;
            --red: #C12323;
            --dark: #000000;
            --light: #f8f9fa;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--primary-dark-green), var(--secondary-dark-blue));
            color: white;
            overflow-x: hidden;
        }

        .presentation-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .slide {
            display: none;
            flex: 1;
            padding: 2rem;
            animation: slideIn 0.5s ease-in-out;
        }

        .slide.active {
            display: flex;
            flex-direction: column;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateX(50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        .slide-header {
            text-align: center;
            margin-bottom: 2rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 1rem;
            backdrop-filter: blur(10px);
        }

        .slide-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .navigation {
            position: fixed;
            bottom: 2rem;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 1rem;
            z-index: 1000;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }

        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .slide-counter {
            position: fixed;
            top: 2rem;
            right: 2rem;
            background: rgba(255, 255, 255, 0.2);
            padding: 0.5rem 1rem;
            border-radius: 1rem;
            backdrop-filter: blur(10px);
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 1rem;
            padding: 2rem;
            text-align: center;
            transition: transform 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .feature-card:hover {
            transform: translateY(-10px);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: var(--light-green);
        }

        .architecture-diagram {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 1rem;
            padding: 2rem;
            margin: 2rem 0;
            backdrop-filter: blur(10px);
        }

        .component-layer {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin: 1rem 0;
            flex-wrap: wrap;
        }

        .component-box {
            background: var(--light-green);
            color: white;
            padding: 1rem 2rem;
            border-radius: 0.5rem;
            font-weight: 600;
            min-width: 150px;
            text-align: center;
        }

        .component-box.secondary { background: var(--secondary-dark-blue); }
        .component-box.info { background: var(--light-blue); }
        .component-box.warning { background: var(--orange); }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 1rem;
            padding: 2rem;
            text-align: center;
            backdrop-filter: blur(10px);
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            color: var(--light-green);
        }

        .code-demo {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 1rem;
            padding: 2rem;
            margin: 2rem 0;
            font-family: 'Courier New', monospace;
            backdrop-filter: blur(10px);
        }

        .timeline {
            position: relative;
            margin: 2rem 0;
        }

        .timeline-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 1rem;
            padding: 1.5rem;
            margin: 1rem 0;
            backdrop-filter: blur(10px);
        }

        .progress-indicator {
            position: fixed;
            top: 0;
            left: 0;
            height: 4px;
            background: var(--light-green);
            transition: width 0.3s ease;
            z-index: 1001;
        }

        .theme-toggle {
            position: fixed;
            top: 2rem;
            left: 2rem;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 0.75rem;
            border-radius: 50%;
            cursor: pointer;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .theme-toggle:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        /* Dark mode adjustments */
        .dark-mode {
            background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
        }

        .dark-mode .feature-card,
        .dark-mode .stat-card,
        .dark-mode .timeline-item,
        .dark-mode .architecture-diagram {
            background: rgba(255, 255, 255, 0.05);
        }

        @media (max-width: 768px) {
            .slide {
                padding: 1rem;
            }

            .feature-grid {
                grid-template-columns: 1fr;
            }

            .component-layer {
                flex-direction: column;
                align-items: center;
            }

            .navigation {
                bottom: 1rem;
                gap: 0.5rem;
            }

            .nav-btn {
                padding: 0.5rem 1rem;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Progress Indicator -->
    <div class="progress-indicator" id="progressBar"></div>

    <!-- Theme Toggle -->
    <button class="theme-toggle" onclick="toggleTheme()" title="Toggle Theme">
        <i class="fas fa-moon" id="themeIcon"></i>
    </button>

    <!-- Slide Counter -->
    <div class="slide-counter">
        <span id="currentSlide">1</span> / <span id="totalSlides">12</span>
    </div>

    <div class="presentation-container">
        <!-- Slide 1: Title -->
        <div class="slide active">
            <div class="slide-content">
                <div class="text-center">
                    <h1 class="display-1 mb-4">
                        <i class="fas fa-file-alt me-3"></i>
                        Document Management System
                    </h1>
                    <h2 class="display-6 mb-4">Technical Presentation</h2>
                    <p class="lead">AI-Powered Knowledge Management Platform for ERDB</p>
                    <div class="mt-5">
                        <span class="badge bg-light text-dark me-2 p-3">Python Flask</span>
                        <span class="badge bg-light text-dark me-2 p-3">AI Integration</span>
                        <span class="badge bg-light text-dark me-2 p-3">Vector Search</span>
                        <span class="badge bg-light text-dark me-2 p-3">Geospatial Analysis</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 2: System Overview -->
        <div class="slide">
            <div class="slide-header">
                <h2><i class="fas fa-sitemap me-3"></i>System Overview</h2>
            </div>
            <div class="slide-content">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">3</div>
                        <h5>AI Models</h5>
                        <p>LLM, Vision, Embedding</p>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">4</div>
                        <h5>Databases</h5>
                        <p>SQLite + ChromaDB</p>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">25MB</div>
                        <h5>File Limit</h5>
                        <p>PDF Processing</p>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">3</div>
                        <h5>Admin Levels</h5>
                        <p>Philippine Geography</p>
                    </div>
                </div>
                <div class="text-center mt-4">
                    <p class="lead">Comprehensive AI-powered platform for intelligent document management, semantic search, and conversational AI with geographical intelligence for Philippine administrative levels.</p>
                </div>
            </div>
        </div>

        <!-- Slide 3: Architecture -->
        <div class="slide">
            <div class="slide-header">
                <h2><i class="fas fa-layer-group me-3"></i>Technical Architecture</h2>
            </div>
            <div class="slide-content">
                <div class="architecture-diagram">
                    <h4 class="text-center mb-4">Layered Architecture</h4>
                    <div class="component-layer">
                        <div class="component-box">Frontend Layer</div>
                        <div class="component-box secondary">Bootstrap 5 UI</div>
                        <div class="component-box info">Leaflet.js Maps</div>
                        <div class="component-box warning">Chart.js Analytics</div>
                    </div>
                    <div class="component-layer">
                        <div class="component-box">Flask Application</div>
                        <div class="component-box secondary">Authentication</div>
                        <div class="component-box info">Session Management</div>
                    </div>
                    <div class="component-layer">
                        <div class="component-box">AI Processing</div>
                        <div class="component-box secondary">Ollama Integration</div>
                        <div class="component-box info">Vision Processing</div>
                        <div class="component-box warning">NLP & Location</div>
                    </div>
                    <div class="component-layer">
                        <div class="component-box">Data Storage</div>
                        <div class="component-box secondary">SQLite DBs</div>
                        <div class="component-box info">ChromaDB Vectors</div>
                        <div class="component-box warning">File System</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 4: Core Features -->
        <div class="slide">
            <div class="slide-header">
                <h2><i class="fas fa-cogs me-3"></i>Core Features</h2>
            </div>
            <div class="slide-content">
                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-file-upload"></i>
                        </div>
                        <h5>Document Processing</h5>
                        <ul class="text-start">
                            <li>PDF text extraction (PyMuPDF)</li>
                            <li>Image analysis with vision models</li>
                            <li>Table detection and processing</li>
                            <li>Hierarchical storage by category</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <h5>Vector Search</h5>
                        <ul class="text-start">
                            <li>ChromaDB semantic search</li>
                            <li>mxbai-embed-large model</li>
                            <li>Configurable relevance thresholds</li>
                            <li>Category-based collections</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-comments"></i>
                        </div>
                        <h5>Conversational AI</h5>
                        <ul class="text-start">
                            <li>Llama 3.1 8B Instruct</li>
                            <li>Anti-hallucination modes</li>
                            <li>Citation support</li>
                            <li>Session management</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-map-marked-alt"></i>
                        </div>
                        <h5>Geographical Intelligence</h5>
                        <ul class="text-start">
                            <li>spaCy NER for location extraction</li>
                            <li>Philippine administrative levels</li>
                            <li>Leaflet.js mapping</li>
                            <li>Geocoding with OpenStreetMap</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 5: AI Integration -->
        <div class="slide">
            <div class="slide-header">
                <h2><i class="fas fa-brain me-3"></i>AI Model Integration</h2>
            </div>
            <div class="slide-content">
                <div class="row">
                    <div class="col-lg-4">
                        <div class="feature-card h-100">
                            <h5><i class="fas fa-robot me-2"></i>Language Models</h5>
                            <ul class="text-start">
                                <li><strong>Llama 3.1 8B:</strong> Primary LLM</li>
                                <li><strong>Gemma 3:</strong> Alternative models</li>
                                <li><strong>Parameters:</strong> Temperature 0.7, Context 4096</li>
                                <li><strong>Features:</strong> Automatic fallback</li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="feature-card h-100">
                            <h5><i class="fas fa-eye me-2"></i>Vision Models</h5>
                            <ul class="text-start">
                                <li><strong>Llama 3.2 Vision 11B:</strong> Primary</li>
                                <li><strong>Gemma 3 Multimodal:</strong> 4B/12B</li>
                                <li><strong>Features:</strong> Image filtering</li>
                                <li><strong>Processing:</strong> Contextual captions</li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="feature-card h-100">
                            <h5><i class="fas fa-vector-square me-2"></i>Embeddings</h5>
                            <ul class="text-start">
                                <li><strong>mxbai-embed-large:</strong> Default</li>
                                <li><strong>Chunks:</strong> 800 tokens</li>
                                <li><strong>Overlap:</strong> 250 tokens</li>
                                <li><strong>Storage:</strong> ChromaDB</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="code-demo mt-4">
                    <h6><i class="fas fa-code me-2"></i>Anti-Hallucination Configuration</h6>
                    <pre><code>{
  "hallucination_detection": {
    "threshold_strict": 0.6,
    "threshold_balanced": 0.4,
    "enable_detection": true
  }
}</code></pre>
                </div>
            </div>
        </div>

        <!-- Slide 6: Database Schema -->
        <div class="slide">
            <div class="slide-header">
                <h2><i class="fas fa-database me-3"></i>Database Architecture</h2>
            </div>
            <div class="slide-content">
                <div class="row">
                    <div class="col-lg-6">
                        <div class="feature-card h-100">
                            <h5>User Management DB</h5>
                            <ul class="text-start small">
                                <li><strong>users:</strong> Authentication & profiles</li>
                                <li><strong>permission_groups:</strong> RBAC system</li>
                                <li><strong>user_permissions:</strong> Individual overrides</li>
                                <li><strong>activity_logs:</strong> Audit trails</li>
                                <li><strong>user_sessions:</strong> Session tracking</li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="feature-card h-100">
                            <h5>Content Database</h5>
                            <ul class="text-start small">
                                <li><strong>pdf_documents:</strong> Document metadata</li>
                                <li><strong>source_urls:</strong> URL source management</li>
                                <li><strong>url_content:</strong> Scraped content</li>
                                <li><strong>cover_images:</strong> Document thumbnails</li>
                                <li><strong>extracted_locations:</strong> Geographic data</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-lg-6">
                        <div class="feature-card h-100">
                            <h5>Chat History DB</h5>
                            <ul class="text-start small">
                                <li><strong>chat_history:</strong> Conversations</li>
                                <li><strong>chat_analytics:</strong> Usage metrics</li>
                                <li><strong>geoip_analytics:</strong> Location tracking</li>
                                <li><strong>greeting_templates:</strong> Dynamic greetings</li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="feature-card h-100">
                            <h5>Vector Database</h5>
                            <ul class="text-start small">
                                <li><strong>ChromaDB:</strong> Vector storage</li>
                                <li><strong>Collections:</strong> Category-based</li>
                                <li><strong>Metadata:</strong> Document context</li>
                                <li><strong>Similarity:</strong> Semantic search</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 7: User Management -->
        <div class="slide">
            <div class="slide-header">
                <h2><i class="fas fa-users me-3"></i>User Management & Security</h2>
            </div>
            <div class="slide-content">
                <div class="row">
                    <div class="col-lg-6">
                        <div class="feature-card h-100">
                            <h5><i class="fas fa-shield-alt me-2"></i>Authentication</h5>
                            <ul class="text-start">
                                <li><strong>Registration:</strong> Email validation</li>
                                <li><strong>Password:</strong> Bcrypt hashing</li>
                                <li><strong>Sessions:</strong> Secure token management</li>
                                <li><strong>Protection:</strong> CSRF, rate limiting</li>
                                <li><strong>Tracking:</strong> Failed login attempts</li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="feature-card h-100">
                            <h5><i class="fas fa-users-cog me-2"></i>Role-Based Access</h5>
                            <ul class="text-start">
                                <li><strong>Admin:</strong> Full system access</li>
                                <li><strong>Editor:</strong> Content management</li>
                                <li><strong>Viewer:</strong> Read-only access</li>
                                <li><strong>Permissions:</strong> Function-based</li>
                                <li><strong>Inheritance:</strong> Hierarchical groups</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="architecture-diagram mt-4">
                    <h5 class="text-center">Permission Flow</h5>
                    <div class="component-layer">
                        <div class="component-box">User Login</div>
                        <div class="component-box secondary">Role Assignment</div>
                        <div class="component-box info">Permission Check</div>
                        <div class="component-box warning">Function Access</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 8: Geographical Features -->
        <div class="slide">
            <div class="slide-header">
                <h2><i class="fas fa-map-marked-alt me-3"></i>Geographical Intelligence</h2>
            </div>
            <div class="slide-content">
                <div class="row">
                    <div class="col-lg-6">
                        <div class="feature-card h-100">
                            <h5><i class="fas fa-search-location me-2"></i>Location Extraction</h5>
                            <ul class="text-start">
                                <li><strong>NER Engine:</strong> spaCy processing</li>
                                <li><strong>Entity Types:</strong> GPE, LOC, FAC</li>
                                <li><strong>Focus:</strong> Philippine locations</li>
                                <li><strong>Levels:</strong> Municipality, City, Barangay</li>
                                <li><strong>Confidence:</strong> Quality scoring</li>
                            </ul>
                            <div class="mt-3">
                                <span class="badge bg-primary me-2">Municipality</span>
                                <span class="badge bg-secondary me-2">City</span>
                                <span class="badge bg-success">Barangay</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="feature-card h-100">
                            <h5><i class="fas fa-globe me-2"></i>Mapping & Geocoding</h5>
                            <ul class="text-start">
                                <li><strong>Geocoding:</strong> Nominatim/OSM</li>
                                <li><strong>Mapping:</strong> Leaflet.js</li>
                                <li><strong>Features:</strong> Clustered markers</li>
                                <li><strong>Filtering:</strong> Category-based</li>
                                <li><strong>Caching:</strong> Performance optimization</li>
                            </ul>
                            <div class="mt-3 small">
                                <strong>Default Center:</strong> Los Baños (14.1648, 121.2413)
                            </div>
                        </div>
                    </div>
                </div>
                <div class="code-demo mt-4">
                    <h6><i class="fas fa-code me-2"></i>Location Processing Pipeline</h6>
                    <pre><code>PDF Upload → Text Extraction → NER Processing →
Location Classification → Geocoding → Database Storage →
Map Visualization</code></pre>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <div class="navigation">
        <button class="nav-btn" id="prevBtn" onclick="changeSlide(-1)">
            <i class="fas fa-chevron-left me-2"></i>Previous
        </button>
        <button class="nav-btn" id="nextBtn" onclick="changeSlide(1)">
            Next<i class="fas fa-chevron-right ms-2"></i>
        </button>
    </div>

    <script>
        let currentSlideIndex = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;

        document.getElementById('totalSlides').textContent = totalSlides;

        function showSlide(index) {
            slides.forEach(slide => slide.classList.remove('active'));
            slides[index].classList.add('active');

            document.getElementById('currentSlide').textContent = index + 1;
            document.getElementById('prevBtn').disabled = index === 0;
            document.getElementById('nextBtn').disabled = index === totalSlides - 1;

            // Update progress bar
            const progress = ((index + 1) / totalSlides) * 100;
            document.getElementById('progressBar').style.width = progress + '%';
        }

        function changeSlide(direction) {
            const newIndex = currentSlideIndex + direction;
            if (newIndex >= 0 && newIndex < totalSlides) {
                currentSlideIndex = newIndex;
                showSlide(currentSlideIndex);
            }
        }

        function toggleTheme() {
            document.body.classList.toggle('dark-mode');
            const icon = document.getElementById('themeIcon');
            if (document.body.classList.contains('dark-mode')) {
                icon.classList.remove('fa-moon');
                icon.classList.add('fa-sun');
            } else {
                icon.classList.remove('fa-sun');
                icon.classList.add('fa-moon');
            }
        }

        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') changeSlide(-1);
            if (e.key === 'ArrowRight') changeSlide(1);
            if (e.key === 'Home') {
                currentSlideIndex = 0;
                showSlide(currentSlideIndex);
            }
            if (e.key === 'End') {
                currentSlideIndex = totalSlides - 1;
                showSlide(currentSlideIndex);
            }
        });

        // Initialize
        showSlide(0);
    </script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
