#!/usr/bin/env python3
"""
Simple test to verify scientific name formatting is working
"""

import requests
import json

def test_scientific_names():
    """Test scientific name formatting with a simple query"""

    print("🧪 Testing Scientific Name Formatting")
    print("=" * 40)

    # Test query with scientific names
    test_query = "What do you know about Pterocarpus indicus?"

    try:
        # Send request to the system
        response = requests.post("http://localhost:5000/query", json={
            "query": test_query,
            "category": "Forest Research",
            "anti_hallucination_mode": "balanced"
        }, timeout=30)

        if response.status_code == 200:
            result = response.json()
            answer = result.get("answer", "")

            print(f"✅ Query successful!")
            print(f"📝 Query: {test_query}")
            print(f"📋 Response length: {len(answer)} characters")

            # Check for scientific name formatting
            if "*Pterocarpus indicus*" in answer:
                print("✅ Scientific name properly formatted with italics!")
                print(f"🔬 Found: *Pterocarpus indicus* in response")
            elif "Pterocarpus indicus" in answer:
                print("❌ Scientific name found but NOT formatted with italics")
                print(f"🔍 Found: Pterocarpus indicus (without italics)")
            else:
                print("❓ Scientific name not found in response")

            # Show a snippet of the response
            print(f"\n📄 Response snippet:")
            print(f"   {answer[:200]}...")

            # Check follow-up questions
            followup_questions = result.get("followup_questions", [])
            if followup_questions:
                print(f"\n❓ Follow-up questions ({len(followup_questions)}):")
                for i, question in enumerate(followup_questions, 1):
                    print(f"   {i}. {question}")
                    if "*" in question:
                        print(f"      ✅ Contains italics formatting")

        else:
            print(f"❌ Request failed with status {response.status_code}")
            print(f"   Response: {response.text}")

    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to server. Is it running on localhost:5000?")
    except Exception as e:
        print(f"❌ Error: {e}")

def check_template_loading():
    """Check if templates are being loaded correctly"""

    print("\n🔧 Checking Template Loading")
    print("=" * 30)

    try:
        # Load the default_models.json file
        with open('default_models.json', 'r', encoding='utf-8') as f:
            config = json.load(f)

        # Get templates from the correct nested structure
        query_params = config.get('query_parameters', {})
        templates = query_params.get('prompt_templates', {})

        # Check each template
        for mode in ['strict', 'balanced', 'off']:
            template = templates.get(mode, '')
            if 'SCIENTIFIC NAME FORMATTING' in template:
                print(f"✅ {mode.upper()} template has scientific name section")
                if '*Pterocarpus indicus*' in template:
                    print(f"   ✅ Contains example: *Pterocarpus indicus*")
            else:
                print(f"❌ {mode.upper()} template missing scientific name section")

    except Exception as e:
        print(f"❌ Error checking templates: {e}")

if __name__ == "__main__":
    check_template_loading()
    test_scientific_names()
