<!--
Reusable Sidebar Component

This component provides a centralized sidebar navigation structure that can be used
across admin templates to eliminate duplication. It includes permission-based
navigation items and responsive behavior.

Usage:
{% include 'components/sidebar.html' with context %}

Parameters:
- sidebar_brand_text: Brand text (default: "ERDB DMS")
- sidebar_brand_icon: Brand icon class (default: "fa fa-book-reader")
- show_user_info: boolean (default: true)
- compact_mode: boolean (default: false)
-->

{% set sidebar_brand_text = sidebar_brand_text or "ERDB DMS" %}
{% set sidebar_brand_icon = sidebar_brand_icon or "fa fa-book-reader" %}
{% set show_user_info = show_user_info if show_user_info is defined else true %}
{% set compact_mode = compact_mode if compact_mode is defined else false %}

<!-- Sidebar Start -->
<div class="sidebar pe-4 pb-3 {{ 'compact' if compact_mode else '' }}">
    <nav class="navbar bg-dark navbar-dark">
        <a href="{{ url_for('admin.admin_dashboard') }}" class="navbar-brand mx-4 mb-3">
            <h3 class="text-danger">
                <i class="{{ sidebar_brand_icon }}"></i>
                {% if not compact_mode %}
                <span class="brand-text">{{ sidebar_brand_text }}</span>
                {% endif %}
            </h3>
        </a>
        
        {% if show_user_info %}
        <div class="d-flex align-items-center ms-4 mb-4">
            <div class="position-relative">
                <div class="user-avatar rounded-circle me-3">
                    {% if current_user.is_authenticated %}
                        {{ current_user.username[0].upper() }}
                    {% else %}
                        ?
                    {% endif %}
                </div>
                <div class="bg-success rounded-circle border border-2 border-white position-absolute end-0 bottom-0 p-1"></div>
            </div>
            <div class="ms-3 {{ 'd-none' if compact_mode else '' }}">
                <h6 class="mb-0 text-light">
                    {% if current_user.is_authenticated %}
                        {{ current_user.username }}
                    {% else %}
                        Guest User
                    {% endif %}
                </h6>
                <span class="text-muted small">
                    {% if current_user.is_authenticated %}
                        {{ current_user.role.title() if current_user.role else 'User' }}
                    {% else %}
                        Not logged in
                    {% endif %}
                </span>
            </div>
        </div>
        {% endif %}
        
        <!-- Main Navigation -->
        <div class="navbar-nav w-100">
            <a href="{{ url_for('admin.admin_dashboard') }}" class="nav-item nav-link {% if request.endpoint == 'admin.admin_dashboard' %}active{% endif %}">
                <i class="fa fa-tachometer-alt me-2"></i>
                {% if not compact_mode %}<span>Dashboard</span>{% endif %}
            </a>

            {% if current_user.is_authenticated %}
            <!-- Content Management Section -->
            <div class="nav-item dropdown">
                <a href="#" class="nav-link dropdown-toggle {% if request.endpoint in ['file_management.upload_file', 'file_management.list_files', 'file_management.view_vector_data', 'admin.clean_urls'] %}active{% endif %}" role="button" aria-expanded="false">
                    <i class="fa fa-folder me-2"></i>
                    {% if not compact_mode %}<span>Content Management</span>{% endif %}
                </a>
                <div class="dropdown-menu bg-transparent border-0" style="display: none;">
                    {% if current_user.has_dashboard_permission('upload_content') %}
                    <a href="{{ url_for('file_management.upload_file') }}" class="dropdown-item {% if request.endpoint == 'file_management.upload_file' %}active{% endif %}">
                        <i class="fa fa-upload me-2"></i>Upload Content
                    </a>
                    {% endif %}
                    {% if current_user.has_dashboard_permission('manage_files') %}
                    <a href="{{ url_for('file_management.list_files') }}" class="dropdown-item {% if request.endpoint in ['file_management.list_files', 'file_management.view_vector_data'] %}active{% endif %}">
                        <i class="fa fa-file-alt me-2"></i>Manage Files
                    </a>
                    {% endif %}
                    {% if current_user.has_dashboard_permission('clean_urls') %}
                    <a href="{{ url_for('admin.clean_urls') }}" class="dropdown-item {% if request.endpoint == 'admin.clean_urls' %}active{% endif %}">
                        <i class="fa fa-broom me-2"></i>Clean URLs
                    </a>
                    {% endif %}
                </div>
            </div>

            <!-- Chat & Analytics Section -->
            <div class="nav-item dropdown">
                <a href="#" class="nav-link dropdown-toggle {% if request.endpoint in ['analytics.chat_history', 'analytics.view_sessions', 'analytics.view_session', 'analytics.analytics_dashboard'] %}active{% endif %}" role="button" aria-expanded="false">
                    <i class="fa fa-comments me-2"></i>
                    {% if not compact_mode %}<span>Chat & Analytics</span>{% endif %}
                </a>
                <div class="dropdown-menu bg-transparent border-0" style="display: none;">
                    {% if current_user.has_dashboard_permission('chat_history') %}
                    <a href="{{ url_for('analytics.chat_history') }}" class="dropdown-item {% if request.endpoint == 'analytics.chat_history' %}active{% endif %}">
                        <i class="fa fa-history me-2"></i>Chat History
                    </a>
                    {% endif %}
                    {% if current_user.has_dashboard_permission('chat_sessions') %}
                    <a href="{{ url_for('analytics.view_sessions') }}" class="dropdown-item {% if request.endpoint in ['analytics.view_sessions', 'analytics.view_session'] %}active{% endif %}">
                        <i class="fa fa-comment-dots me-2"></i>Chat Sessions
                    </a>
                    {% endif %}
                    {% if current_user.has_dashboard_permission('ai_analytics') %}
                    <a href="{{ url_for('analytics.analytics_dashboard') }}" class="dropdown-item {% if request.endpoint == 'analytics.analytics_dashboard' %}active{% endif %}">
                        <i class="fa fa-chart-bar me-2"></i>AI Analytics
                    </a>
                    <a href="{{ url_for('analytics.location_map') }}" class="dropdown-item {% if request.endpoint == 'analytics.location_map' %}active{% endif %}">
                        <i class="fa fa-map-marked-alt me-2"></i>Location Map
                    </a>
                    {% endif %}
                </div>
            </div>

            <!-- System Settings Section -->
            <div class="nav-item dropdown">
                <a href="#" class="nav-link dropdown-toggle {% if request.endpoint in ['admin.unified_config', 'admin.greeting_management'] %}active{% endif %}" role="button" aria-expanded="false">
                    <i class="fa fa-cogs me-2"></i>
                    {% if not compact_mode %}<span>System Settings</span>{% endif %}
                </a>
                <div class="dropdown-menu bg-transparent border-0" style="display: none;">
                    {% if current_user.has_dashboard_permission('model_settings') %}
                    <a href="{{ url_for('admin.unified_config') }}" class="dropdown-item {% if request.endpoint == 'admin.unified_config' %}active{% endif %}">
                        <i class="fa fa-sliders-h me-2"></i>Model Settings
                    </a>
                    {% endif %}
                    {% if current_user.has_dashboard_permission('greeting_management') %}
                    <a href="{{ url_for('admin.greeting_management') }}" class="dropdown-item {% if request.endpoint == 'admin.greeting_management' %}active{% endif %}">
                        <i class="fa fa-comments me-2"></i>Greeting Management
                    </a>
                    {% endif %}
                </div>
            </div>

            <!-- User Management Section -->
            <div class="nav-item dropdown">
                <a href="#" class="nav-link dropdown-toggle {% if request.endpoint in ['user.admin_users', 'user.admin_permission_groups', 'user.admin_permission_audit', 'user.admin_activity_logs', 'user.admin_edit_user', 'user.admin_new_user', 'user.admin_user_details'] %}active{% endif %}" role="button" aria-expanded="false">
                    <i class="fa fa-users me-2"></i>
                    {% if not compact_mode %}<span>User Management</span>{% endif %}
                </a>
                <div class="dropdown-menu bg-transparent border-0" style="display: none;">
                    <a href="{{ url_for('user.admin_users') }}" class="dropdown-item {% if request.endpoint in ['user.admin_users', 'user.admin_edit_user', 'user.admin_new_user', 'user.admin_user_details'] %}active{% endif %}">
                        <i class="fa fa-user-friends me-2"></i>Users
                    </a>
                    <a href="{{ url_for('user.admin_permission_groups') }}" class="dropdown-item {% if request.endpoint == 'user.admin_permission_groups' %}active{% endif %}">
                        <i class="fa fa-user-tag me-2"></i>Permission Groups
                    </a>
                    <a href="{{ url_for('user.admin_permission_audit') }}" class="dropdown-item {% if request.endpoint == 'user.admin_permission_audit' %}active{% endif %}">
                        <i class="fa fa-clipboard-list me-2"></i>Permission Audit
                    </a>
                    <a href="{{ url_for('user.admin_activity_logs') }}" class="dropdown-item {% if request.endpoint == 'user.admin_activity_logs' %}active{% endif %}">
                        <i class="fa fa-user-clock me-2"></i>Activity Logs
                    </a>
                </div>
            </div>
            {% endif %}

            <!-- Help & Support Section -->
            <div class="nav-item dropdown">
                <a href="#" class="nav-link dropdown-toggle" role="button" aria-expanded="false">
                    <i class="fa fa-question-circle me-2"></i>
                    {% if not compact_mode %}<span>Help & Support</span>{% endif %}
                </a>
                <div class="dropdown-menu bg-transparent border-0" style="display: none;">
                    <a href="{{ url_for('core.documentation') }}" target="_blank" class="dropdown-item">
                        <i class="fa fa-book me-2"></i>Documentation
                    </a>
                    <a href="{{ url_for('core.index') }}" target="_blank" class="dropdown-item">
                        <i class="fa fa-comments me-2"></i>Chat Interface
                    </a>
                    <a href="mailto:<EMAIL>" class="dropdown-item">
                        <i class="fa fa-life-ring me-2"></i>Contact Support
                    </a>
                    <a href="#" onclick="showSystemInfo()" class="dropdown-item">
                        <i class="fa fa-info-circle me-2"></i>System Info
                    </a>
                </div>
            </div>
        </div>
    </nav>
</div>
<!-- Sidebar End -->

<!-- Sidebar Styles -->
<style>
.sidebar.compact {
    width: 80px;
}

.sidebar.compact .brand-text,
.sidebar.compact .dropdown-menu {
    display: none;
}

.sidebar.compact .nav-link span {
    display: none;
}

.sidebar.compact .nav-link {
    text-align: center;
    padding: 12px 8px;
}

.sidebar.compact .nav-link i {
    margin-right: 0 !important;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 16px;
}

@media (max-width: 991.98px) {
    .sidebar {
        position: fixed;
        top: 0;
        left: -250px;
        width: 250px;
        height: 100vh;
        z-index: 999;
        transition: left 0.3s ease;
    }
    
    .sidebar.open {
        left: 0;
    }
}
</style>
