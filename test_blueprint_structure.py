#!/usr/bin/env python3
"""
Test script for Blueprint Structure Implementation

This script validates the blueprint structure and ensures all routes
are properly organized and accessible. It tests the Phase 2 implementation
of the systematic codebase improvements.

Tests:
1. Blueprint registration
2. Route accessibility
3. Import structure
4. Configuration management
5. Error handling
"""

import os
import sys
import importlib
import logging
from datetime import datetime

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_blueprint_imports():
    """Test that all blueprints can be imported successfully."""
    print("🧪 Testing Blueprint Imports")
    print("=" * 50)
    
    blueprints_to_test = [
        'blueprints',
        'blueprints.core',
        'blueprints.auth',
        'blueprints.admin',
        'blueprints.file_management',
        'blueprints.api',
        'blueprints.analytics'
    ]
    
    success_count = 0
    total_count = len(blueprints_to_test)
    
    for blueprint_name in blueprints_to_test:
        try:
            module = importlib.import_module(blueprint_name)
            print(f"✅ Successfully imported {blueprint_name}")
            success_count += 1
            
            # Check if blueprint object exists
            if hasattr(module, blueprint_name.split('.')[-1] + '_bp'):
                bp_name = blueprint_name.split('.')[-1] + '_bp'
                blueprint = getattr(module, bp_name)
                print(f"   📋 Blueprint object '{bp_name}' found")
            
        except ImportError as e:
            print(f"❌ Failed to import {blueprint_name}: {str(e)}")
        except Exception as e:
            print(f"⚠️  Error importing {blueprint_name}: {str(e)}")
    
    print(f"\n📊 Import Results: {success_count}/{total_count} blueprints imported successfully")
    return success_count == total_count

def test_session_utils():
    """Test session utilities module."""
    print("\n🧪 Testing Session Utils")
    print("=" * 30)
    
    try:
        import session_utils
        print("✅ Successfully imported session_utils")
        
        # Test function availability
        functions_to_test = [
            'get_or_create_session',
            'get_session_info',
            'update_session_data',
            'clear_session_data',
            'is_session_valid'
        ]
        
        for func_name in functions_to_test:
            if hasattr(session_utils, func_name):
                print(f"   ✅ Function '{func_name}' available")
            else:
                print(f"   ❌ Function '{func_name}' missing")
        
        return True
        
    except ImportError as e:
        print(f"❌ Failed to import session_utils: {str(e)}")
        return False

def test_blueprint_structure():
    """Test the overall blueprint structure."""
    print("\n🧪 Testing Blueprint Structure")
    print("=" * 35)
    
    try:
        from blueprints import ALL_BLUEPRINTS, register_blueprints
        print(f"✅ Found {len(ALL_BLUEPRINTS)} blueprints in structure")
        
        # Test each blueprint
        for i, blueprint in enumerate(ALL_BLUEPRINTS, 1):
            blueprint_name = blueprint.name
            url_prefix = getattr(blueprint, 'url_prefix', 'None')
            print(f"   {i}. {blueprint_name} (prefix: {url_prefix})")
        
        print("✅ Blueprint structure validation completed")
        return True
        
    except Exception as e:
        print(f"❌ Error testing blueprint structure: {str(e)}")
        return False

def test_app_creation():
    """Test Flask app creation with blueprints."""
    print("\n🧪 Testing App Creation")
    print("=" * 25)
    
    try:
        # Import Flask and create test app
        from flask import Flask
        from blueprints import register_blueprints
        
        # Create test app
        test_app = Flask(__name__)
        test_app.config['TESTING'] = True
        test_app.config['SECRET_KEY'] = 'test-secret-key'
        
        # Register blueprints
        register_blueprints(test_app)
        
        print("✅ Flask app created successfully")
        print("✅ Blueprints registered successfully")
        
        # Test app context
        with test_app.app_context():
            print("✅ App context working")
        
        # Get route count
        route_count = len(test_app.url_map._rules)
        print(f"📊 Total routes registered: {route_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating Flask app: {str(e)}")
        return False

def test_configuration_management():
    """Test configuration management."""
    print("\n🧪 Testing Configuration Management")
    print("=" * 40)
    
    try:
        # Test if we can import the new app structure
        import app_blueprints
        
        # Check if Config class exists
        if hasattr(app_blueprints, 'Config'):
            config = app_blueprints.Config
            print("✅ Config class found")
            
            # Test configuration attributes
            config_attrs = [
                'SECRET_KEY',
                'MAX_CONTENT_LENGTH',
                'WTF_CSRF_ENABLED',
                'TEMP_FOLDER',
                'CHROMA_PATH'
            ]
            
            for attr in config_attrs:
                if hasattr(config, attr):
                    value = getattr(config, attr)
                    print(f"   ✅ {attr}: {value}")
                else:
                    print(f"   ❌ {attr}: Missing")
        
        print("✅ Configuration management test completed")
        return True
        
    except Exception as e:
        print(f"❌ Error testing configuration: {str(e)}")
        return False

def calculate_complexity_reduction():
    """Calculate the complexity reduction achieved."""
    print("\n📊 Calculating Complexity Reduction")
    print("=" * 40)
    
    try:
        # Count lines in original app.py
        original_lines = 0
        if os.path.exists('app.py'):
            with open('app.py', 'r', encoding='utf-8') as f:
                original_lines = len(f.readlines())
        
        # Count lines in new structure
        new_structure_lines = 0
        
        # Count app_blueprints.py
        if os.path.exists('app_blueprints.py'):
            with open('app_blueprints.py', 'r', encoding='utf-8') as f:
                new_structure_lines += len(f.readlines())
        
        # Count blueprint files
        blueprint_files = [
            'blueprints/__init__.py',
            'blueprints/core.py',
            'blueprints/auth.py',
            'blueprints/admin.py',
            'blueprints/file_management.py',
            'blueprints/api.py',
            'blueprints/analytics.py'
        ]
        
        blueprint_lines = 0
        for blueprint_file in blueprint_files:
            if os.path.exists(blueprint_file):
                with open(blueprint_file, 'r', encoding='utf-8') as f:
                    lines = len(f.readlines())
                    blueprint_lines += lines
                    print(f"   📄 {blueprint_file}: {lines} lines")
        
        new_structure_lines += blueprint_lines
        
        # Count session_utils.py
        if os.path.exists('session_utils.py'):
            with open('session_utils.py', 'r', encoding='utf-8') as f:
                session_utils_lines = len(f.readlines())
                new_structure_lines += session_utils_lines
                print(f"   📄 session_utils.py: {session_utils_lines} lines")
        
        print(f"\n📊 Complexity Analysis:")
        print(f"   Original app.py: {original_lines} lines")
        print(f"   New structure total: {new_structure_lines} lines")
        print(f"   Main app file (app_blueprints.py): {new_structure_lines - blueprint_lines - (session_utils_lines if 'session_utils_lines' in locals() else 0)} lines")
        
        if original_lines > 0:
            main_app_lines = new_structure_lines - blueprint_lines - (session_utils_lines if 'session_utils_lines' in locals() else 0)
            reduction_percentage = ((original_lines - main_app_lines) / original_lines) * 100
            print(f"   Complexity reduction: {reduction_percentage:.1f}%")
            
            # Check if we met our target (50% reduction)
            if reduction_percentage >= 50:
                print("   ✅ Target complexity reduction achieved (≥50%)")
            else:
                print("   ⚠️  Target complexity reduction not yet achieved")
        
        return True
        
    except Exception as e:
        print(f"❌ Error calculating complexity reduction: {str(e)}")
        return False

def main():
    """Run all tests."""
    print("🚀 Blueprint Structure Validation")
    print("=" * 60)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    tests = [
        ("Blueprint Imports", test_blueprint_imports),
        ("Session Utils", test_session_utils),
        ("Blueprint Structure", test_blueprint_structure),
        ("App Creation", test_app_creation),
        ("Configuration Management", test_configuration_management),
        ("Complexity Reduction", calculate_complexity_reduction)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 All tests passed! Blueprint structure is working correctly.")
        print("\n✅ Phase 2 Step 2.1 (Blueprint Structure) - COMPLETED")
    else:
        print("⚠️  Some tests failed. Please review the issues above.")
    
    print(f"\nTest completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return passed_tests == total_tests

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
