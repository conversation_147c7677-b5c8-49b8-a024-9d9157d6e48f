accelerate==1.6.0
aiofiles==24.1.0
aiohappyeyeballs==2.6.1
aiohttp==3.11.18
aiosignal==1.3.2
aiosqlite==0.21.0
annotated-types==0.7.0
anyio==4.9.0
asgiref==3.8.1
attrs==25.3.0
audioop-lts==0.2.1
backoff==2.2.1
banks==2.1.2
bcrypt==4.3.0
beautifulsoup4==4.13.4
bitsandbytes==0.45.5
blinker==1.9.0
blis==1.3.0
bm25s==0.2.12
Brotli==1.1.0
build==1.2.2.post1
cachetools==5.5.2
camelot-py==1.0.0
catalogue==2.0.10
certifi==2025.4.26
cffi==1.17.1
chardet==5.2.0
charset-normalizer==3.4.2
chroma-hnswlib==0.7.6
chromadb==0.6.3
click==8.1.8
cloudpathlib==0.21.1
colorama==0.4.6
coloredlogs==15.0.1
colpali_engine==0.3.10
confection==0.1.5
contourpy==1.3.2
Crawl4AI==0.6.2
cryptography==44.0.3
cssselect==1.3.0
cycler==0.12.1
cymem==2.0.11
dataclasses-json==0.6.7
decorator==5.2.1
Deprecated==1.2.18
dirtyjson==1.0.8
distro==1.9.0
dnspython==2.7.0
durationpy==0.9
einops==0.8.1
email-validator==2.1.0.post1
en_core_web_sm @ https://github.com/explosion/spacy-models/releases/download/en_core_web_sm-3.8.0/en_core_web_sm-3.8.0-py3-none-any.whl#sha256=1932429db727d4bff3deed6b34cfc05df17794f4a52eeb26cf8928f7c1a0fb85
et_xmlfile==2.0.0
fake-http-header==0.3.5
fake-useragent==2.2.0
fastapi==0.115.12
ffmpy==0.5.0
filelock==3.18.0
filetype==1.2.0
Flask==3.1.0
Flask-Limiter==3.12
Flask-Login==0.6.3
Flask-WTF==1.2.2
flatbuffers==25.2.10
fonttools==4.58.0
frozenlist==1.6.0
fsspec==2025.3.2
future==1.0.0
geocoder==1.38.1
geographiclib==2.0
geoip2==5.1.0
geopy==2.4.1
google-ai-generativelanguage==0.6.15
google-api-core==2.25.0rc1
google-api-python-client==2.169.0
google-auth==2.40.1
google-auth-httplib2==0.2.0
google-generativeai==0.8.5
googleapis-common-protos==1.70.0
gradio==5.29.0
gradio_client==1.10.0
greenlet==3.2.1
griffe==1.7.3
groovy==0.1.2
grpcio==1.71.0
grpcio-status==1.71.0
gunicorn==23.0.0
h11==0.16.0
h2==4.2.0
hpack==4.1.0
httpcore==1.0.9
httplib2==0.22.0
httptools==0.6.4
httpx==0.28.1
httpx-sse==0.4.0
huggingface-hub==0.31.1
humanfriendly==10.0
humanize==4.12.3
hyperframe==6.1.0
idna==3.10
importlib_metadata==8.6.1
importlib_resources==6.5.2
itsdangerous==2.2.0
Jinja2==3.1.6
jiter==0.9.0
joblib==1.5.0
jpype1==1.5.2
jsonpatch==1.33
jsonpointer==3.0.0
jsonschema==4.23.0
jsonschema-specifications==2025.4.1
kiwisolver==1.4.8
kubernetes==32.0.1
langchain==0.3.25
langchain-chroma==0.2.3
langchain-community==0.3.23
langchain-core==0.3.59
langchain-ollama==0.3.2
langchain-text-splitters==0.3.8
langcodes==3.5.0
langsmith==0.3.42
language_data==1.3.0
limits==5.2.0
litellm==1.68.1
llama-cloud==0.1.19
llama-cloud-services==0.6.22
llama-index==0.12.35
llama-index-agent-openai==0.4.7
llama-index-cli==0.4.1
llama-index-core==0.12.35
llama-index-embeddings-ollama==0.6.0
llama-index-embeddings-openai==0.3.1
llama-index-indices-managed-llama-cloud==0.6.11
llama-index-llms-ollama==0.5.4
llama-index-llms-openai==0.3.38
llama-index-multi-modal-llms-openai==0.4.3
llama-index-program-openai==0.3.1
llama-index-question-gen-openai==0.3.0
llama-index-readers-file==0.4.7
llama-index-readers-llama-parse==0.4.0
llama-index-retrievers-bm25==0.5.2
llama-index-vector-stores-chroma==0.4.1
llama-parse==0.6.22
lxml==5.4.0
marisa-trie==1.2.1
Markdown==3.8
markdown-it-py==3.0.0
MarkupSafe==3.0.2
marshmallow==3.26.1
matplotlib==3.10.3
maxminddb==2.7.0
mdurl==0.1.2
mmh3==5.1.0
mpmath==1.3.0
multidict==6.4.3
murmurhash==1.0.13
mypy_extensions==1.1.0
nest-asyncio==1.6.0
networkx==3.4.2
nltk==3.9.1
numpy==2.2.5
oauthlib==3.2.2
ollama==0.4.8
onnxruntime==1.21.1
openai==1.78.1
opencv-python==4.11.0.86
opencv-python-headless==4.11.0.86
openpyxl==3.1.5
opentelemetry-api==1.32.1
opentelemetry-exporter-otlp-proto-common==1.32.1
opentelemetry-exporter-otlp-proto-grpc==1.32.1
opentelemetry-instrumentation==0.53b1
opentelemetry-instrumentation-asgi==0.53b1
opentelemetry-instrumentation-fastapi==0.53b1
opentelemetry-proto==1.32.1
opentelemetry-sdk==1.32.1
opentelemetry-semantic-conventions==0.53b1
opentelemetry-util-http==0.53b1
ordered-set==4.1.0
orjson==3.10.18
overrides==7.7.0
packaging==24.2
pandas==2.2.3
pdf2image==1.17.0
pdfminer.six==20250327
pdfplumber==0.11.6
peft==0.15.2
pillow==10.4.0
platformdirs==4.3.8
playwright==1.52.0
portalocker==2.10.1
posthog==4.0.1
preshed==3.0.9
propcache==0.3.1
proto-plus==1.26.1
protobuf==5.29.4
psutil==7.0.0
pyasn1==0.6.1
pyasn1_modules==0.4.2
pycparser==2.22
pydantic==2.11.4
pydantic-settings==2.9.1
pydantic_core==2.33.2
pydub==0.25.1
pyee==13.0.0
Pygments==2.19.1
PyMuPDF==1.25.5
pyOpenSSL==25.0.0
pyparsing==3.2.3
pypdf==5.4.0
pypdfium2==4.30.1
pyperclip==1.9.0
PyPika==0.48.9
pyproject_hooks==1.2.0
pyreadline3==3.5.4
PyStemmer==*******
pytesseract==0.3.13
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
python-multipart==0.0.20
pytz==2025.2
pywin32==310
PyYAML==6.0.2
qdrant-client==1.14.2
rank-bm25==0.2.2
ratelim==0.1.6
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
requests-oauthlib==2.0.0
requests-toolbelt==1.0.0
rich==13.9.4
rpds-py==0.24.0
rsa==4.9.1
ruff==0.11.9
safehttpx==0.1.6
safetensors==0.5.3
scikit-learn==1.6.1
scipy==1.15.3
semantic-version==2.10.0
sentence-transformers==4.1.0
setuptools==80.4.0
shellingham==1.5.4
six==1.17.0
smart-open==7.1.0
sniffio==1.3.1
snowballstemmer==2.2.0
soupsieve==2.7
spacy==3.8.7
spacy-legacy==3.0.12
spacy-loggers==1.0.5
SQLAlchemy==2.0.40
srsly==2.5.1
starlette==0.46.2
striprtf==0.0.26
sympy==1.13.1
tabula-py==2.10.0
tabulate==0.9.0
tenacity==9.1.2
tf-playwright-stealth==1.1.2
thinc==8.3.6
threadpoolctl==3.6.0
tiktoken==0.9.0
tokenizers==0.21.1
tomlkit==0.13.2
torch==2.6.0+cu118
tqdm==4.67.1
transformers==4.51.3
typer==0.15.3
typing-inspect==0.9.0
typing-inspection==0.4.0
typing_extensions==4.13.2
tzdata==2025.2
uritemplate==4.1.1
urllib3==2.4.0
uvicorn==0.34.2
wasabi==1.1.3
watchfiles==1.0.5
weasel==0.4.1
websocket-client==1.8.0
websockets==15.0.1
Werkzeug==3.1.3
wrapt==1.17.2
WTForms==3.2.1
xxhash==3.5.0
yarl==1.20.0
zipp==3.21.0
zstandard==0.23.0
