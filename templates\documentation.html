<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Management System - Technical Documentation</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Prism.js for syntax highlighting -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">

    <style>
        :root {
            /* ERDB Brand Colors */
            --primary-dark-green: #378C47;
            --secondary-dark-blue: #0267B6;
            --light-green: #5BA85B;
            --light-blue: #3CA6D6;
            --orange: #FFBD5C;
            --orange-dark: #FC762B;
            --red: #C12323;
            --dark: #000000;
            --light: #f8f9fa;
            --gray-100: #f8f9fa;
            --gray-200: #e9ecef;
            --gray-300: #dee2e6;
            --gray-600: #6c757d;
            --gray-700: #495057;
            --gray-800: #343a40;
            --gray-900: #212529;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--gray-800);
            background-color: var(--light);
        }

        .navbar {
            background: linear-gradient(135deg, var(--primary-dark-green), var(--secondary-dark-blue));
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }

        .sidebar {
            background-color: var(--gray-100);
            border-right: 1px solid var(--gray-300);
            height: calc(100vh - 76px);
            overflow-y: auto;
            position: sticky;
            top: 76px;
        }

        .sidebar .nav-link {
            color: var(--gray-700);
            padding: 0.75rem 1rem;
            border-radius: 0.375rem;
            margin: 0.25rem 0.5rem;
            transition: all 0.2s ease;
        }

        .sidebar .nav-link:hover {
            background-color: var(--light-green);
            color: white;
            transform: translateX(5px);
        }

        .sidebar .nav-link.active {
            background-color: var(--primary-dark-green);
            color: white;
        }

        .sidebar .nav-link i {
            width: 20px;
            margin-right: 0.5rem;
        }

        .content-area {
            padding: 2rem;
            background-color: white;
            min-height: calc(100vh - 76px);
        }

        .section-header {
            background: linear-gradient(135deg, var(--primary-dark-green), var(--light-green));
            color: white;
            padding: 1.5rem;
            border-radius: 0.5rem;
            margin-bottom: 2rem;
        }

        .section-header h1 {
            margin: 0;
            font-weight: 700;
        }

        .section-header p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
        }

        .card {
            border: none;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-radius: 0.75rem;
            margin-bottom: 1.5rem;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }

        .card-header {
            background: linear-gradient(135deg, var(--gray-100), var(--gray-200));
            border-bottom: 1px solid var(--gray-300);
            font-weight: 600;
            color: var(--gray-800);
        }

        .badge-primary {
            background-color: var(--primary-dark-green);
        }

        .badge-secondary {
            background-color: var(--secondary-dark-blue);
        }

        .badge-success {
            background-color: var(--light-green);
        }

        .badge-info {
            background-color: var(--light-blue);
        }

        .badge-warning {
            background-color: var(--orange);
            color: var(--gray-800);
        }

        .badge-danger {
            background-color: var(--red);
        }

        .btn-primary {
            background-color: var(--primary-dark-green);
            border-color: var(--primary-dark-green);
        }

        .btn-primary:hover {
            background-color: var(--light-green);
            border-color: var(--light-green);
        }

        .btn-secondary {
            background-color: var(--secondary-dark-blue);
            border-color: var(--secondary-dark-blue);
        }

        .btn-secondary:hover {
            background-color: var(--light-blue);
            border-color: var(--light-blue);
        }

        .table th {
            background-color: var(--gray-100);
            border-color: var(--gray-300);
            font-weight: 600;
            color: var(--gray-800);
        }

        .table td {
            border-color: var(--gray-200);
        }

        .code-block {
            background-color: var(--gray-900);
            color: var(--light);
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
            margin: 1rem 0;
        }

        .architecture-diagram {
            background: linear-gradient(135deg, var(--gray-100), white);
            border: 2px solid var(--gray-300);
            border-radius: 0.75rem;
            padding: 2rem;
            margin: 2rem 0;
            text-align: center;
        }

        .component-box {
            background-color: var(--primary-dark-green);
            color: white;
            padding: 1rem;
            border-radius: 0.5rem;
            margin: 0.5rem;
            display: inline-block;
            min-width: 150px;
            font-weight: 600;
        }

        .component-box.secondary {
            background-color: var(--secondary-dark-blue);
        }

        .component-box.success {
            background-color: var(--light-green);
        }

        .component-box.info {
            background-color: var(--light-blue);
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .feature-card {
            background: white;
            border: 1px solid var(--gray-300);
            border-radius: 0.75rem;
            padding: 1.5rem;
            transition: all 0.2s ease;
        }

        .feature-card:hover {
            border-color: var(--primary-dark-green);
            box-shadow: 0 4px 12px rgba(55, 140, 71, 0.15);
        }

        .feature-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, var(--primary-dark-green), var(--light-green));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }

        .tech-spec-table {
            background-color: var(--gray-50);
            border-radius: 0.5rem;
            overflow: hidden;
        }

        .tech-spec-table th {
            background-color: var(--primary-dark-green);
            color: white;
            font-weight: 600;
        }

        .api-endpoint {
            background-color: var(--gray-100);
            border-left: 4px solid var(--primary-dark-green);
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0 0.5rem 0.5rem 0;
        }

        .method-get {
            border-left-color: var(--light-green);
        }

        .method-post {
            border-left-color: var(--secondary-dark-blue);
        }

        .method-put {
            border-left-color: var(--orange);
        }

        .method-delete {
            border-left-color: var(--red);
        }

        .scroll-to-top {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            background-color: var(--primary-dark-green);
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            font-size: 1.2rem;
            cursor: pointer;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1000;
        }

        .scroll-to-top.visible {
            opacity: 1;
        }

        .scroll-to-top:hover {
            background-color: var(--light-green);
            transform: scale(1.1);
        }

        /* Dark mode styles */
        .dark-mode {
            --light: #1a1a1a;
            --gray-100: #2d2d2d;
            --gray-200: #404040;
            --gray-300: #555555;
            --gray-600: #cccccc;
            --gray-700: #e0e0e0;
            --gray-800: #f0f0f0;
            --gray-900: #ffffff;
        }

        .dark-mode body {
            background-color: var(--light);
            color: var(--gray-800);
        }

        .dark-mode .content-area {
            background-color: var(--gray-100);
        }

        .dark-mode .card {
            background-color: var(--gray-200);
            color: var(--gray-800);
        }

        .dark-mode .sidebar {
            background-color: var(--gray-200);
            border-right-color: var(--gray-300);
        }

        .theme-toggle {
            position: fixed;
            top: 1rem;
            right: 1rem;
            z-index: 1001;
            background-color: var(--primary-dark-green);
            color: white;
            border: none;
            border-radius: 50%;
            width: 45px;
            height: 45px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .theme-toggle:hover {
            background-color: var(--light-green);
            transform: scale(1.1);
        }

        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: -100%;
                transition: left 0.3s ease;
                z-index: 1000;
                width: 280px;
            }

            .sidebar.show {
                left: 0;
            }

            .content-area {
                padding: 1rem;
            }

            .feature-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Theme Toggle Button -->
    <button class="theme-toggle" onclick="toggleTheme()" title="Toggle Dark Mode">
        <i class="fas fa-moon" id="theme-icon"></i>
    </button>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#overview">
                <i class="fas fa-file-alt me-2"></i>
                Document Management System - Technical Documentation
            </a>
            <button class="navbar-toggler d-lg-none" type="button" onclick="toggleSidebar()">
                <span class="navbar-toggler-icon"></span>
            </button>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar Navigation -->
            <nav class="col-lg-3 col-xl-2 sidebar" id="sidebar">
                <div class="nav flex-column pt-3">
                    <a class="nav-link active" href="#overview">
                        <i class="fas fa-home"></i>Overview
                    </a>
                    <a class="nav-link" href="#architecture">
                        <i class="fas fa-sitemap"></i>System Architecture
                    </a>
                    <a class="nav-link" href="#core-features">
                        <i class="fas fa-cogs"></i>Core Features
                    </a>
                    <a class="nav-link" href="#user-management">
                        <i class="fas fa-users"></i>User Management
                    </a>
                    <a class="nav-link" href="#ai-integration">
                        <i class="fas fa-brain"></i>AI Integration
                    </a>
                    <a class="nav-link" href="#geographical">
                        <i class="fas fa-map-marked-alt"></i>Geographical Features
                    </a>
                    <a class="nav-link" href="#database">
                        <i class="fas fa-database"></i>Database Schema
                    </a>
                    <a class="nav-link" href="#api">
                        <i class="fas fa-code"></i>API Documentation
                    </a>
                    <a class="nav-link" href="#analytics">
                        <i class="fas fa-chart-line"></i>Analytics & Monitoring
                    </a>
                    <a class="nav-link" href="#styling">
                        <i class="fas fa-palette"></i>Styling & UI
                    </a>
                    <a class="nav-link" href="#deployment">
                        <i class="fas fa-server"></i>Deployment
                    </a>
                </div>
            </nav>

            <!-- Main Content -->
            <main class="col-lg-9 col-xl-10 content-area">
                <!-- Overview Section -->
                <section id="overview">
                    <div class="section-header">
                        <h1><i class="fas fa-file-alt me-3"></i>Document Management System</h1>
                        <p>Comprehensive Technical Documentation for ERDB AI-Powered Knowledge Management Platform</p>
                    </div>

                    <div class="row">
                        <div class="col-lg-8">
                            <div class="card">
                                <div class="card-header">
                                    <h3><i class="fas fa-info-circle me-2"></i>System Overview</h3>
                                </div>
                                <div class="card-body">
                                    <p class="lead">The Document Management System is a comprehensive AI-powered platform designed for the Ecosystems Research and Development Bureau (ERDB) to manage, search, and interact with knowledge products through intelligent document processing and conversational AI capabilities.</p>

                                    <h5>Key Capabilities</h5>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success me-2"></i><strong>Intelligent Document Processing:</strong> PDF upload, text extraction, image analysis, and table processing</li>
                                        <li><i class="fas fa-check text-success me-2"></i><strong>AI-Powered Search:</strong> Vector-based semantic search with multiple embedding models</li>
                                        <li><i class="fas fa-check text-success me-2"></i><strong>Conversational Interface:</strong> Category-based chat with citation support and anti-hallucination modes</li>
                                        <li><i class="fas fa-check text-success me-2"></i><strong>Geographical Intelligence:</strong> Location extraction and mapping for Philippine administrative levels</li>
                                        <li><i class="fas fa-check text-success me-2"></i><strong>Comprehensive Analytics:</strong> Usage tracking, performance monitoring, and geolocation analytics</li>
                                        <li><i class="fas fa-check text-success me-2"></i><strong>Enterprise Security:</strong> Role-based access control, user management, and audit logging</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-layer-group me-2"></i>Technology Stack</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <span class="badge badge-primary me-2">Python 3.8+</span>
                                        <span class="badge badge-secondary me-2">Flask</span>
                                        <span class="badge badge-success me-2">SQLite</span>
                                    </div>
                                    <div class="mb-3">
                                        <span class="badge badge-info me-2">ChromaDB</span>
                                        <span class="badge badge-warning me-2">Ollama</span>
                                        <span class="badge badge-danger me-2">Bootstrap 5</span>
                                    </div>
                                    <div class="mb-3">
                                        <span class="badge badge-primary me-2">Leaflet.js</span>
                                        <span class="badge badge-secondary me-2">Chart.js</span>
                                        <span class="badge badge-success me-2">spaCy NLP</span>
                                    </div>
                                </div>
                            </div>

                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-robot me-2"></i>AI Models</h5>
                                </div>
                                <div class="card-body">
                                    <h6>Language Models</h6>
                                    <ul class="list-unstyled small">
                                        <li><i class="fas fa-circle text-primary me-2" style="font-size: 0.5rem;"></i>Llama 3.1 8B Instruct (Default)</li>
                                        <li><i class="fas fa-circle text-secondary me-2" style="font-size: 0.5rem;"></i>Gemma 3 (1B/4B variants)</li>
                                    </ul>

                                    <h6>Vision Models</h6>
                                    <ul class="list-unstyled small">
                                        <li><i class="fas fa-circle text-success me-2" style="font-size: 0.5rem;"></i>Llama 3.2 Vision 11B</li>
                                        <li><i class="fas fa-circle text-info me-2" style="font-size: 0.5rem;"></i>Gemma 3 Multimodal (4B/12B)</li>
                                    </ul>

                                    <h6>Embedding Models</h6>
                                    <ul class="list-unstyled small">
                                        <li><i class="fas fa-circle text-warning me-2" style="font-size: 0.5rem;"></i>mxbai-embed-large (Default)</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- System Architecture Section -->
                <section id="architecture" class="mt-5">
                    <div class="section-header">
                        <h1><i class="fas fa-sitemap me-3"></i>System Architecture</h1>
                        <p>Technical architecture overview and component relationships</p>
                    </div>

                    <div class="architecture-diagram">
                        <h4 class="mb-4">High-Level Architecture</h4>
                        <div class="row">
                            <div class="col-12 mb-3">
                                <div class="component-box">Frontend Layer</div>
                                <div class="component-box secondary">Bootstrap 5 UI</div>
                                <div class="component-box success">Leaflet.js Maps</div>
                                <div class="component-box info">Chart.js Analytics</div>
                            </div>
                            <div class="col-12 mb-3">
                                <div class="component-box">Flask Application Server</div>
                                <div class="component-box secondary">Authentication & RBAC</div>
                                <div class="component-box success">Session Management</div>
                            </div>
                            <div class="col-12 mb-3">
                                <div class="component-box">AI Processing Layer</div>
                                <div class="component-box secondary">Ollama Integration</div>
                                <div class="component-box success">Vision Processing</div>
                                <div class="component-box info">NLP & Location Extraction</div>
                            </div>
                            <div class="col-12 mb-3">
                                <div class="component-box">Data Storage Layer</div>
                                <div class="component-box secondary">SQLite Databases</div>
                                <div class="component-box success">ChromaDB Vectors</div>
                                <div class="component-box info">File System Storage</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-server me-2"></i>Core Components</h5>
                                </div>
                                <div class="card-body">
                                    <h6>Flask Application Server (app.py)</h6>
                                    <ul class="small">
                                        <li>HTTP request handling and routing</li>
                                        <li>Session management and authentication</li>
                                        <li>API endpoint definitions</li>
                                        <li>Template rendering and response generation</li>
                                        <li>CSRF protection with Flask-WTF</li>
                                        <li>Rate limiting with Flask-Limiter</li>
                                        <li>File upload handling (25MB limit)</li>
                                        <li>Device fingerprinting for session tracking</li>
                                        <li>Geolocation tracking middleware</li>
                                    </ul>

                                    <h6>Configuration</h6>
                                    <ul class="small">
                                        <li>Port 8080 (configurable via environment)</li>
                                        <li>Secret key management</li>
                                        <li>Maximum content length controls</li>
                                        <li>Temporary folder management</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-database me-2"></i>Database Architecture</h5>
                                </div>
                                <div class="card-body">
                                    <h6>User Management Database (user_management.db)</h6>
                                    <ul class="small">
                                        <li>User accounts and authentication</li>
                                        <li>Role-based access control</li>
                                        <li>Permission groups and inheritance</li>
                                        <li>Activity logging and audit trails</li>
                                        <li>Session management</li>
                                    </ul>

                                    <h6>Chat History Database (chat_history.db)</h6>
                                    <ul class="small">
                                        <li>Conversation history storage</li>
                                        <li>Analytics and usage tracking</li>
                                        <li>Geolocation data</li>
                                        <li>Model performance metrics</li>
                                    </ul>

                                    <h6>Content Database (content_db.sqlite)</h6>
                                    <ul class="small">
                                        <li>PDF document metadata</li>
                                        <li>URL source management</li>
                                        <li>Cover images and thumbnails</li>
                                        <li>Geographical location data</li>
                                        <li>Geocoding cache</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Core Features Section -->
                <section id="core-features" class="mt-5">
                    <div class="section-header">
                        <h1><i class="fas fa-cogs me-3"></i>Core Features</h1>
                        <p>Detailed overview of system capabilities and implementation</p>
                    </div>

                    <div class="feature-grid">
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-file-upload"></i>
                            </div>
                            <h5>Document Processing</h5>
                            <p><strong>PDF Upload & Processing:</strong></p>
                            <ul class="small">
                                <li>25MB file size limit with validation</li>
                                <li>Text extraction using PyMuPDF</li>
                                <li>Image extraction and analysis</li>
                                <li>Table detection and processing</li>
                                <li>Hierarchical storage by category</li>
                                <li>Duplicate detection and prevention</li>
                            </ul>
                            <p><strong>URL Scraping:</strong></p>
                            <ul class="small">
                                <li>Web content extraction</li>
                                <li>Image and link collection</li>
                                <li>Metadata preservation</li>
                                <li>Source URL association</li>
                            </ul>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-search"></i>
                            </div>
                            <h5>Vector Search & Retrieval</h5>
                            <p><strong>ChromaDB Integration:</strong></p>
                            <ul class="small">
                                <li>Semantic vector search</li>
                                <li>Category-based collections</li>
                                <li>Configurable retrieval parameters</li>
                                <li>Relevance threshold filtering</li>
                                <li>Metadata-enhanced search</li>
                            </ul>
                            <p><strong>Embedding Models:</strong></p>
                            <ul class="small">
                                <li>mxbai-embed-large (default)</li>
                                <li>Configurable chunk size (800 tokens)</li>
                                <li>Overlap handling (250 tokens)</li>
                                <li>Batch processing optimization</li>
                            </ul>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-comments"></i>
                            </div>
                            <h5>Conversational AI</h5>
                            <p><strong>Chat Interface:</strong></p>
                            <ul class="small">
                                <li>Category-based conversations</li>
                                <li>Session management with persistence</li>
                                <li>Client name collection and addressing</li>
                                <li>Real-time response generation</li>
                                <li>Citation support with source links</li>
                            </ul>
                            <p><strong>Anti-Hallucination Modes:</strong></p>
                            <ul class="small">
                                <li><strong>Strict:</strong> Only document-based responses</li>
                                <li><strong>Balanced:</strong> Limited inference allowed</li>
                                <li><strong>Off:</strong> Creative responses enabled</li>
                            </ul>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-images"></i>
                            </div>
                            <h5>Image Processing</h5>
                            <p><strong>Vision Model Analysis:</strong></p>
                            <ul class="small">
                                <li>Llama 3.2 Vision 11B integration</li>
                                <li>Gemma 3 multimodal support</li>
                                <li>Intelligent image filtering</li>
                                <li>Relevance threshold configuration</li>
                                <li>Contextual caption generation</li>
                            </ul>
                            <p><strong>Image Management:</strong></p>
                            <ul class="small">
                                <li>Cover image selection hierarchy</li>
                                <li>Thumbnail generation</li>
                                <li>URL-based image scraping</li>
                                <li>Temporary storage organization</li>
                            </ul>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-table"></i>
                            </div>
                            <h5>CRUD Operations</h5>
                            <p><strong>Document Management:</strong></p>
                            <ul class="small">
                                <li>Create: PDF upload and processing</li>
                                <li>Read: Document viewing and search</li>
                                <li>Update: Metadata modification</li>
                                <li>Delete: Cascading resource cleanup</li>
                                <li>Category organization</li>
                            </ul>
                            <p><strong>Resource Management:</strong></p>
                            <ul class="small">
                                <li>Automatic cleanup on deletion</li>
                                <li>Orphaned resource detection</li>
                                <li>Storage optimization</li>
                                <li>Backup and recovery support</li>
                            </ul>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-history"></i>
                            </div>
                            <h5>Session Management</h5>
                            <p><strong>Session Tracking:</strong></p>
                            <ul class="small">
                                <li>Device fingerprinting</li>
                                <li>Session persistence across refreshes</li>
                                <li>Timestamp tracking</li>
                                <li>Multi-user concurrent support</li>
                                <li>Session analytics</li>
                            </ul>
                            <p><strong>Chat History:</strong></p>
                            <ul class="small">
                                <li>Conversation storage</li>
                                <li>Source preservation</li>
                                <li>Image association</li>
                                <li>Model configuration tracking</li>
                            </ul>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-code me-2"></i>Technical Implementation Details</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-6">
                                    <h6>File Processing Pipeline</h6>
                                    <div class="code-block">
<pre><code class="language-python">def process_pdf(pdf_path, category=None, source_url=None,
               extract_tables=True, save_images=True,
               use_vision=None, extract_locations=True):
    """
    Central PDF processing function
    - Text extraction with PyMuPDF
    - Image analysis with vision models
    - Table extraction with Camelot
    - Location extraction with spaCy NER
    - Hierarchical storage organization
    """</code></pre>
                                    </div>

                                    <h6>Query Processing</h6>
                                    <div class="code-block">
<pre><code class="language-python">def query_category(category, question,
                  anti_hallucination_mode='strict',
                  client_name=None, session_id=None):
    """
    AI query processing with:
    - Vector similarity search
    - Context assembly
    - LLM response generation
    - Citation formatting
    - Hallucination detection
    """</code></pre>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <h6>Configuration Management</h6>
                                    <div class="code-block">
<pre><code class="language-json">{
  "llm_model": "llama3.1:8b-instruct-q4_K_M",
  "embedding_model": "mxbai-embed-large:latest",
  "vision_model": "llama3.2-vision:11b-instruct-q4_K_M",
  "model_parameters": {
    "temperature": 0.7,
    "num_ctx": 4096,
    "num_predict": 256,
    "top_p": 0.9,
    "top_k": 40,
    "repeat_penalty": 1.1
  }
}</code></pre>
                                    </div>

                                    <h6>Anti-Hallucination Configuration</h6>
                                    <div class="code-block">
<pre><code class="language-json">{
  "hallucination_detection": {
    "threshold_strict": 0.6,
    "threshold_balanced": 0.4,
    "min_statement_length": 20,
    "enable_detection": true
  }
}</code></pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- User Management Section -->
                <section id="user-management" class="mt-5">
                    <div class="section-header">
                        <h1><i class="fas fa-users me-3"></i>User Management System</h1>
                        <p>Authentication, authorization, and user administration</p>
                    </div>

                    <div class="row">
                        <div class="col-lg-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-shield-alt me-2"></i>Authentication & Security</h5>
                                </div>
                                <div class="card-body">
                                    <h6>User Registration & Login</h6>
                                    <ul class="small">
                                        <li>Username validation (3-20 alphanumeric characters)</li>
                                        <li>Email validation with EmailValidator</li>
                                        <li>Password complexity requirements</li>
                                        <li>Account approval workflow</li>
                                        <li>Failed login attempt tracking</li>
                                        <li>Password change enforcement</li>
                                    </ul>

                                    <h6>Security Features</h6>
                                    <ul class="small">
                                        <li>Bcrypt password hashing</li>
                                        <li>Session token management</li>
                                        <li>CSRF protection</li>
                                        <li>Rate limiting</li>
                                        <li>Account lockout protection</li>
                                        <li>Secure cookie handling</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-users-cog me-2"></i>Role-Based Access Control</h5>
                                </div>
                                <div class="card-body">
                                    <h6>User Roles</h6>
                                    <ul class="small">
                                        <li><strong>Admin:</strong> Full system access and user management</li>
                                        <li><strong>Editor:</strong> Content management and limited admin functions</li>
                                        <li><strong>Viewer:</strong> Read-only access to content</li>
                                    </ul>

                                    <h6>Permission Groups</h6>
                                    <ul class="small">
                                        <li>Function-based permissions</li>
                                        <li>Hierarchical inheritance</li>
                                        <li>Individual permission overrides</li>
                                        <li>Automatic group assignment</li>
                                        <li>Permission audit logging</li>
                                    </ul>

                                    <h6>Protected Functions</h6>
                                    <ul class="small">
                                        <li>Document upload and management</li>
                                        <li>User administration</li>
                                        <li>System configuration</li>
                                        <li>Analytics access</li>
                                        <li>Model configuration</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- AI Integration Section -->
                <section id="ai-integration" class="mt-5">
                    <div class="section-header">
                        <h1><i class="fas fa-brain me-3"></i>AI Integration</h1>
                        <p>Comprehensive AI model integration and processing capabilities</p>
                    </div>

                    <div class="row">
                        <div class="col-lg-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-robot me-2"></i>Language Models</h5>
                                </div>
                                <div class="card-body">
                                    <h6>Primary Models</h6>
                                    <ul class="small">
                                        <li><strong>Llama 3.1 8B Instruct:</strong> Default conversational AI</li>
                                        <li><strong>Gemma 3 1B/4B:</strong> Lightweight alternatives</li>
                                        <li><strong>Model Fallback:</strong> Automatic failover to stable models</li>
                                    </ul>

                                    <h6>Model Parameters</h6>
                                    <table class="table table-sm">
                                        <tr><td>Temperature</td><td>0.7</td></tr>
                                        <tr><td>Context Window</td><td>4096</td></tr>
                                        <tr><td>Max Tokens</td><td>256</td></tr>
                                        <tr><td>Top-p</td><td>0.9</td></tr>
                                        <tr><td>Top-k</td><td>40</td></tr>
                                        <tr><td>Repeat Penalty</td><td>1.1</td></tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-eye me-2"></i>Vision Models</h5>
                                </div>
                                <div class="card-body">
                                    <h6>Supported Models</h6>
                                    <ul class="small">
                                        <li><strong>Llama 3.2 Vision 11B:</strong> Primary vision analysis</li>
                                        <li><strong>Gemma 3 4B/12B:</strong> Alternative vision capabilities</li>
                                    </ul>

                                    <h6>Vision Processing Features</h6>
                                    <ul class="small">
                                        <li>Image relevance filtering</li>
                                        <li>Contextual caption generation</li>
                                        <li>Document image analysis</li>
                                        <li>Configurable sensitivity levels</li>
                                        <li>Batch processing optimization</li>
                                        <li>Cache-enabled processing</li>
                                    </ul>

                                    <h6>Filter Sensitivity Levels</h6>
                                    <ul class="small">
                                        <li><strong>Low:</strong> Minimal filtering</li>
                                        <li><strong>Medium:</strong> Balanced relevance (default)</li>
                                        <li><strong>High:</strong> Strict relevance filtering</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-vector-square me-2"></i>Embedding Models</h5>
                                </div>
                                <div class="card-body">
                                    <h6>Primary Embedding Model</h6>
                                    <ul class="small">
                                        <li><strong>mxbai-embed-large:</strong> Default embedding model</li>
                                        <li>High-dimensional vector representations</li>
                                        <li>Optimized for semantic similarity</li>
                                        <li>Multi-language support</li>
                                    </ul>

                                    <h6>Embedding Parameters</h6>
                                    <table class="table table-sm">
                                        <tr><td>Chunk Size</td><td>800 tokens</td></tr>
                                        <tr><td>Chunk Overlap</td><td>250 tokens</td></tr>
                                        <tr><td>Batch Size</td><td>50 documents</td></tr>
                                        <tr><td>Processing Threads</td><td>4</td></tr>
                                    </table>

                                    <h6>Vector Database</h6>
                                    <ul class="small">
                                        <li>ChromaDB integration</li>
                                        <li>Category-based collections</li>
                                        <li>Metadata preservation</li>
                                        <li>Similarity search optimization</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-shield-alt me-2"></i>Anti-Hallucination System</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-6">
                                    <h6>Detection Modes</h6>
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>Mode</th>
                                                <th>Threshold</th>
                                                <th>Description</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td><span class="badge badge-danger">Strict</span></td>
                                                <td>0.6</td>
                                                <td>Only document-based responses</td>
                                            </tr>
                                            <tr>
                                                <td><span class="badge badge-warning">Balanced</span></td>
                                                <td>0.4</td>
                                                <td>Limited inference with citations</td>
                                            </tr>
                                            <tr>
                                                <td><span class="badge badge-success">Off</span></td>
                                                <td>N/A</td>
                                                <td>Creative responses with external knowledge</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="col-lg-6">
                                    <h6>Detection Algorithm</h6>
                                    <div class="code-block">
<pre><code class="language-python">def detect_hallucination(answer, context_docs, mode):
    """
    Hallucination detection process:
    1. Extract key statements from answer
    2. Check statement support in context
    3. Calculate word overlap ratios
    4. Apply mode-specific thresholds
    5. Generate appropriate warnings
    """</code></pre>
                                    </div>

                                    <h6>Configuration Options</h6>
                                    <ul class="small">
                                        <li>Configurable detection thresholds</li>
                                        <li>Minimum statement length filtering</li>
                                        <li>Context overlap analysis</li>
                                        <li>Mode-specific warning messages</li>
                                        <li>Performance impact monitoring</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Geographical Features Section -->
                <section id="geographical" class="mt-5">
                    <div class="section-header">
                        <h1><i class="fas fa-map-marked-alt me-3"></i>Geographical Features</h1>
                        <p>Location extraction, mapping, and Philippine administrative level processing</p>
                    </div>

                    <div class="row">
                        <div class="col-lg-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-search-location me-2"></i>Location Extraction (NER)</h5>
                                </div>
                                <div class="card-body">
                                    <h6>Named Entity Recognition</h6>
                                    <ul class="small">
                                        <li><strong>spaCy NLP:</strong> Core NER processing</li>
                                        <li><strong>Entity Types:</strong> GPE, LOC, FAC</li>
                                        <li><strong>Philippine Focus:</strong> Administrative level filtering</li>
                                        <li><strong>Confidence Scoring:</strong> Quality assessment</li>
                                    </ul>

                                    <h6>Administrative Levels</h6>
                                    <table class="table table-sm">
                                        <tr>
                                            <td><span class="badge badge-primary">Municipality</span></td>
                                            <td>Local government units</td>
                                        </tr>
                                        <tr>
                                            <td><span class="badge badge-secondary">City</span></td>
                                            <td>Urban administrative centers</td>
                                        </tr>
                                        <tr>
                                            <td><span class="badge badge-success">Barangay</span></td>
                                            <td>Smallest administrative divisions</td>
                                        </tr>
                                    </table>

                                    <h6>Extraction Methods</h6>
                                    <ul class="small">
                                        <li><strong>NER:</strong> Named entity recognition</li>
                                        <li><strong>Regex:</strong> Pattern-based extraction</li>
                                        <li><strong>Manual:</strong> User-defined locations</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-globe me-2"></i>Geocoding & Mapping</h5>
                                </div>
                                <div class="card-body">
                                    <h6>Geocoding Service</h6>
                                    <ul class="small">
                                        <li><strong>Nominatim (OpenStreetMap):</strong> Primary geocoding</li>
                                        <li><strong>Rate Limiting:</strong> Respectful API usage</li>
                                        <li><strong>Caching:</strong> Performance optimization</li>
                                        <li><strong>Fallback Handling:</strong> Error resilience</li>
                                    </ul>

                                    <h6>Leaflet.js Integration</h6>
                                    <ul class="small">
                                        <li>Interactive map visualization</li>
                                        <li>Clustered marker display</li>
                                        <li>Category-based filtering</li>
                                        <li>Location details sidebar</li>
                                        <li>ERDB brand color scheme</li>
                                        <li>Dark mode support</li>
                                        <li>Responsive design</li>
                                    </ul>

                                    <h6>Map Features</h6>
                                    <ul class="small">
                                        <li>Default center: Los Baños (14.1648, 121.2413)</li>
                                        <li>OpenStreetMap tile layers</li>
                                        <li>Zoom controls and attribution</li>
                                        <li>Mobile-responsive interface</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-database me-2"></i>Location Data Management</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-6">
                                    <h6>Database Tables</h6>
                                    <div class="code-block">
<pre><code class="language-sql">-- Extracted locations storage
extracted_locations (
    id INTEGER PRIMARY KEY,
    location_text TEXT NOT NULL,
    location_type TEXT CHECK(...),
    latitude REAL,
    longitude REAL,
    confidence_score REAL,
    context_snippet TEXT,
    geocoded_address TEXT,
    country TEXT,
    region TEXT,
    city TEXT,
    municipality TEXT,
    barangay TEXT,
    administrative_level TEXT
)</code></pre>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <h6>Source Linking</h6>
                                    <div class="code-block">
<pre><code class="language-sql">-- Location source tracking
location_sources (
    id INTEGER PRIMARY KEY,
    location_id INTEGER,
    source_type TEXT CHECK(...),
    source_id INTEGER,
    page_number INTEGER,
    extraction_method TEXT,
    FOREIGN KEY (location_id)
        REFERENCES extracted_locations(id)
        ON DELETE CASCADE
)</code></pre>
                                    </div>
                                </div>
                            </div>

                            <h6>Cascading Deletion</h6>
                            <p class="small">When a PDF is deleted, all associated location data is automatically removed through foreign key constraints, ensuring data consistency and preventing orphaned records.</p>

                            <h6>Performance Optimization</h6>
                            <ul class="small">
                                <li>Geocoding cache with expiration</li>
                                <li>Confidence threshold filtering</li>
                                <li>Maximum locations per document limit</li>
                                <li>Asynchronous map loading</li>
                                <li>Batch processing for large documents</li>
                            </ul>
                        </div>
                    </div>
                </section>

                <!-- Database Schema Section -->
                <section id="database" class="mt-5">
                    <div class="section-header">
                        <h1><i class="fas fa-database me-3"></i>Database Schema</h1>
                        <p>Complete database architecture with table structures, relationships, and constraints</p>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-users-cog me-2"></i>User Management Database (user_management.db)</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-6">
                                    <h6>Core User Table</h6>
                                    <div class="code-block">
<pre><code class="language-sql">CREATE TABLE users (
    user_id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    role TEXT NOT NULL CHECK(role IN ('admin', 'editor', 'viewer')),
    group_id INTEGER,
    account_status TEXT DEFAULT 'pending'
        CHECK(account_status IN ('active', 'pending', 'suspended', 'deleted')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP,
    failed_login_attempts INTEGER DEFAULT 0,
    password_changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    profile_picture TEXT,
    full_name TEXT,
    email_verified BOOLEAN DEFAULT 0,
    FOREIGN KEY (group_id) REFERENCES permission_groups(group_id)
);</code></pre>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <h6>Permission Groups</h6>
                                    <div class="code-block">
<pre><code class="language-sql">CREATE TABLE permission_groups (
    group_id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT UNIQUE NOT NULL,
    description TEXT,
    permissions TEXT, -- JSON format
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Default permission groups
INSERT INTO permission_groups (name, description, permissions) VALUES
('Admin', 'Full system access', '{"all": true}'),
('Editor', 'Content management access',
 '{"upload_files": true, "manage_content": true, "view_analytics": true}'),
('Viewer', 'Read-only access',
 '{"view_content": true}');</code></pre>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-lg-6">
                                    <h6>User Permissions & Activity Logs</h6>
                                    <div class="code-block">
<pre><code class="language-sql">CREATE TABLE user_permissions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    permission_name TEXT NOT NULL,
    granted BOOLEAN DEFAULT 1,
    granted_by INTEGER,
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES users(user_id)
);

CREATE TABLE activity_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    action TEXT NOT NULL,
    resource_type TEXT,
    resource_id TEXT,
    ip_address TEXT,
    user_agent TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL
);</code></pre>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <h6>Session Management</h6>
                                    <div class="code-block">
<pre><code class="language-sql">CREATE TABLE user_sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    session_token TEXT UNIQUE NOT NULL,
    device_fingerprint TEXT,
    ip_address TEXT,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT 1,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- Indexes for performance
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_sessions_token ON user_sessions(session_token);
CREATE INDEX idx_sessions_user ON user_sessions(user_id);
CREATE INDEX idx_activity_user ON activity_logs(user_id);
CREATE INDEX idx_activity_timestamp ON activity_logs(timestamp);</code></pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-comments me-2"></i>Chat History Database (chat_history.db)</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-6">
                                    <h6>Chat History Table</h6>
                                    <div class="code-block">
<pre><code class="language-sql">CREATE TABLE chat_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id TEXT NOT NULL,
    category TEXT NOT NULL,
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    sources TEXT, -- JSON format
    images TEXT, -- JSON format
    pdf_links TEXT, -- JSON format
    metadata TEXT, -- JSON format
    url_images TEXT, -- JSON format
    pdf_images TEXT, -- JSON format
    client_name TEXT,
    session_start TIMESTAMP,
    device_fingerprint TEXT,
    document_thumbnails TEXT, -- JSON format
    anti_hallucination_mode TEXT DEFAULT 'strict',
    llm_model TEXT,
    embedding_model TEXT,
    vision_model TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);</code></pre>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <h6>Analytics Table</h6>
                                    <div class="code-block">
<pre><code class="language-sql">CREATE TABLE chat_analytics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id TEXT,
    chat_id INTEGER,
    category TEXT,
    client_name TEXT,
    question_length INTEGER,
    answer_length INTEGER,
    processing_time REAL,
    source_count INTEGER,
    image_count INTEGER,
    token_count INTEGER,
    model_name TEXT,
    embedding_model TEXT,
    vision_model TEXT,
    vision_enabled BOOLEAN DEFAULT 0,
    images_filtered INTEGER DEFAULT 0,
    total_images_extracted INTEGER DEFAULT 0,
    filter_sensitivity TEXT DEFAULT 'medium',
    hallucination_detected BOOLEAN DEFAULT 0,
    anti_hallucination_mode TEXT DEFAULT 'strict',
    device_fingerprint TEXT,
    ip_address TEXT,
    city TEXT,
    region TEXT,
    country TEXT,
    latitude REAL,
    longitude REAL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (chat_id) REFERENCES chat_history(id) ON DELETE CASCADE
);</code></pre>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-lg-6">
                                    <h6>GeoIP Analytics</h6>
                                    <div class="code-block">
<pre><code class="language-sql">CREATE TABLE geoip_analytics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ip_address TEXT NOT NULL,
    device_fingerprint TEXT,
    client_name TEXT,
    city TEXT,
    region TEXT,
    country TEXT,
    latitude REAL,
    longitude REAL,
    user_agent TEXT,
    page_url TEXT,
    session_id TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Performance indexes
CREATE INDEX idx_chat_session ON chat_history(session_id);
CREATE INDEX idx_chat_category ON chat_history(category);
CREATE INDEX idx_chat_timestamp ON chat_history(timestamp);
CREATE INDEX idx_analytics_session ON chat_analytics(session_id);
CREATE INDEX idx_analytics_model ON chat_analytics(model_name);
CREATE INDEX idx_geoip_ip ON geoip_analytics(ip_address);
CREATE INDEX idx_geoip_timestamp ON geoip_analytics(timestamp);</code></pre>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <h6>Greeting Templates</h6>
                                    <div class="code-block">
<pre><code class="language-sql">CREATE TABLE greeting_templates (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    template_type TEXT NOT NULL
        CHECK(template_type IN ('welcome', 'response', 'return_user', 'time_based')),
    greeting_text TEXT NOT NULL,
    context_conditions TEXT, -- JSON for conditions
    is_active BOOLEAN DEFAULT 1,
    weight INTEGER DEFAULT 1, -- For weighted random selection
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Default greeting templates
INSERT INTO greeting_templates (template_type, greeting_text, weight) VALUES
('welcome', 'Welcome to ERDB Knowledge Products! I''m here to help you find information from our research documents.', 10),
('response', 'I''m happy to help you explore ERDB''s knowledge products.', 5),
('return_user', 'Welcome back! How can I assist you with ERDB research today?', 8),
('time_based', 'Good morning! Ready to explore ERDB''s research resources?', 3);</code></pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-file-alt me-2"></i>Content Database (content_db.sqlite)</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-6">
                                    <h6>Document Management</h6>
                                    <div class="code-block">
<pre><code class="language-sql">CREATE TABLE source_urls (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    url TEXT UNIQUE NOT NULL,
    title TEXT,
    description TEXT,
    last_scraped TIMESTAMP,
    last_updated TIMESTAMP,
    status TEXT DEFAULT 'active'
        CHECK(status IN ('active', 'archived', 'error')),
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE pdf_documents (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    filename TEXT NOT NULL,
    original_filename TEXT NOT NULL,
    category TEXT NOT NULL,
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    source_url_id INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (source_url_id) REFERENCES source_urls(id) ON DELETE SET NULL
);</code></pre>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <h6>URL Content & Images</h6>
                                    <div class="code-block">
<pre><code class="language-sql">CREATE TABLE url_content (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    source_url_id INTEGER NOT NULL,
    content_type TEXT NOT NULL
        CHECK(content_type IN ('text', 'image', 'link')),
    content TEXT NOT NULL,
    content_order INTEGER DEFAULT 0,
    metadata TEXT, -- JSON format
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (source_url_id) REFERENCES source_urls(id) ON DELETE CASCADE
);

CREATE TABLE cover_images (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    pdf_document_id INTEGER NOT NULL,
    image_path TEXT NOT NULL,
    image_url TEXT NOT NULL,
    source TEXT NOT NULL
        CHECK(source IN ('pdf_first_page', 'pdf_internal', 'url', 'default')),
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (pdf_document_id) REFERENCES pdf_documents(id) ON DELETE CASCADE
);</code></pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-map-marked-alt me-2"></i>Geographical Location Tables</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-6">
                                    <h6>Extracted Locations</h6>
                                    <div class="code-block">
<pre><code class="language-sql">CREATE TABLE extracted_locations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    location_text TEXT NOT NULL,
    location_type TEXT NOT NULL
        CHECK(location_type IN ('place_name', 'address', 'coordinates',
                                'landmark', 'region', 'municipality', 'city', 'barangay')),
    latitude REAL,
    longitude REAL,
    confidence_score REAL DEFAULT 0.0,
    context_snippet TEXT,
    geocoded_address TEXT,
    country TEXT,
    region TEXT,
    city TEXT,
    municipality TEXT,
    barangay TEXT,
    administrative_level TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE location_sources (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    location_id INTEGER NOT NULL,
    source_type TEXT NOT NULL
        CHECK(source_type IN ('pdf_document', 'chat_message', 'url_content')),
    source_id INTEGER NOT NULL,
    page_number INTEGER,
    extraction_method TEXT NOT NULL
        CHECK(extraction_method IN ('ner', 'regex', 'manual')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (location_id) REFERENCES extracted_locations(id) ON DELETE CASCADE
);</code></pre>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <h6>Geocoding Cache & ChromaDB</h6>
                                    <div class="code-block">
<pre><code class="language-sql">CREATE TABLE geocoding_cache (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    location_query TEXT UNIQUE NOT NULL,
    latitude REAL,
    longitude REAL,
    formatted_address TEXT,
    geocoding_service TEXT DEFAULT 'nominatim',
    confidence_score REAL DEFAULT 0.0,
    cached_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,
    status TEXT DEFAULT 'success'
        CHECK(status IN ('success', 'failed', 'partial')),
    country TEXT,
    region TEXT,
    city TEXT
);

-- ChromaDB Collections (Vector Database)
-- Collections are created programmatically:
-- - One collection per document category
-- - Vector embeddings stored with metadata
-- - Document chunks with page references
-- - Similarity search optimization

-- Migration and Version Management
CREATE TABLE database_version (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    version INTEGER NOT NULL,
    applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    description TEXT
);

-- Current schema version
INSERT INTO database_version (version, description) VALUES
(1, 'Initial schema with all core tables');</code></pre>
                                    </div>
                                </div>
                            </div>

                            <h6>Database Relationships & Constraints</h6>
                            <div class="architecture-diagram">
                                <h5 class="text-center">Database Relationship Flow</h5>
                                <div class="component-layer">
                                    <div class="component-box">users</div>
                                    <div class="component-box secondary">→ user_sessions</div>
                                    <div class="component-box info">→ activity_logs</div>
                                </div>
                                <div class="component-layer">
                                    <div class="component-box">pdf_documents</div>
                                    <div class="component-box secondary">→ cover_images</div>
                                    <div class="component-box info">→ location_sources</div>
                                </div>
                                <div class="component-layer">
                                    <div class="component-box">chat_history</div>
                                    <div class="component-box secondary">→ chat_analytics</div>
                                    <div class="component-box info">→ geoip_analytics</div>
                                </div>
                                <div class="component-layer">
                                    <div class="component-box">extracted_locations</div>
                                    <div class="component-box secondary">→ geocoding_cache</div>
                                    <div class="component-box info">→ ChromaDB vectors</div>
                                </div>
                            </div>

                            <h6>Cascading Deletion Rules</h6>
                            <ul class="small">
                                <li><strong>User Deletion:</strong> Cascades to sessions, permissions, and sets activity logs user_id to NULL</li>
                                <li><strong>PDF Deletion:</strong> Cascades to cover images, location sources, and ChromaDB vectors</li>
                                <li><strong>Chat Deletion:</strong> Cascades to analytics records</li>
                                <li><strong>Location Deletion:</strong> Cascades to location sources</li>
                                <li><strong>URL Deletion:</strong> Cascades to URL content, sets PDF source_url_id to NULL</li>
                            </ul>
                        </div>
                    </div>
                </section>

                <!-- API Documentation Section -->
                <section id="api" class="mt-5">
                    <div class="section-header">
                        <h1><i class="fas fa-code me-3"></i>API Documentation</h1>
                        <p>Complete REST API reference with endpoints, authentication, and examples</p>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-shield-alt me-2"></i>Authentication & Authorization</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-6">
                                    <h6>Authentication Methods</h6>
                                    <ul class="small">
                                        <li><strong>Session-based:</strong> Flask sessions with secure cookies</li>
                                        <li><strong>CSRF Protection:</strong> Required for all POST/PUT/DELETE requests</li>
                                        <li><strong>Rate Limiting:</strong> Applied per IP and per user</li>
                                        <li><strong>Permission Decorators:</strong> Function-level access control</li>
                                    </ul>

                                    <h6>Required Headers</h6>
                                    <div class="code-block">
<pre><code class="language-http">Content-Type: application/json
X-CSRFToken: [csrf_token]
Cookie: session=[session_id]</code></pre>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <h6>Permission Levels</h6>
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Role</th>
                                                <th>Permissions</th>
                                                <th>API Access</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td><span class="badge badge-danger">Admin</span></td>
                                                <td>All functions</td>
                                                <td>Full API access</td>
                                            </tr>
                                            <tr>
                                                <td><span class="badge badge-warning">Editor</span></td>
                                                <td>Content management</td>
                                                <td>Upload, analytics</td>
                                            </tr>
                                            <tr>
                                                <td><span class="badge badge-success">Viewer</span></td>
                                                <td>Read-only</td>
                                                <td>Query, view only</td>
                                            </tr>
                                        </tbody>
                                    </table>

                                    <h6>Error Response Format</h6>
                                    <div class="code-block">
<pre><code class="language-json">{
  "error": "Authentication required",
  "code": 401,
  "message": "Please log in to access this resource",
  "timestamp": "2024-01-15T10:30:00Z"
}</code></pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-comments me-2"></i>Core Query API</h5>
                        </div>
                        <div class="card-body">
                            <div class="api-endpoint method-post">
                                <h6><span class="badge badge-secondary me-2">POST</span>/query</h6>
                                <p><strong>Process user queries and return AI responses</strong></p>

                                <h6>Request Body</h6>
                                <div class="code-block">
<pre><code class="language-json">{
  "category": "research_papers",
  "question": "What are the latest findings on biodiversity conservation?",
  "anti_hallucination_mode": "strict",
  "client_name": "John Doe",
  "session_id": "sess_123456789"
}</code></pre>
                                </div>

                                <h6>Response</h6>
                                <div class="code-block">
<pre><code class="language-json">{
  "success": true,
  "answer": "Based on recent ERDB research...",
  "sources": [
    {
      "filename": "biodiversity_report_2024.pdf",
      "page": 15,
      "url": "https://example.com/report.pdf",
      "relevance_score": 0.95
    }
  ],
  "images": [
    {
      "url": "/static/images/chart_biodiversity.png",
      "description": "Biodiversity trends chart",
      "source": "pdf_page_15"
    }
  ],
  "processing_time": 2.34,
  "model_info": {
    "llm": "llama3.1:8b-instruct-q4_K_M",
    "embedding": "mxbai-embed-large:latest",
    "vision": "llama3.2-vision:11b-instruct-q4_K_M"
  },
  "hallucination_detected": false,
  "follow_up_questions": [
    "What conservation strategies are most effective?",
    "How has biodiversity changed over the past decade?"
  ]
}</code></pre>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-upload me-2"></i>Document Management API</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="api-endpoint method-post">
                                        <h6><span class="badge badge-secondary me-2">POST</span>/upload</h6>
                                        <p><strong>Upload and process PDF documents</strong></p>
                                        <p><small><strong>Permission:</strong> upload_files</small></p>

                                        <h6>Request (multipart/form-data)</h6>
                                        <div class="code-block">
<pre><code class="language-http">Content-Type: multipart/form-data

file: [PDF file, max 25MB]
category: "research_papers"
source_url: "https://example.com/source" (optional)
extract_tables: true (optional)
save_images: true (optional)
use_vision: true (optional)
extract_locations: true (optional)</code></pre>
                                        </div>

                                        <h6>Response</h6>
                                        <div class="code-block">
<pre><code class="language-json">{
  "success": true,
  "message": "PDF processed successfully",
  "document_id": 123,
  "filename": "research_paper_2024.pdf",
  "category": "research_papers",
  "processing_stats": {
    "pages_processed": 45,
    "images_extracted": 12,
    "images_filtered": 8,
    "tables_found": 3,
    "locations_extracted": 15,
    "processing_time": 45.2
  }
}</code></pre>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="api-endpoint method-get">
                                        <h6><span class="badge badge-success me-2">GET</span>/documents</h6>
                                        <p><strong>List all documents with filtering</strong></p>
                                        <p><small><strong>Permission:</strong> view_content</small></p>

                                        <h6>Query Parameters</h6>
                                        <div class="code-block">
<pre><code class="language-http">GET /documents?category=research_papers&page=1&limit=20&search=biodiversity</code></pre>
                                        </div>

                                        <h6>Response</h6>
                                        <div class="code-block">
<pre><code class="language-json">{
  "success": true,
  "documents": [
    {
      "id": 123,
      "filename": "biodiversity_study.pdf",
      "original_filename": "Biodiversity Study 2024.pdf",
      "category": "research_papers",
      "upload_date": "2024-01-15T10:30:00Z",
      "source_url": "https://example.com/study.pdf",
      "cover_image": "/static/covers/123_cover.jpg",
      "page_count": 45,
      "file_size": "2.3MB"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 156,
    "pages": 8
  }
}</code></pre>
                                        </div>
                                    </div>

                                    <div class="api-endpoint method-delete">
                                        <h6><span class="badge badge-danger me-2">DELETE</span>/documents/{id}</h6>
                                        <p><strong>Delete document and all associated resources</strong></p>
                                        <p><small><strong>Permission:</strong> manage_content</small></p>

                                        <h6>Response</h6>
                                        <div class="code-block">
<pre><code class="language-json">{
  "success": true,
  "message": "Document and all associated resources deleted",
  "deleted_resources": {
    "images": 12,
    "vectors": 45,
    "locations": 8,
    "cover_images": 1
  }
}</code></pre>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-users-cog me-2"></i>User Management API</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="api-endpoint method-post">
                                        <h6><span class="badge badge-secondary me-2">POST</span>/auth/login</h6>
                                        <p><strong>User authentication</strong></p>

                                        <h6>Request Body</h6>
                                        <div class="code-block">
<pre><code class="language-json">{
  "username": "john_doe",
  "password": "secure_password123"
}</code></pre>
                                        </div>

                                        <h6>Response</h6>
                                        <div class="code-block">
<pre><code class="language-json">{
  "success": true,
  "message": "Login successful",
  "user": {
    "user_id": 123,
    "username": "john_doe",
    "role": "editor",
    "permissions": ["upload_files", "manage_content"],
    "last_login": "2024-01-15T10:30:00Z"
  },
  "session_expires": "2024-01-15T18:30:00Z"
}</code></pre>
                                        </div>
                                    </div>

                                    <div class="api-endpoint method-get">
                                        <h6><span class="badge badge-success me-2">GET</span>/users</h6>
                                        <p><strong>List users (Admin only)</strong></p>
                                        <p><small><strong>Permission:</strong> manage_users</small></p>

                                        <h6>Response</h6>
                                        <div class="code-block">
<pre><code class="language-json">{
  "success": true,
  "users": [
    {
      "user_id": 123,
      "username": "john_doe",
      "email": "<EMAIL>",
      "role": "editor",
      "account_status": "active",
      "created_at": "2024-01-01T00:00:00Z",
      "last_login": "2024-01-15T10:30:00Z"
    }
  ]
}</code></pre>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="api-endpoint method-get">
                                        <h6><span class="badge badge-success me-2">GET</span>/analytics/overview</h6>
                                        <p><strong>System analytics overview</strong></p>
                                        <p><small><strong>Permission:</strong> view_analytics</small></p>

                                        <h6>Response</h6>
                                        <div class="code-block">
<pre><code class="language-json">{
  "success": true,
  "analytics": {
    "total_documents": 156,
    "total_queries": 2847,
    "active_users": 23,
    "categories": {
      "research_papers": 89,
      "policy_documents": 34,
      "technical_reports": 33
    },
    "recent_activity": {
      "queries_today": 45,
      "uploads_today": 3,
      "new_users_week": 2
    },
    "model_performance": {
      "avg_response_time": 2.3,
      "hallucination_rate": 0.05,
      "user_satisfaction": 4.2
    }
  }
}</code></pre>
                                        </div>
                                    </div>

                                    <div class="api-endpoint method-get">
                                        <h6><span class="badge badge-success me-2">GET</span>/config/models</h6>
                                        <p><strong>Get current model configuration</strong></p>
                                        <p><small><strong>Permission:</strong> view_config</small></p>

                                        <h6>Response</h6>
                                        <div class="code-block">
<pre><code class="language-json">{
  "success": true,
  "models": {
    "llm": {
      "current": "llama3.1:8b-instruct-q4_K_M",
      "available": ["llama3.1:8b-instruct-q4_K_M", "gemma3:1b", "gemma3:4b"]
    },
    "embedding": {
      "current": "mxbai-embed-large:latest",
      "available": ["mxbai-embed-large:latest"]
    },
    "vision": {
      "current": "llama3.2-vision:11b-instruct-q4_K_M",
      "available": ["llama3.2-vision:11b-instruct-q4_K_M", "gemma3-multimodal:4b"]
    }
  }
}</code></pre>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <h6>Rate Limiting</h6>
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Endpoint</th>
                                        <th>Rate Limit</th>
                                        <th>Window</th>
                                        <th>Scope</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>/query</td>
                                        <td>30 requests</td>
                                        <td>1 minute</td>
                                        <td>Per user</td>
                                    </tr>
                                    <tr>
                                        <td>/upload</td>
                                        <td>10 requests</td>
                                        <td>1 hour</td>
                                        <td>Per user</td>
                                    </tr>
                                    <tr>
                                        <td>/auth/login</td>
                                        <td>5 requests</td>
                                        <td>5 minutes</td>
                                        <td>Per IP</td>
                                    </tr>
                                    <tr>
                                        <td>All endpoints</td>
                                        <td>1000 requests</td>
                                        <td>1 hour</td>
                                        <td>Per IP</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </section>

                <!-- Analytics & Monitoring Section -->
                <section id="analytics" class="mt-5">
                    <div class="section-header">
                        <h1><i class="fas fa-chart-line me-3"></i>Analytics & Monitoring</h1>
                        <p>Comprehensive analytics system for usage tracking, performance monitoring, and insights</p>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-chart-bar me-2"></i>Data Collection System</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-6">
                                    <h6>Chat Analytics Tracking</h6>
                                    <ul class="small">
                                        <li><strong>Query Metrics:</strong> Question length, processing time, token count</li>
                                        <li><strong>Response Quality:</strong> Source count, image count, hallucination detection</li>
                                        <li><strong>Model Performance:</strong> LLM, embedding, and vision model usage</li>
                                        <li><strong>User Behavior:</strong> Session patterns, category preferences</li>
                                        <li><strong>Geographic Data:</strong> Location-based usage patterns</li>
                                    </ul>

                                    <h6>Automatic Data Collection</h6>
                                    <div class="code-block">
<pre><code class="language-python">def track_chat_analytics(session_id, question, answer,
                         processing_time, models_used):
    """
    Automatically tracks:
    - Query and response metrics
    - Model performance data
    - Geographic information
    - Session behavior patterns
    - Hallucination detection results
    """</code></pre>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <h6>Performance Metrics</h6>
                                    <table class="table table-sm">
                                        <tr>
                                            <td><strong>Response Time</strong></td>
                                            <td>Average: 2.3s, P95: 5.2s</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Hallucination Rate</strong></td>
                                            <td>Strict: 2%, Balanced: 8%</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Source Accuracy</strong></td>
                                            <td>95% relevant citations</td>
                                        </tr>
                                        <tr>
                                            <td><strong>User Satisfaction</strong></td>
                                            <td>4.2/5.0 average rating</td>
                                        </tr>
                                        <tr>
                                            <td><strong>System Uptime</strong></td>
                                            <td>99.8% availability</td>
                                        </tr>
                                    </table>

                                    <h6>Real-time Monitoring</h6>
                                    <ul class="small">
                                        <li>Active user sessions</li>
                                        <li>Query processing queue</li>
                                        <li>Model response times</li>
                                        <li>Error rates and exceptions</li>
                                        <li>Resource utilization</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-globe me-2"></i>Geolocation Analytics</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-6">
                                    <h6>MaxMind GeoLite2 Integration</h6>
                                    <ul class="small">
                                        <li><strong>IP Geolocation:</strong> City-level accuracy</li>
                                        <li><strong>Data Sources:</strong> MaxMind GeoLite2 database</li>
                                        <li><strong>Privacy Compliance:</strong> IP anonymization after processing</li>
                                        <li><strong>Update Frequency:</strong> Monthly database updates</li>
                                        <li><strong>Fallback:</strong> Default to Los Baños coordinates</li>
                                    </ul>

                                    <h6>Geographic Analytics Queries</h6>
                                    <div class="code-block">
<pre><code class="language-sql">-- Usage by geographic region
SELECT country, region, city,
       COUNT(*) as query_count,
       AVG(processing_time) as avg_response_time
FROM chat_analytics
WHERE timestamp >= date('now', '-30 days')
GROUP BY country, region, city
ORDER BY query_count DESC;

-- Popular categories by location
SELECT city, category, COUNT(*) as usage_count
FROM chat_analytics
WHERE country = 'Philippines'
GROUP BY city, category
ORDER BY usage_count DESC;</code></pre>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <h6>Model Performance Analysis</h6>
                                    <div class="code-block">
<pre><code class="language-python">def analyze_model_performance():
    """
    Comprehensive model performance analysis:
    - Response time by model type
    - Hallucination rates by anti-hallucination mode
    - User satisfaction correlation
    - Resource utilization patterns
    - Error rate analysis
    """
    return {
        'llm_performance': {
            'llama3.1:8b': {'avg_time': 2.1, 'accuracy': 0.94},
            'gemma3:4b': {'avg_time': 1.8, 'accuracy': 0.91}
        },
        'vision_performance': {
            'image_filtering_accuracy': 0.89,
            'processing_time_per_image': 0.3
        },
        'embedding_performance': {
            'similarity_accuracy': 0.92,
            'retrieval_time': 0.15
        }
    }</code></pre>
                                    </div>

                                    <h6>Dashboard Components</h6>
                                    <ul class="small">
                                        <li><strong>Real-time Metrics:</strong> Active users, query rate, response times</li>
                                        <li><strong>Geographic Maps:</strong> Usage heatmaps with Leaflet.js</li>
                                        <li><strong>Performance Charts:</strong> Time-series data with Chart.js</li>
                                        <li><strong>Model Comparison:</strong> A/B testing results</li>
                                        <li><strong>Error Tracking:</strong> Exception monitoring and alerting</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Styling & UI Section -->
                <section id="styling" class="mt-5">
                    <div class="section-header">
                        <h1><i class="fas fa-palette me-3"></i>Styling & UI Framework</h1>
                        <p>Bootstrap 5 implementation with ERDB branding and accessibility compliance</p>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-paint-brush me-2"></i>ERDB Brand Color System</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-6">
                                    <h6>Primary Color Palette</h6>
                                    <div class="code-block">
<pre><code class="language-css">:root {
  /* ERDB Brand Colors */
  --primary-dark-green: #378C47;
  --secondary-dark-blue: #0267B6;
  --light-green: #5BA85B;
  --light-blue: #3CA6D6;
  --orange: #FFBD5C;
  --orange-dark: #FC762B;
  --red: #C12323;

  /* Semantic Colors */
  --success: var(--light-green);
  --info: var(--light-blue);
  --warning: var(--orange);
  --danger: var(--red);
}</code></pre>
                                    </div>

                                    <h6>Color Usage Guidelines</h6>
                                    <table class="table table-sm">
                                        <tr>
                                            <td><div style="width: 20px; height: 20px; background: #378C47; display: inline-block; margin-right: 10px;"></div><strong>Primary Green</strong></td>
                                            <td>Main navigation, primary buttons</td>
                                        </tr>
                                        <tr>
                                            <td><div style="width: 20px; height: 20px; background: #0267B6; display: inline-block; margin-right: 10px;"></div><strong>Secondary Blue</strong></td>
                                            <td>Secondary actions, links</td>
                                        </tr>
                                        <tr>
                                            <td><div style="width: 20px; height: 20px; background: #FFBD5C; display: inline-block; margin-right: 10px;"></div><strong>Warning Orange</strong></td>
                                            <td>Alerts, warnings, highlights</td>
                                        </tr>
                                        <tr>
                                            <td><div style="width: 20px; height: 20px; background: #C12323; display: inline-block; margin-right: 10px;"></div><strong>Error Red</strong></td>
                                            <td>Errors, deletions, critical alerts</td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-lg-6">
                                    <h6>Dark/Light Theme Implementation</h6>
                                    <div class="code-block">
<pre><code class="language-css">/* Light Theme (Default) */
body {
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --text-primary: #212529;
  --text-secondary: #6c757d;
}

/* Dark Theme */
.dark-mode {
  --bg-primary: #1a1a1a;
  --bg-secondary: #2d2d2d;
  --text-primary: #ffffff;
  --text-secondary: #cccccc;
}

/* Theme Toggle Implementation */
function toggleTheme() {
  document.body.classList.toggle('dark-mode');
  localStorage.setItem('theme',
    document.body.classList.contains('dark-mode') ? 'dark' : 'light'
  );
}</code></pre>
                                    </div>

                                    <h6>Theme Persistence</h6>
                                    <ul class="small">
                                        <li><strong>localStorage:</strong> User preference persistence</li>
                                        <li><strong>System Detection:</strong> Respects OS dark mode preference</li>
                                        <li><strong>Smooth Transitions:</strong> CSS transitions for theme changes</li>
                                        <li><strong>Component Consistency:</strong> All components support both themes</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-universal-access me-2"></i>WCAG 2.1 AA Accessibility Compliance</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-6">
                                    <h6>Color Contrast Standards</h6>
                                    <ul class="small">
                                        <li><strong>Text Contrast:</strong> Minimum 4.5:1 ratio for normal text</li>
                                        <li><strong>Large Text:</strong> Minimum 3:1 ratio for 18pt+ text</li>
                                        <li><strong>Interactive Elements:</strong> 3:1 ratio for UI components</li>
                                        <li><strong>Dark Text on Light:</strong> Preferred for readability</li>
                                    </ul>

                                    <h6>Keyboard Navigation</h6>
                                    <div class="code-block">
<pre><code class="language-css">/* Focus indicators */
.btn:focus, .form-control:focus {
  outline: 2px solid var(--primary-dark-green);
  outline-offset: 2px;
}

/* Skip links */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary-dark-green);
  color: white;
  padding: 8px;
  text-decoration: none;
  z-index: 1000;
}

.skip-link:focus {
  top: 6px;
}</code></pre>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <h6>Semantic HTML Structure</h6>
                                    <div class="code-block">
<pre><code class="language-html"><!-- Proper heading hierarchy -->
<h1>Main Page Title</h1>
  <h2>Section Title</h2>
    <h3>Subsection Title</h3>

<!-- ARIA labels and roles -->
<nav aria-label="Main navigation">
<button aria-expanded="false" aria-controls="menu">
<div role="alert" aria-live="polite">

<!-- Form accessibility -->
<label for="query-input">Enter your question</label>
<input id="query-input" aria-describedby="query-help">
<div id="query-help">Ask questions about ERDB research</div></code></pre>
                                    </div>

                                    <h6>Screen Reader Support</h6>
                                    <ul class="small">
                                        <li><strong>ARIA Labels:</strong> Descriptive labels for all interactive elements</li>
                                        <li><strong>Live Regions:</strong> Dynamic content announcements</li>
                                        <li><strong>Landmark Roles:</strong> Navigation, main, complementary regions</li>
                                        <li><strong>Alternative Text:</strong> Descriptive alt text for all images</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-mobile-alt me-2"></i>Responsive Design & Component Library</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-6">
                                    <h6>Bootstrap 5 Breakpoints</h6>
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Breakpoint</th>
                                                <th>Class Prefix</th>
                                                <th>Dimensions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>Extra Small</td>
                                                <td>xs</td>
                                                <td>&lt;576px</td>
                                            </tr>
                                            <tr>
                                                <td>Small</td>
                                                <td>sm</td>
                                                <td>≥576px</td>
                                            </tr>
                                            <tr>
                                                <td>Medium</td>
                                                <td>md</td>
                                                <td>≥768px</td>
                                            </tr>
                                            <tr>
                                                <td>Large</td>
                                                <td>lg</td>
                                                <td>≥992px</td>
                                            </tr>
                                            <tr>
                                                <td>Extra Large</td>
                                                <td>xl</td>
                                                <td>≥1200px</td>
                                            </tr>
                                        </tbody>
                                    </table>

                                    <h6>Mobile Optimization</h6>
                                    <ul class="small">
                                        <li><strong>Touch Targets:</strong> Minimum 44px for interactive elements</li>
                                        <li><strong>Viewport Meta:</strong> Proper mobile viewport configuration</li>
                                        <li><strong>Flexible Layouts:</strong> CSS Grid and Flexbox implementation</li>
                                        <li><strong>Image Optimization:</strong> Responsive images with srcset</li>
                                    </ul>
                                </div>
                                <div class="col-lg-6">
                                    <h6>Reusable UI Components</h6>
                                    <div class="code-block">
<pre><code class="language-html"><!-- ERDB Card Component -->
<div class="card erdb-card">
  <div class="card-header erdb-header">
    <h5><i class="fas fa-icon me-2"></i>Title</h5>
  </div>
  <div class="card-body">
    Content
  </div>
</div>

<!-- ERDB Button Variants -->
<button class="btn btn-erdb-primary">Primary Action</button>
<button class="btn btn-erdb-secondary">Secondary Action</button>
<button class="btn btn-erdb-success">Success Action</button>

<!-- ERDB Form Controls -->
<div class="form-group erdb-form-group">
  <label class="erdb-label">Label</label>
  <input class="form-control erdb-input">
</div></code></pre>
                                    </div>

                                    <h6>Design System Hierarchy</h6>
                                    <ul class="small">
                                        <li><strong>Typography:</strong> Consistent font scales and line heights</li>
                                        <li><strong>Spacing:</strong> 8px grid system for consistent spacing</li>
                                        <li><strong>Shadows:</strong> Layered elevation system</li>
                                        <li><strong>Animations:</strong> Smooth transitions and micro-interactions</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Deployment Section -->
                <section id="deployment" class="mt-5">
                    <div class="section-header">
                        <h1><i class="fas fa-server me-3"></i>Deployment & Operations</h1>
                        <p>Production deployment procedures, system requirements, and maintenance guidelines</p>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-cogs me-2"></i>System Requirements</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-6">
                                    <h6>Minimum Hardware Requirements</h6>
                                    <table class="table table-sm">
                                        <tr>
                                            <td><strong>CPU</strong></td>
                                            <td>4 cores, 2.5GHz+ (8 cores recommended)</td>
                                        </tr>
                                        <tr>
                                            <td><strong>RAM</strong></td>
                                            <td>16GB minimum (32GB recommended)</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Storage</strong></td>
                                            <td>100GB SSD minimum (500GB recommended)</td>
                                        </tr>
                                        <tr>
                                            <td><strong>GPU</strong></td>
                                            <td>Optional: NVIDIA GPU with 8GB+ VRAM</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Network</strong></td>
                                            <td>1Gbps connection recommended</td>
                                        </tr>
                                    </table>

                                    <h6>Software Dependencies</h6>
                                    <ul class="small">
                                        <li><strong>Python:</strong> 3.8+ (3.11 recommended)</li>
                                        <li><strong>Ollama:</strong> Latest version for AI models</li>
                                        <li><strong>SQLite:</strong> 3.35+ (included with Python)</li>
                                        <li><strong>Operating System:</strong> Ubuntu 20.04+, Windows 10+, macOS 11+</li>
                                    </ul>
                                </div>
                                <div class="col-lg-6">
                                    <h6>Environment Setup</h6>
                                    <div class="code-block">
<pre><code class="language-bash"># Create virtual environment
python -m venv venv
source venv/bin/activate  # Linux/Mac
# or
venv\Scripts\activate     # Windows

# Install dependencies
pip install -r requirements.txt

# Install Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# Pull required models
ollama pull llama3.1:8b-instruct-q4_K_M
ollama pull mxbai-embed-large:latest
ollama pull llama3.2-vision:11b-instruct-q4_K_M

# Install spaCy model
python -m spacy download en_core_web_sm</code></pre>
                                    </div>

                                    <h6>Environment Variables</h6>
                                    <div class="code-block">
<pre><code class="language-bash"># .env file
FLASK_ENV=production
SECRET_KEY=your-secret-key-here
OLLAMA_BASE_URL=http://localhost:11434
MAX_CONTENT_LENGTH=26214400  # 25MB
UPLOAD_FOLDER=uploads
TEMP_FOLDER=_temp
DATABASE_PATH=databases/
GEOIP_DATABASE_PATH=GeoLite2-City.mmdb</code></pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-rocket me-2"></i>Production Deployment</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-6">
                                    <h6>Step-by-Step Deployment</h6>
                                    <div class="code-block">
<pre><code class="language-bash"># 1. Clone repository
git clone https://github.com/your-org/document-management-system.git
cd document-management-system

# 2. Setup environment
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# 3. Configure environment
cp .env.example .env
# Edit .env with production values

# 4. Initialize databases
python init_databases.py

# 5. Download GeoIP database
wget https://download.maxmind.com/app/geoip_download?edition_id=GeoLite2-City&license_key=YOUR_KEY&suffix=tar.gz
tar -xzf GeoLite2-City.tar.gz

# 6. Setup Ollama and models
ollama serve &
ollama pull llama3.1:8b-instruct-q4_K_M
ollama pull mxbai-embed-large:latest
ollama pull llama3.2-vision:11b-instruct-q4_K_M

# 7. Start application
gunicorn --bind 0.0.0.0:8080 --workers 4 app:app</code></pre>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <h6>Database Initialization</h6>
                                    <div class="code-block">
<pre><code class="language-python"># init_databases.py
import sqlite3
import os

def initialize_databases():
    """Initialize all required databases"""

    # Create directories
    os.makedirs('databases', exist_ok=True)
    os.makedirs('uploads', exist_ok=True)
    os.makedirs('_temp', exist_ok=True)

    # Initialize user management database
    init_user_management_db()

    # Initialize chat history database
    init_chat_history_db()

    # Initialize content database
    init_content_db()

    # Create default admin user
    create_default_admin()

def create_default_admin():
    """Create default admin user"""
    # Implementation for creating admin user
    pass

if __name__ == "__main__":
    initialize_databases()</code></pre>
                                    </div>

                                    <h6>Production Configuration</h6>
                                    <div class="code-block">
<pre><code class="language-python"># production_config.py
import os

class ProductionConfig:
    SECRET_KEY = os.environ.get('SECRET_KEY')
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL')
    SQLALCHEMY_TRACK_MODIFICATIONS = False

    # Security settings
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'

    # File upload settings
    MAX_CONTENT_LENGTH = 26214400  # 25MB
    UPLOAD_FOLDER = 'uploads'

    # Rate limiting
    RATELIMIT_STORAGE_URL = 'redis://localhost:6379'</code></pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-shield-alt me-2"></i>Security & Monitoring</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-6">
                                    <h6>Security Checklist</h6>
                                    <ul class="small">
                                        <li><strong>SSL/TLS:</strong> Configure HTTPS with valid certificates</li>
                                        <li><strong>Firewall:</strong> Restrict access to necessary ports only</li>
                                        <li><strong>Environment Variables:</strong> Secure storage of secrets</li>
                                        <li><strong>Database Security:</strong> Regular backups and access controls</li>
                                        <li><strong>User Authentication:</strong> Strong password policies</li>
                                        <li><strong>Rate Limiting:</strong> Prevent abuse and DoS attacks</li>
                                        <li><strong>Input Validation:</strong> Sanitize all user inputs</li>
                                        <li><strong>CSRF Protection:</strong> Enable for all forms</li>
                                    </ul>

                                    <h6>Backup Procedures</h6>
                                    <div class="code-block">
<pre><code class="language-bash">#!/bin/bash
# backup.sh - Daily backup script

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/$DATE"

mkdir -p $BACKUP_DIR

# Backup databases
cp databases/*.db $BACKUP_DIR/
cp databases/*.sqlite $BACKUP_DIR/

# Backup uploads
tar -czf $BACKUP_DIR/uploads.tar.gz uploads/

# Backup ChromaDB
tar -czf $BACKUP_DIR/chromadb.tar.gz chromadb/

# Clean old backups (keep 30 days)
find /backups -type d -mtime +30 -exec rm -rf {} \;</code></pre>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <h6>Monitoring Setup</h6>
                                    <div class="code-block">
<pre><code class="language-python"># monitoring.py
import logging
import time
from functools import wraps

def monitor_performance(func):
    """Decorator to monitor function performance"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time

            # Log performance metrics
            logging.info(f"{func.__name__} executed in {execution_time:.2f}s")

            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logging.error(f"{func.__name__} failed after {execution_time:.2f}s: {str(e)}")
            raise
    return wrapper

# Health check endpoint
@app.route('/health')
def health_check():
    """System health check"""
    return {
        'status': 'healthy',
        'timestamp': time.time(),
        'version': '1.0.0',
        'services': {
            'ollama': check_ollama_status(),
            'database': check_database_status(),
            'chromadb': check_chromadb_status()
        }
    }</code></pre>
                                    </div>

                                    <h6>Log Management</h6>
                                    <ul class="small">
                                        <li><strong>Application Logs:</strong> Structured logging with JSON format</li>
                                        <li><strong>Access Logs:</strong> Nginx/Apache access logs</li>
                                        <li><strong>Error Logs:</strong> Centralized error tracking</li>
                                        <li><strong>Performance Logs:</strong> Response time and resource usage</li>
                                        <li><strong>Security Logs:</strong> Authentication and authorization events</li>
                                        <li><strong>Log Rotation:</strong> Automatic log rotation and archival</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-tools me-2"></i>Troubleshooting Guide</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-6">
                                    <h6>Common Issues & Solutions</h6>

                                    <div class="alert alert-warning">
                                        <h6><i class="fas fa-exclamation-triangle me-2"></i>Ollama Connection Issues</h6>
                                        <p><strong>Symptoms:</strong> "Connection refused" errors, model loading failures</p>
                                        <p><strong>Solutions:</strong></p>
                                        <ul class="small">
                                            <li>Check if Ollama service is running: <code>ollama list</code></li>
                                            <li>Restart Ollama: <code>ollama serve</code></li>
                                            <li>Verify model availability: <code>ollama pull model-name</code></li>
                                            <li>Check firewall settings for port 11434</li>
                                        </ul>
                                    </div>

                                    <div class="alert alert-danger">
                                        <h6><i class="fas fa-database me-2"></i>Database Lock Issues</h6>
                                        <p><strong>Symptoms:</strong> "Database is locked" errors</p>
                                        <p><strong>Solutions:</strong></p>
                                        <ul class="small">
                                            <li>Check for long-running transactions</li>
                                            <li>Restart the application</li>
                                            <li>Use WAL mode: <code>PRAGMA journal_mode=WAL;</code></li>
                                            <li>Implement connection pooling</li>
                                        </ul>
                                    </div>

                                    <div class="alert alert-info">
                                        <h6><i class="fas fa-memory me-2"></i>Memory Issues</h6>
                                        <p><strong>Symptoms:</strong> Out of memory errors, slow responses</p>
                                        <p><strong>Solutions:</strong></p>
                                        <ul class="small">
                                            <li>Increase system RAM</li>
                                            <li>Optimize model parameters</li>
                                            <li>Implement request queuing</li>
                                            <li>Use smaller model variants</li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <h6>Performance Optimization</h6>

                                    <div class="code-block">
<pre><code class="language-python"># Performance optimization tips

# 1. Database optimization
def optimize_database():
    """Optimize SQLite databases"""
    conn = sqlite3.connect('database.db')
    conn.execute('PRAGMA optimize;')
    conn.execute('PRAGMA vacuum;')
    conn.execute('PRAGMA journal_mode=WAL;')
    conn.close()

# 2. ChromaDB optimization
def optimize_chromadb():
    """Optimize vector database"""
    # Use persistent storage
    # Implement batch operations
    # Regular index optimization

# 3. Model optimization
def optimize_models():
    """Optimize AI model performance"""
    # Use quantized models (q4_K_M)
    # Implement model caching
    # Batch processing for embeddings</code></pre>
                                    </div>

                                    <h6>Diagnostic Commands</h6>
                                    <div class="code-block">
<pre><code class="language-bash"># System diagnostics
python -c "import app; app.run_diagnostics()"

# Check disk space
df -h

# Check memory usage
free -h

# Check process status
ps aux | grep python
ps aux | grep ollama

# Check network connectivity
curl -X GET http://localhost:11434/api/tags

# Check database integrity
sqlite3 databases/content_db.sqlite "PRAGMA integrity_check;"

# Monitor real-time logs
tail -f logs/application.log</code></pre>
                                    </div>

                                    <h6>Emergency Procedures</h6>
                                    <ul class="small">
                                        <li><strong>Service Restart:</strong> Graceful application restart</li>
                                        <li><strong>Database Recovery:</strong> Restore from latest backup</li>
                                        <li><strong>Model Fallback:</strong> Switch to lighter models</li>
                                        <li><strong>Emergency Contacts:</strong> System administrator contacts</li>
                                        <li><strong>Rollback Plan:</strong> Previous version deployment</li>
                                    </ul>
                                </div>
                            </div>

                            <h6>Support Resources</h6>
                            <div class="row">
                                <div class="col-lg-4">
                                    <h6>Documentation</h6>
                                    <ul class="small">
                                        <li><a href="#" class="text-decoration-none">Technical Documentation</a></li>
                                        <li><a href="#" class="text-decoration-none">API Reference</a></li>
                                        <li><a href="#" class="text-decoration-none">User Manual</a></li>
                                        <li><a href="#" class="text-decoration-none">FAQ</a></li>
                                    </ul>
                                </div>
                                <div class="col-lg-4">
                                    <h6>External Resources</h6>
                                    <ul class="small">
                                        <li><a href="https://ollama.ai/docs" class="text-decoration-none">Ollama Documentation</a></li>
                                        <li><a href="https://docs.trychroma.com/" class="text-decoration-none">ChromaDB Documentation</a></li>
                                        <li><a href="https://flask.palletsprojects.com/" class="text-decoration-none">Flask Documentation</a></li>
                                        <li><a href="https://getbootstrap.com/docs/5.3/" class="text-decoration-none">Bootstrap 5 Documentation</a></li>
                                    </ul>
                                </div>
                                <div class="col-lg-4">
                                    <h6>Community Support</h6>
                                    <ul class="small">
                                        <li><a href="#" class="text-decoration-none">GitHub Issues</a></li>
                                        <li><a href="#" class="text-decoration-none">Discussion Forum</a></li>
                                        <li><a href="#" class="text-decoration-none">Stack Overflow</a></li>
                                        <li><a href="#" class="text-decoration-none">Discord Community</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </main>
        </div>
    </div>

    <!-- Scroll to Top Button -->
    <button class="scroll-to-top" onclick="scrollToTop()" title="Scroll to Top">
        <i class="fas fa-chevron-up"></i>
    </button>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Prism.js for syntax highlighting -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>

    <script>
        // Theme toggle functionality
        function toggleTheme() {
            const body = document.body;
            const themeIcon = document.getElementById('theme-icon');

            if (body.classList.contains('dark-mode')) {
                body.classList.remove('dark-mode');
                themeIcon.classList.remove('fa-sun');
                themeIcon.classList.add('fa-moon');
                localStorage.setItem('theme', 'light');
            } else {
                body.classList.add('dark-mode');
                themeIcon.classList.remove('fa-moon');
                themeIcon.classList.add('fa-sun');
                localStorage.setItem('theme', 'dark');
            }
        }

        // Load saved theme
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = localStorage.getItem('theme');
            const themeIcon = document.getElementById('theme-icon');

            if (savedTheme === 'dark') {
                document.body.classList.add('dark-mode');
                themeIcon.classList.remove('fa-moon');
                themeIcon.classList.add('fa-sun');
            }
        });

        // Sidebar toggle for mobile
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('show');
        }

        // Smooth scrolling for navigation links
        document.addEventListener('DOMContentLoaded', function() {
            const navLinks = document.querySelectorAll('.sidebar .nav-link');

            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Remove active class from all links
                    navLinks.forEach(l => l.classList.remove('active'));

                    // Add active class to clicked link
                    this.classList.add('active');

                    // Smooth scroll to target
                    const targetId = this.getAttribute('href').substring(1);
                    const targetElement = document.getElementById(targetId);

                    if (targetElement) {
                        targetElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }

                    // Close sidebar on mobile
                    if (window.innerWidth <= 768) {
                        document.getElementById('sidebar').classList.remove('show');
                    }
                });
            });
        });

        // Scroll to top functionality
        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // Show/hide scroll to top button
        window.addEventListener('scroll', function() {
            const scrollButton = document.querySelector('.scroll-to-top');
            if (window.pageYOffset > 300) {
                scrollButton.classList.add('visible');
            } else {
                scrollButton.classList.remove('visible');
            }
        });

        // Update active navigation based on scroll position
        window.addEventListener('scroll', function() {
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('.sidebar .nav-link');

            let current = '';

            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (pageYOffset >= sectionTop - 200) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });
    </script>
</body>
</html>






<!-- <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Documentation - Document Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .doc-sidebar {
            background-color: #f8f9fa;
            min-height: 100vh;
            padding: 20px;
        }
        .doc-content {
            padding: 20px;
        }
        .doc-nav-link {
            color: #495057;
            text-decoration: none;
            padding: 8px 12px;
            display: block;
            border-radius: 4px;
            margin-bottom: 4px;
        }
        .doc-nav-link:hover {
            background-color: #e9ecef;
            color: #495057;
        }
        .doc-nav-link.active {
            background-color: #007bff;
            color: white;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 doc-sidebar">
                <h5 class="mb-4">
                    <i class="fas fa-book me-2"></i>Documentation
                </h5>
                <nav>
                    <a href="#overview" class="doc-nav-link active">Overview</a>
                    <a href="#getting-started" class="doc-nav-link">Getting Started</a>
                    <a href="#uploading-content" class="doc-nav-link">Uploading Content</a>
                    <a href="#managing-files" class="doc-nav-link">Managing Files</a>
                    <a href="#chat-interface" class="doc-nav-link">Chat Interface</a>
                    <a href="#analytics" class="doc-nav-link">Analytics</a>
                    <a href="#user-management" class="doc-nav-link">User Management</a>
                    <a href="#troubleshooting" class="doc-nav-link">Troubleshooting</a>
                </nav>
                <hr>
                <a href="/admin/dashboard" class="btn btn-primary btn-sm">
                    <i class="fas fa-arrow-left me-2"></i>Back to Admin
                </a>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 doc-content">
                <section id="overview">
                    <h1>Document Management System</h1>
                    <p class="lead">A comprehensive AI-powered document management and chat system.</p>
                    
                    <h2>Features</h2>
                    <ul>
                        <li><strong>PDF Upload & Processing:</strong> Upload PDF documents with automatic text extraction and vectorization</li>
                        <li><strong>URL Scraping:</strong> Add web content by URL with configurable depth crawling</li>
                        <li><strong>AI-Powered Chat:</strong> Query your documents using natural language with multiple AI models</li>
                        <li><strong>Category Organization:</strong> Organize content into categories for better management</li>
                        <li><strong>User Management:</strong> Role-based access control with permissions</li>
                        <li><strong>Analytics:</strong> Comprehensive usage analytics and performance metrics</li>
                    </ul>
                </section>

                <section id="getting-started" class="mt-5">
                    <h2>Getting Started</h2>
                    <p>Follow these steps to start using the Document Management System:</p>
                    
                    <ol>
                        <li><strong>Login:</strong> Access the admin dashboard at <code>/admin</code></li>
                        <li><strong>Create Categories:</strong> Organize your content by creating categories</li>
                        <li><strong>Upload Content:</strong> Add PDFs or URLs to your knowledge base</li>
                        <li><strong>Configure Models:</strong> Set up AI models in the Model Settings</li>
                        <li><strong>Start Chatting:</strong> Use the chat interface to query your documents</li>
                    </ol>
                </section>

                <section id="uploading-content" class="mt-5">
                    <h2>Uploading Content</h2>
                    
                    <h3>PDF Upload</h3>
                    <p>Upload PDF documents to make them searchable:</p>
                    <div class="code-block">
                        1. Navigate to Content Management → Upload Content<br>
                        2. Select a category (create one if needed)<br>
                        3. Choose your PDF file (max 25MB)<br>
                        4. Optionally add a source URL<br>
                        5. Configure vision model settings if needed<br>
                        6. Click Upload
                    </div>

                    <h3>URL Scraping</h3>
                    <p>Add web content by URL:</p>
                    <div class="code-block">
                        1. Enter the URL in the URL field<br>
                        2. Set crawl depth (0-3 levels)<br>
                        3. Select category<br>
                        4. Click Upload
                    </div>
                </section>

                <section id="managing-files" class="mt-5">
                    <h2>Managing Files</h2>
                    <p>View and manage your uploaded content:</p>
                    
                    <ul>
                        <li><strong>View Files:</strong> See all uploaded PDFs and URLs by category</li>
                        <li><strong>Vector Data:</strong> Examine how content is chunked and vectorized</li>
                        <li><strong>Delete Content:</strong> Remove files and all associated data</li>
                        <li><strong>Source URLs:</strong> View original sources for uploaded content</li>
                    </ul>
                </section>

                <section id="chat-interface" class="mt-5">
                    <h2>Chat Interface</h2>
                    <p>Query your documents using natural language:</p>
                    
                    <h3>Features</h3>
                    <ul>
                        <li>Category-based queries</li>
                        <li>Anti-hallucination modes</li>
                        <li>Source citations</li>
                        <li>Image display from documents</li>
                        <li>Follow-up question suggestions</li>
                    </ul>

                    <h3>Usage Tips</h3>
                    <div class="code-block">
                        • Be specific in your questions<br>
                        • Use the category filter to narrow results<br>
                        • Check source citations for accuracy<br>
                        • Try different anti-hallucination modes for better results
                    </div>
                </section>

                <section id="analytics" class="mt-5">
                    <h2>Analytics</h2>
                    <p>Monitor system usage and performance:</p>
                    
                    <ul>
                        <li><strong>Usage Statistics:</strong> Track queries, users, and popular content</li>
                        <li><strong>Performance Metrics:</strong> Monitor response times and model performance</li>
                        <li><strong>Geographic Data:</strong> See where users are accessing from</li>
                        <li><strong>Chat History:</strong> Review past conversations</li>
                        <li><strong>Session Management:</strong> Monitor active user sessions</li>
                    </ul>
                </section>

                <section id="user-management" class="mt-5">
                    <h2>User Management</h2>
                    <p>Manage users and permissions:</p>
                    
                    <h3>Roles</h3>
                    <ul>
                        <li><strong>Admin:</strong> Full system access</li>
                        <li><strong>Editor:</strong> Content management and analytics</li>
                        <li><strong>Viewer:</strong> Read-only access to analytics</li>
                    </ul>

                    <h3>Permissions</h3>
                    <p>Fine-grained control over dashboard functions:</p>
                    <ul>
                        <li>Upload Content</li>
                        <li>Manage Files</li>
                        <li>Model Settings</li>
                        <li>Chat History</li>
                        <li>User Management</li>
                    </ul>
                </section>

                <section id="troubleshooting" class="mt-5">
                    <h2>Troubleshooting</h2>
                    
                    <h3>Common Issues</h3>
                    <div class="accordion" id="troubleshootingAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#upload-issues">
                                    Upload Issues
                                </button>
                            </h2>
                            <div id="upload-issues" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                                <div class="accordion-body">
                                    <ul>
                                        <li>Check file size (max 25MB for PDFs)</li>
                                        <li>Ensure category is selected</li>
                                        <li>Verify file format (PDF only)</li>
                                        <li>Check network connection for URL scraping</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#chat-issues">
                                    Chat Issues
                                </button>
                            </h2>
                            <div id="chat-issues" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                                <div class="accordion-body">
                                    <ul>
                                        <li>Ensure content is uploaded and processed</li>
                                        <li>Check if AI models are configured</li>
                                        <li>Try different categories</li>
                                        <li>Verify network connectivity</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <section id="support" class="mt-5">
                    <h2>Support</h2>
                    <p>Need help? Contact our support team:</p>
                    <ul>
                        <li><strong>Email:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></li>
                        <li><strong>System Info:</strong> Use the Help menu in the admin interface</li>
                        <li><strong>Chat Interface:</strong> <a href="/" target="_blank">Try the chat interface</a></li>
                    </ul>
                </section>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('.doc-nav-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);
                if (targetElement) {
                    targetElement.scrollIntoView({ behavior: 'smooth' });
                    
                    // Update active link
                    document.querySelectorAll('.doc-nav-link').forEach(l => l.classList.remove('active'));
                    this.classList.add('active');
                }
            });
        });

        // Update active link on scroll
        window.addEventListener('scroll', function() {
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('.doc-nav-link');
            
            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (window.pageYOffset >= sectionTop - 200) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });
    </script>
</body>
</html>
 -->