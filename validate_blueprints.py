#!/usr/bin/env python3
"""
Simple Blueprint Validation Script

Quick validation of blueprint structure without complex dependencies.
"""

import os
import sys

def test_basic_imports():
    """Test basic imports step by step."""
    print("Testing basic imports...")
    
    try:
        # Test Flask import
        from flask import Flask, Blueprint
        print("✅ Flask imported successfully")
        
        # Test individual blueprint imports
        print("\nTesting individual blueprint imports:")
        
        try:
            from blueprints.core import core_bp
            print("✅ Core blueprint imported")
        except Exception as e:
            print(f"❌ Core blueprint failed: {e}")
        
        try:
            from blueprints.auth import auth_bp
            print("✅ Auth blueprint imported")
        except Exception as e:
            print(f"❌ Auth blueprint failed: {e}")
        
        try:
            from blueprints.admin import admin_bp
            print("✅ Admin blueprint imported")
        except Exception as e:
            print(f"❌ Admin blueprint failed: {e}")
        
        try:
            from blueprints.file_management import file_management_bp
            print("✅ File management blueprint imported")
        except Exception as e:
            print(f"❌ File management blueprint failed: {e}")
        
        try:
            from blueprints.api import api_bp
            print("✅ API blueprint imported")
        except Exception as e:
            print(f"❌ API blueprint failed: {e}")
        
        try:
            from blueprints.analytics import analytics_bp
            print("✅ Analytics blueprint imported")
        except Exception as e:
            print(f"❌ Analytics blueprint failed: {e}")
        
        # Test main blueprints import
        try:
            from blueprints import ALL_BLUEPRINTS, register_blueprints
            print(f"✅ Main blueprints package imported - {len(ALL_BLUEPRINTS)} blueprints found")
        except Exception as e:
            print(f"❌ Main blueprints package failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Basic import test failed: {e}")
        return False

def test_file_structure():
    """Test that all required files exist."""
    print("\nTesting file structure...")
    
    required_files = [
        'blueprints/__init__.py',
        'blueprints/core.py',
        'blueprints/auth.py',
        'blueprints/admin.py',
        'blueprints/file_management.py',
        'blueprints/api.py',
        'blueprints/analytics.py',
        'session_utils.py',
        'app_blueprints.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - MISSING")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n⚠️  Missing files: {missing_files}")
        return False
    else:
        print("\n✅ All required files present")
        return True

def count_lines():
    """Count lines in original vs new structure."""
    print("\nCounting lines...")
    
    # Original app.py
    original_lines = 0
    if os.path.exists('app.py'):
        with open('app.py', 'r', encoding='utf-8') as f:
            original_lines = len(f.readlines())
        print(f"📄 Original app.py: {original_lines} lines")
    
    # New main app
    new_main_lines = 0
    if os.path.exists('app_blueprints.py'):
        with open('app_blueprints.py', 'r', encoding='utf-8') as f:
            new_main_lines = len(f.readlines())
        print(f"📄 New app_blueprints.py: {new_main_lines} lines")
    
    # Calculate reduction
    if original_lines > 0 and new_main_lines > 0:
        reduction = ((original_lines - new_main_lines) / original_lines) * 100
        print(f"📊 Main file complexity reduction: {reduction:.1f}%")
        
        if reduction >= 50:
            print("✅ Target reduction achieved (≥50%)")
            return True
        else:
            print("⚠️  Target reduction not yet achieved")
            return False
    
    return True

def main():
    """Run validation tests."""
    print("🧪 Blueprint Structure Validation")
    print("=" * 50)
    
    tests = [
        ("File Structure", test_file_structure),
        ("Basic Imports", test_basic_imports),
        ("Line Count Analysis", count_lines)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        print("-" * 30)
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - PASSED")
            else:
                print(f"❌ {test_name} - FAILED")
        except Exception as e:
            print(f"❌ {test_name} - ERROR: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 Blueprint structure validation successful!")
        print("\n✅ Phase 2 Step 2.1 - Blueprint Structure Creation COMPLETED")
    else:
        print("⚠️  Some validation tests failed")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
