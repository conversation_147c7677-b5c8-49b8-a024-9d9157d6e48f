/**
 * Voice Interface Test Suite
 * Comprehensive testing functions for voice interaction capabilities
 */

(function() {
    'use strict';

    // Test suite namespace
    window.VoiceInterfaceTest = {
        
        /**
         * Run all voice interface tests
         */
        runAllTests: function() {
            console.log('🎤 Starting Voice Interface Test Suite...');
            
            const tests = [
                this.testBrowserSupport,
                this.testInitialization,
                this.testVoiceLoading,
                this.testSettingsPanel,
                this.testSpeechControls,
                this.testContentProcessing,
                this.testSettingsPersistence,
                this.testAccessibility
            ];
            
            let passed = 0;
            let failed = 0;
            
            tests.forEach((test, index) => {
                try {
                    const result = test.call(this);
                    if (result) {
                        console.log(`✅ Test ${index + 1}: ${test.name} - PASSED`);
                        passed++;
                    } else {
                        console.log(`❌ Test ${index + 1}: ${test.name} - FAILED`);
                        failed++;
                    }
                } catch (error) {
                    console.error(`💥 Test ${index + 1}: ${test.name} - ERROR:`, error);
                    failed++;
                }
            });
            
            console.log(`\n📊 Test Results: ${passed} passed, ${failed} failed`);
            return { passed, failed, total: tests.length };
        },
        
        /**
         * Test browser support for Web Speech API
         */
        testBrowserSupport: function() {
            const hasWebSpeech = 'speechSynthesis' in window && 'SpeechSynthesisUtterance' in window;
            console.log(`Browser support: ${hasWebSpeech ? 'Supported' : 'Not supported'}`);
            return hasWebSpeech;
        },
        
        /**
         * Test VoiceInterface initialization
         */
        testInitialization: function() {
            const isInitialized = typeof VoiceInterface !== 'undefined' && 
                                 VoiceInterface.state && 
                                 VoiceInterface.state.isInitialized;
            console.log(`VoiceInterface initialized: ${isInitialized}`);
            return isInitialized;
        },
        
        /**
         * Test voice loading
         */
        testVoiceLoading: function() {
            if (!VoiceInterface.state.isSupported) {
                console.log('Skipping voice loading test - not supported');
                return true;
            }
            
            const hasVoices = VoiceInterface.state.availableVoices.length > 0;
            console.log(`Available voices: ${VoiceInterface.state.availableVoices.length}`);
            
            if (hasVoices) {
                VoiceInterface.state.availableVoices.slice(0, 3).forEach(voice => {
                    console.log(`  - ${voice.name} (${voice.lang})`);
                });
            }
            
            return hasVoices;
        },
        
        /**
         * Test settings panel functionality
         */
        testSettingsPanel: function() {
            const panel = document.getElementById('voice-settings-panel');
            const button = document.getElementById('voice-settings-toggle');
            
            if (!panel || !button) {
                console.log('Settings panel or button not found');
                return false;
            }
            
            // Test panel toggle
            const initiallyHidden = panel.classList.contains('hidden');
            VoiceInterface.toggleSettingsPanel();
            const afterToggle = panel.classList.contains('hidden');
            VoiceInterface.toggleSettingsPanel(); // Reset
            
            const toggleWorks = initiallyHidden !== afterToggle;
            console.log(`Settings panel toggle works: ${toggleWorks}`);
            
            // Test form controls
            const voiceSelect = document.getElementById('voice-select');
            const rateSlider = document.getElementById('speech-rate');
            const volumeSlider = document.getElementById('speech-volume');
            const autoPlayCheckbox = document.getElementById('auto-play-responses');
            
            const controlsExist = voiceSelect && rateSlider && volumeSlider && autoPlayCheckbox;
            console.log(`All form controls exist: ${controlsExist}`);
            
            return toggleWorks && controlsExist;
        },
        
        /**
         * Test speech controls functionality
         */
        testSpeechControls: function() {
            if (!VoiceInterface.state.isSupported) {
                console.log('Skipping speech controls test - not supported');
                return true;
            }
            
            // Create a test message element
            const testMessage = document.createElement('div');
            testMessage.className = 'welcome-message';
            testMessage.innerHTML = `
                <div class="welcome-message-content">
                    <p>This is a test message for voice interface testing.</p>
                </div>
            `;
            
            // Add speech controls
            const messageId = 'test_message_' + Date.now();
            const messageText = 'This is a test message for voice interface testing.';
            
            VoiceInterface.addSpeechControls(testMessage, messageId, messageText);
            
            // Check if controls were added
            const speechButton = testMessage.querySelector('.speech-control-btn');
            const progressBar = testMessage.querySelector('.speech-progress');
            
            const controlsAdded = speechButton && progressBar;
            console.log(`Speech controls added: ${controlsAdded}`);
            
            // Clean up
            testMessage.remove();
            
            return controlsAdded;
        },
        
        /**
         * Test content processing
         */
        testContentProcessing: function() {
            const testCases = [
                {
                    input: '**Bold text** and *italic text* with `code`',
                    expected: 'Bold text and italic text with code'
                },
                {
                    input: '[Link text](http://example.com)',
                    expected: 'Link text'
                },
                {
                    input: '# Header\n\nParagraph with\n\nmultiple lines',
                    expected: 'Header. Paragraph with. multiple lines'
                },
                {
                    input: 'Scientific name: *Homo sapiens*',
                    expected: 'Scientific name: Homo sapiens'
                }
            ];
            
            let allPassed = true;
            
            testCases.forEach((testCase, index) => {
                const result = VoiceInterface.processTextForSpeech(testCase.input);
                const passed = result.trim() === testCase.expected.trim();
                
                if (!passed) {
                    console.log(`Content processing test ${index + 1} failed:`);
                    console.log(`  Input: "${testCase.input}"`);
                    console.log(`  Expected: "${testCase.expected}"`);
                    console.log(`  Got: "${result}"`);
                    allPassed = false;
                }
            });
            
            console.log(`Content processing tests: ${allPassed ? 'All passed' : 'Some failed'}`);
            return allPassed;
        },
        
        /**
         * Test settings persistence
         */
        testSettingsPersistence: function() {
            // Save current settings
            const originalSettings = localStorage.getItem('voiceSettings');
            
            // Test saving settings
            const testSettings = {
                defaultRate: 1.5,
                defaultVolume: 0.7,
                autoPlay: true,
                language: 'en-US'
            };
            
            VoiceInterface.config = { ...VoiceInterface.config, ...testSettings };
            VoiceInterface.saveSettings();
            
            // Test loading settings
            VoiceInterface.config = {}; // Reset
            VoiceInterface.loadSettings();
            
            const settingsMatch = VoiceInterface.config.defaultRate === testSettings.defaultRate &&
                                 VoiceInterface.config.defaultVolume === testSettings.defaultVolume &&
                                 VoiceInterface.config.autoPlay === testSettings.autoPlay;
            
            console.log(`Settings persistence: ${settingsMatch ? 'Working' : 'Failed'}`);
            
            // Restore original settings
            if (originalSettings) {
                localStorage.setItem('voiceSettings', originalSettings);
            } else {
                localStorage.removeItem('voiceSettings');
            }
            
            return settingsMatch;
        },
        
        /**
         * Test accessibility features
         */
        testAccessibility: function() {
            const panel = document.getElementById('voice-settings-panel');
            if (!panel) {
                console.log('Settings panel not found for accessibility test');
                return false;
            }
            
            // Check ARIA labels
            const speechButtons = document.querySelectorAll('.speech-control-btn');
            const hasAriaLabels = Array.from(speechButtons).every(btn => 
                btn.hasAttribute('aria-label') || btn.hasAttribute('title')
            );
            
            // Check keyboard navigation
            const focusableElements = panel.querySelectorAll(
                'button, input, select, textarea, [tabindex]:not([tabindex="-1"])'
            );
            const hasFocusableElements = focusableElements.length > 0;
            
            // Check minimum touch target size (44px)
            const touchTargets = document.querySelectorAll('.speech-control-btn, .voice-settings-button');
            const hasProperTouchTargets = Array.from(touchTargets).every(element => {
                const rect = element.getBoundingClientRect();
                return rect.width >= 44 && rect.height >= 44;
            });
            
            const accessibilityPassed = hasAriaLabels && hasFocusableElements && hasProperTouchTargets;
            
            console.log(`Accessibility features:`);
            console.log(`  ARIA labels: ${hasAriaLabels}`);
            console.log(`  Focusable elements: ${hasFocusableElements}`);
            console.log(`  Touch targets: ${hasProperTouchTargets}`);
            
            return accessibilityPassed;
        },
        
        /**
         * Test speech synthesis (requires user interaction)
         */
        testSpeechSynthesis: function() {
            if (!VoiceInterface.state.isSupported) {
                console.log('Speech synthesis not supported');
                return false;
            }
            
            console.log('🔊 Testing speech synthesis (you should hear audio)...');
            
            const testText = 'This is a test of the voice interface speech synthesis functionality.';
            const success = VoiceInterface.speak(testText, 'test_speech');
            
            console.log(`Speech synthesis test: ${success ? 'Started' : 'Failed'}`);
            return success;
        },
        
        /**
         * Generate test report
         */
        generateReport: function() {
            const results = this.runAllTests();
            
            const report = {
                timestamp: new Date().toISOString(),
                browser: navigator.userAgent,
                results: results,
                voiceInterface: {
                    supported: VoiceInterface.state.isSupported,
                    initialized: VoiceInterface.state.isInitialized,
                    availableVoices: VoiceInterface.state.availableVoices.length,
                    currentSettings: VoiceInterface.config
                }
            };
            
            console.log('\n📋 Test Report:', report);
            return report;
        }
    };
    
    // Auto-run tests if in development mode
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                console.log('🧪 Auto-running voice interface tests in development mode...');
                VoiceInterfaceTest.runAllTests();
            }, 2000);
        });
    }

})();
