/**
 * Simplified Admin.js - Admin Interface JavaScript
 * 
 * This file contains admin interface specific functions that use the consolidated
 * DMS utilities. Common functions (API, CSRF, toast) are now handled by dms-core.js
 * 
 * Dependencies: dms-core.js, utilities-consolidated.js
 */

// Category management
async function createCategory() {
    const categoryInput = document.getElementById("newCategory");
    if (!categoryInput) {
        showToast("Category input not found.", "error");
        return;
    }
    
    const category = categoryInput.value.trim();
    if (!category) {
        showToast("Category name cannot be empty.", "error");
        return;
    }
    
    try {
        const { ok, json } = await api('/admin/categories', {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ category })
        });
        
        showToast(ok ? json.message : json.error, ok ? "success" : "error");
        
        if (ok) {
            categoryInput.value = "";
            location.reload();
        }
    } catch (error) {
        showToast("Error creating category", "error");
    }
}

async function deleteCategory(category) {
    if (!category) {
        showToast("Category name is required.", "error");
        return;
    }
    
    // Confirm deletion
    const confirmed = await DMSUtils.confirm(
        `Are you sure you want to delete the category "${category}"? This action cannot be undone.`,
        {
            title: 'Delete Category',
            confirmText: 'Delete',
            dangerous: true
        }
    );
    
    if (!confirmed) return;
    
    try {
        const { ok, json } = await api(`/admin/categories/${encodeURIComponent(category)}`, {
            method: "DELETE"
        });
        
        showToast(ok ? json.message : json.error, ok ? "success" : "error");
        
        if (ok) {
            location.reload();
        }
    } catch (error) {
        showToast("Error deleting category", "error");
    }
}

// File management
async function deleteFile(filename) {
    if (!filename) {
        showToast("Filename is required.", "error");
        return;
    }
    
    // Confirm deletion
    const confirmed = await DMSUtils.confirm(
        `Are you sure you want to delete "${filename}"? This action cannot be undone.`,
        {
            title: 'Delete File',
            confirmText: 'Delete',
            dangerous: true
        }
    );
    
    if (!confirmed) return;
    
    try {
        const { ok, json } = await api(`/admin/files/${encodeURIComponent(filename)}`, {
            method: "DELETE"
        });
        
        showToast(ok ? json.message : json.error, ok ? "success" : "error");
        
        if (ok) {
            location.reload();
        }
    } catch (error) {
        showToast("Error deleting file", "error");
    }
}

// Bulk operations
async function deleteSelectedFiles() {
    const checkboxes = document.querySelectorAll('input[name="selected_files"]:checked');
    
    if (checkboxes.length === 0) {
        showToast("Please select files to delete.", "warning");
        return;
    }
    
    const filenames = Array.from(checkboxes).map(cb => cb.value);
    
    // Confirm bulk deletion
    const confirmed = await DMSUtils.confirm(
        `Are you sure you want to delete ${filenames.length} selected file(s)? This action cannot be undone.`,
        {
            title: 'Delete Selected Files',
            confirmText: 'Delete All',
            dangerous: true
        }
    );
    
    if (!confirmed) return;
    
    try {
        const { ok, json } = await api('/admin/files/bulk-delete', {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ filenames })
        });
        
        showToast(ok ? json.message : json.error, ok ? "success" : "error");
        
        if (ok) {
            location.reload();
        }
    } catch (error) {
        showToast("Error deleting selected files", "error");
    }
}

// File selection utilities
function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('select-all');
    const fileCheckboxes = document.querySelectorAll('input[name="selected_files"]');
    
    if (selectAllCheckbox && fileCheckboxes.length > 0) {
        const isChecked = selectAllCheckbox.checked;
        fileCheckboxes.forEach(checkbox => {
            checkbox.checked = isChecked;
        });
        
        updateBulkActionButtons();
    }
}

function updateBulkActionButtons() {
    const selectedCheckboxes = document.querySelectorAll('input[name="selected_files"]:checked');
    const bulkActionButtons = document.querySelectorAll('.bulk-action-btn');
    
    const hasSelection = selectedCheckboxes.length > 0;
    
    bulkActionButtons.forEach(button => {
        button.disabled = !hasSelection;
        if (hasSelection) {
            button.classList.remove('disabled');
        } else {
            button.classList.add('disabled');
        }
    });
    
    // Update selection count display
    const selectionCount = document.getElementById('selection-count');
    if (selectionCount) {
        selectionCount.textContent = selectedCheckboxes.length;
    }
}

// System information modal
function showSystemInfo() {
    const systemInfo = {
        'User Agent': navigator.userAgent,
        'Platform': navigator.platform,
        'Language': navigator.language,
        'Screen Resolution': `${screen.width}x${screen.height}`,
        'Viewport Size': `${window.innerWidth}x${window.innerHeight}`,
        'Local Storage Available': typeof(Storage) !== "undefined",
        'Cookies Enabled': navigator.cookieEnabled,
        'Online Status': navigator.onLine ? 'Online' : 'Offline',
        'Current Time': new Date().toLocaleString()
    };
    
    let infoHtml = '<div class="system-info-grid">';
    Object.entries(systemInfo).forEach(([key, value]) => {
        infoHtml += `
            <div class="system-info-item">
                <strong>${key}:</strong> ${value}
            </div>
        `;
    });
    infoHtml += '</div>';
    
    // Create or update system info modal
    let modal = document.getElementById('systemInfoModal');
    if (!modal) {
        const modalHtml = `
            <div class="modal fade" id="systemInfoModal" tabindex="-1" aria-labelledby="systemInfoModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="systemInfoModalLabel">System Information</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            ${infoHtml}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        modal = document.getElementById('systemInfoModal');
    } else {
        modal.querySelector('.modal-body').innerHTML = infoHtml;
    }
    
    // Show the modal
    const modalInstance = new bootstrap.Modal(modal);
    modalInstance.show();
}

// User settings modal
function showUserSettings() {
    const settingsHtml = `
        <div class="user-settings-grid">
            <div class="setting-item">
                <label for="theme-preference">Theme Preference:</label>
                <select id="theme-preference" class="form-select">
                    <option value="auto">Auto (System)</option>
                    <option value="light">Light</option>
                    <option value="dark">Dark</option>
                </select>
            </div>
            <div class="setting-item">
                <label for="notification-preference">Notifications:</label>
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="notification-preference" checked>
                    <label class="form-check-label" for="notification-preference">
                        Enable toast notifications
                    </label>
                </div>
            </div>
        </div>
    `;
    
    // Create or update user settings modal
    let modal = document.getElementById('userSettingsModal');
    if (!modal) {
        const modalHtml = `
            <div class="modal fade" id="userSettingsModal" tabindex="-1" aria-labelledby="userSettingsModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="userSettingsModalLabel">User Settings</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            ${settingsHtml}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-primary" onclick="saveUserSettings()">Save Settings</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        modal = document.getElementById('userSettingsModal');
    } else {
        modal.querySelector('.modal-body').innerHTML = settingsHtml;
    }
    
    // Load current settings
    const themePreference = document.getElementById('theme-preference');
    const currentTheme = localStorage.getItem('theme') || 'auto';
    if (themePreference) {
        themePreference.value = currentTheme;
    }
    
    // Show the modal
    const modalInstance = new bootstrap.Modal(modal);
    modalInstance.show();
}

function saveUserSettings() {
    const themePreference = document.getElementById('theme-preference');
    const notificationPreference = document.getElementById('notification-preference');
    
    if (themePreference) {
        const selectedTheme = themePreference.value;
        if (selectedTheme === 'auto') {
            localStorage.removeItem('theme');
            // Apply system preference
            const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
            DMSCore.theme.set(prefersDark ? 'dark' : 'light');
        } else {
            DMSCore.theme.set(selectedTheme);
        }
    }
    
    if (notificationPreference) {
        localStorage.setItem('notifications-enabled', notificationPreference.checked);
    }
    
    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('userSettingsModal'));
    modal.hide();
    
    showToast('Settings saved successfully', 'success');
}

// Initialize admin interface
document.addEventListener('DOMContentLoaded', function() {
    // Set up file selection handlers
    const selectAllCheckbox = document.getElementById('select-all');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', toggleSelectAll);
    }
    
    // Set up individual file checkbox handlers
    const fileCheckboxes = document.querySelectorAll('input[name="selected_files"]');
    fileCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActionButtons);
    });
    
    // Initial update of bulk action buttons
    updateBulkActionButtons();
    
    console.log('Admin interface initialized');
});

// Global functions for backward compatibility
window.createCategory = createCategory;
window.deleteCategory = deleteCategory;
window.deleteFile = deleteFile;
window.deleteSelectedFiles = deleteSelectedFiles;
window.toggleSelectAll = toggleSelectAll;
window.showSystemInfo = showSystemInfo;
window.showUserSettings = showUserSettings;
window.saveUserSettings = saveUserSettings;
