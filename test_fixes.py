#!/usr/bin/env python3
"""
Test script to verify the fixes for CSRF token and location extraction issues.
"""

import sys
import os
import sqlite3
import logging

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_location_type_validation():
    """Test the location type validation in save_extracted_location function."""
    print("=== Testing Location Type Validation ===")
    
    try:
        from db_utils import save_extracted_location
        
        # Test valid location types
        valid_test_cases = [
            {'location_text': 'Manila', 'location_type': 'city'},
            {'location_text': 'Barangay San Antonio', 'location_type': 'barangay'},
            {'location_text': 'Los Baños Municipality', 'location_type': 'municipality'},
            {'location_text': 'University of the Philippines', 'location_type': 'landmark'},
            {'location_text': '14.1648, 121.2413', 'location_type': 'coordinates'},
        ]
        
        # Test invalid location types
        invalid_test_cases = [
            {'location_text': 'Dictyota', 'location_type': 'invalid_type'},
            {'location_text': 'Phaeophyceae', 'location_type': 'species'},
            {'location_text': '', 'location_type': 'city'},  # Empty text
            {'location_text': 'A', 'location_type': 'city'},  # Too short
        ]
        
        print("Testing valid location types:")
        for i, test_case in enumerate(valid_test_cases, 1):
            result = save_extracted_location(test_case)
            status = "✓ PASSED" if result is not None else "✗ FAILED"
            print(f"  {i}. {test_case['location_text']} ({test_case['location_type']}): {status}")
        
        print("\nTesting invalid location types (should be rejected):")
        for i, test_case in enumerate(invalid_test_cases, 1):
            result = save_extracted_location(test_case)
            status = "✓ PASSED" if result is None else "✗ FAILED"
            print(f"  {i}. {test_case['location_text']} ({test_case['location_type']}): {status}")
        
        print("Location type validation test completed.\n")
        
    except Exception as e:
        print(f"Error testing location type validation: {str(e)}\n")

def test_location_extraction_filtering():
    """Test the improved location extraction filtering."""
    print("=== Testing Location Extraction Filtering ===")
    
    try:
        from location_extractor import LocationExtractor
        
        extractor = LocationExtractor()
        
        # Test text with scientific terms that should be filtered out
        test_text = """
        The study was conducted in Barangay San Antonio, Los Baños, Laguna.
        Samples of Dictyota and other Phaeophyceae were collected.
        Macroalgae species including Sargassum were analyzed.
        The research was done at the University of the Philippines.
        Coordinates 14.1648, 121.2413 mark the study site.
        """
        
        print("Extracting locations from test text...")
        locations = extractor.extract_locations_from_text(test_text)
        
        print(f"Found {len(locations)} locations:")
        for i, location in enumerate(locations, 1):
            print(f"  {i}. '{location['location_text']}' (type: {location['location_type']}, confidence: {location['confidence_score']:.2f})")
        
        # Check if scientific terms were properly filtered out
        location_texts = [loc['location_text'].lower() for loc in locations]
        scientific_terms = ['dictyota', 'phaeophyceae', 'macroalgae', 'sargassum']
        
        filtered_correctly = True
        for term in scientific_terms:
            if any(term in text for text in location_texts):
                print(f"  ✗ FAILED: Scientific term '{term}' was not filtered out")
                filtered_correctly = False
        
        if filtered_correctly:
            print("  ✓ PASSED: Scientific terms were properly filtered out")
        
        # Check if valid locations were preserved
        expected_locations = ['barangay san antonio', 'los baños', 'laguna', 'university of the philippines']
        found_expected = 0
        for expected in expected_locations:
            if any(expected in text for text in location_texts):
                found_expected += 1
        
        if found_expected >= 2:  # At least some expected locations should be found
            print("  ✓ PASSED: Valid geographical locations were preserved")
        else:
            print("  ✗ FAILED: Expected geographical locations were not found")
        
        print("Location extraction filtering test completed.\n")
        
    except Exception as e:
        print(f"Error testing location extraction filtering: {str(e)}\n")

def test_database_schema():
    """Test that the database schema has the correct CHECK constraint."""
    print("=== Testing Database Schema ===")
    
    try:
        from db_schema import DB_PATH
        
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Get the table schema
        cursor.execute("SELECT sql FROM sqlite_master WHERE type='table' AND name='extracted_locations'")
        schema = cursor.fetchone()
        
        if schema:
            schema_sql = schema[0]
            print("Current extracted_locations table schema:")
            print(schema_sql)
            
            # Check if all required location types are in the CHECK constraint
            required_types = ['place_name', 'address', 'coordinates', 'landmark', 'region', 'municipality', 'city', 'barangay']
            all_types_present = all(location_type in schema_sql for location_type in required_types)
            
            if all_types_present:
                print("✓ PASSED: All required location types are in the CHECK constraint")
            else:
                print("✗ FAILED: Some required location types are missing from the CHECK constraint")
                missing_types = [t for t in required_types if t not in schema_sql]
                print(f"Missing types: {missing_types}")
        else:
            print("✗ FAILED: extracted_locations table not found")
        
        conn.close()
        print("Database schema test completed.\n")
        
    except Exception as e:
        print(f"Error testing database schema: {str(e)}\n")

def main():
    """Run all tests."""
    print("Starting comprehensive test of fixes...\n")
    
    test_database_schema()
    test_location_type_validation()
    test_location_extraction_filtering()
    
    print("All tests completed!")

if __name__ == "__main__":
    main()
