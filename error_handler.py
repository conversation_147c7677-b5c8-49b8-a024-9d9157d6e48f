"""
Standardized Error Handling Module for Document Management System
This module provides consistent error handling patterns across all modules.
"""

import logging
import traceback
from typing import Dict, Any, Optional, Union
from flask import jsonify
from functools import wraps

# Configure logging for error handling
logger = logging.getLogger(__name__)

class DMSError(Exception):
    """Base exception class for Document Management System errors."""
    
    def __init__(self, message: str, error_code: str = None, details: Dict[str, Any] = None):
        self.message = message
        self.error_code = error_code or "DMS_ERROR"
        self.details = details or {}
        super().__init__(self.message)

class DatabaseError(DMSError):
    """Exception for database-related errors."""
    
    def __init__(self, message: str, details: Dict[str, Any] = None):
        super().__init__(message, "DATABASE_ERROR", details)

class FileProcessingError(DMSError):
    """Exception for file processing errors."""
    
    def __init__(self, message: str, details: Dict[str, Any] = None):
        super().__init__(message, "FILE_PROCESSING_ERROR", details)

class ValidationError(DMSError):
    """Exception for validation errors."""
    
    def __init__(self, message: str, details: Dict[str, Any] = None):
        super().__init__(message, "VALIDATION_ERROR", details)

class AuthenticationError(DMSError):
    """Exception for authentication errors."""
    
    def __init__(self, message: str, details: Dict[str, Any] = None):
        super().__init__(message, "AUTHENTICATION_ERROR", details)

class ConfigurationError(DMSError):
    """Exception for configuration errors."""
    
    def __init__(self, message: str, details: Dict[str, Any] = None):
        super().__init__(message, "CONFIGURATION_ERROR", details)

def standardized_error_response(error: Union[Exception, DMSError], 
                               status_code: int = 500,
                               include_traceback: bool = False) -> Dict[str, Any]:
    """
    Create a standardized error response dictionary.
    
    Args:
        error: The exception that occurred
        status_code: HTTP status code
        include_traceback: Whether to include traceback in response
        
    Returns:
        Dictionary containing standardized error information
    """
    if isinstance(error, DMSError):
        response = {
            "success": False,
            "error": error.message,
            "error_code": error.error_code,
            "details": error.details,
            "status": "error"
        }
    else:
        response = {
            "success": False,
            "error": str(error),
            "error_code": "UNKNOWN_ERROR",
            "details": {},
            "status": "error"
        }
    
    if include_traceback:
        response["traceback"] = traceback.format_exc()
    
    # Log the error
    logger.error(f"Error occurred: {response['error']}", extra={
        "error_code": response.get("error_code"),
        "details": response.get("details"),
        "status_code": status_code
    })
    
    return response

def handle_database_error(func):
    """
    Decorator for handling database errors consistently.
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            error_msg = f"Database operation failed in {func.__name__}: {str(e)}"
            logger.error(error_msg, exc_info=True)
            raise DatabaseError(error_msg, {"function": func.__name__, "args": str(args)})
    return wrapper

def handle_file_processing_error(func):
    """
    Decorator for handling file processing errors consistently.
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            error_msg = f"File processing failed in {func.__name__}: {str(e)}"
            logger.error(error_msg, exc_info=True)
            raise FileProcessingError(error_msg, {"function": func.__name__, "args": str(args)})
    return wrapper

def handle_api_error(func):
    """
    Decorator for handling API errors consistently and returning JSON responses.
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except DMSError as e:
            response = standardized_error_response(e, status_code=400)
            return jsonify(response), 400
        except Exception as e:
            response = standardized_error_response(e, status_code=500)
            return jsonify(response), 500
    return wrapper

def log_and_return_error(error: Exception, 
                        context: str = "",
                        return_value: Any = None,
                        log_level: str = "error") -> Any:
    """
    Log an error and return a specified value.
    
    Args:
        error: The exception that occurred
        context: Additional context about where the error occurred
        return_value: Value to return after logging
        log_level: Logging level (error, warning, info)
        
    Returns:
        The specified return_value
    """
    error_msg = f"{context}: {str(error)}" if context else str(error)
    
    if log_level == "error":
        logger.error(error_msg, exc_info=True)
    elif log_level == "warning":
        logger.warning(error_msg)
    elif log_level == "info":
        logger.info(error_msg)
    
    return return_value

def safe_execute(func, *args, default_return=None, log_errors=True, **kwargs):
    """
    Safely execute a function and return a default value if it fails.
    
    Args:
        func: Function to execute
        *args: Arguments for the function
        default_return: Value to return if function fails
        log_errors: Whether to log errors
        **kwargs: Keyword arguments for the function
        
    Returns:
        Function result or default_return if function fails
    """
    try:
        return func(*args, **kwargs)
    except Exception as e:
        if log_errors:
            logger.error(f"Safe execution failed for {func.__name__}: {str(e)}", exc_info=True)
        return default_return

def validate_required_fields(data: Dict[str, Any], required_fields: list) -> None:
    """
    Validate that required fields are present in data.
    
    Args:
        data: Dictionary to validate
        required_fields: List of required field names
        
    Raises:
        ValidationError: If any required fields are missing
    """
    missing_fields = [field for field in required_fields if field not in data or data[field] is None]
    
    if missing_fields:
        raise ValidationError(
            f"Missing required fields: {', '.join(missing_fields)}",
            {"missing_fields": missing_fields, "provided_fields": list(data.keys())}
        )

def setup_error_logging(log_level: str = "INFO", log_file: Optional[str] = None) -> None:
    """
    Set up standardized error logging configuration.
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Optional log file path
    """
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Set up console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    
    # Set up file handler if specified
    handlers = [console_handler]
    if log_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(formatter)
        handlers.append(file_handler)
    
    # Configure root logger
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        handlers=handlers,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    logger.info("Error logging configured successfully")

# Initialize error logging
setup_error_logging()
