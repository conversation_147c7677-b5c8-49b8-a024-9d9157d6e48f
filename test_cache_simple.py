#!/usr/bin/env python3
"""
Simple cache test to verify implementation
"""

import time
import sys
import os

def test_cache_import():
    """Test if cache service can be imported"""
    try:
        from services.cache_service import get_cache_service, cached
        print("✓ Cache service imported successfully")
        return True
    except ImportError as e:
        print(f"✗ Failed to import cache service: {e}")
        return False
    except Exception as e:
        print(f"✗ Error importing cache service: {e}")
        return False

def test_cache_basic():
    """Test basic cache functionality"""
    try:
        from services.cache_service import get_cache_service
        
        cache = get_cache_service()
        print(f"✓ Cache service initialized with backend: {cache.backend_name}")
        
        # Test basic operations
        cache.set("test_key", "test_value", ttl=60)
        value = cache.get("test_key")
        
        if value == "test_value":
            print("✓ Basic cache set/get working")
            return True
        else:
            print(f"✗ Cache value mismatch: expected 'test_value', got '{value}'")
            return False
            
    except Exception as e:
        print(f"✗ Cache basic test failed: {e}")
        return False

def test_cached_decorator():
    """Test the @cached decorator"""
    try:
        from services.cache_service import cached
        
        call_count = 0
        
        @cached(ttl=60)
        def test_function(x):
            nonlocal call_count
            call_count += 1
            return x * 2
        
        # First call
        result1 = test_function(5)
        first_count = call_count
        
        # Second call (should be cached)
        result2 = test_function(5)
        second_count = call_count
        
        if result1 == result2 == 10 and first_count == 1 and second_count == 1:
            print("✓ @cached decorator working correctly")
            return True
        else:
            print(f"✗ @cached decorator failed: results={result1},{result2}, counts={first_count},{second_count}")
            return False
            
    except Exception as e:
        print(f"✗ @cached decorator test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("Testing Phase 3 Step 1: Query Result Caching Implementation")
    print("=" * 60)
    
    tests = [
        ("Cache Import", test_cache_import),
        ("Cache Basic Operations", test_cache_basic),
        ("Cached Decorator", test_cached_decorator),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\nTesting: {test_name}")
        if test_func():
            passed += 1
        else:
            print(f"Failed: {test_name}")
    
    print("\n" + "=" * 60)
    print(f"Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All basic cache tests passed!")
        return True
    else:
        print(f"❌ {total - passed} tests failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
