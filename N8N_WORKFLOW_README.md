# Document Management System N8N Workflow

## Overview

This N8N workflow JSON file represents the complete document management system's core processes and integrations. It provides a visual, automated workflow that can be imported into N8N to replicate the system's functionality using a no-code/low-code approach.

## Workflow Components

### 1. Document Processing Pipeline
- **Document Upload Webhook**: Receives PDF uploads via HTTP POST
- **File Type Validation**: Ensures only PDF files are processed
- **Duplicate Check**: Prevents duplicate document uploads
- **PDF Text & Image Extraction**: Processes PDFs to extract content
- **Vision Analysis**: Uses Llama 3.2 Vision model for image analysis
- **Text Embeddings**: Generates embeddings using mxbai-embed-large model
- **Location Extraction**: Extracts Philippine geographical locations (Municipality, City, Barangay)

### 2. Database Operations
- **SQLite Integration**: CRUD operations on all database tables
- **PDF Documents**: Stores document metadata and relationships
- **Source URLs**: Manages URL associations with PDFs
- **Cover Images**: Handles document thumbnail management
- **Extracted Locations**: Stores geographical data with cascading deletion
- **Chat History**: Comprehensive conversation logging
- **Analytics**: Usage tracking and performance metrics

### 3. AI Model Integration
- **Ollama API Integration**: Connects to local Ollama instance
- **LLM Chat**: Uses llama3.1:8b-instruct-q4_K_M for responses
- **Embedding Generation**: mxbai-embed-large for vector embeddings
- **Vision Processing**: llama3.2-vision for image analysis
- **ChromaDB Integration**: Vector database operations

### 4. User Management Flow
- **User Registration**: Complete validation and account creation
- **Authentication**: Session-based login system
- **Password Hashing**: Secure password storage
- **Activity Logging**: Comprehensive audit trail
- **Role-Based Access**: Permission management

### 5. Chat Interface Logic
- **Category-Based Chat**: Organized by document categories
- **Anti-Hallucination Modes**: Configurable response strictness
- **Follow-up Questions**: Dynamic question generation
- **Citation Management**: Source tracking and display
- **Session Management**: Device fingerprinting and persistence

### 6. Analytics and Tracking
- **Geolocation Tracking**: IP-based location detection
- **Usage Analytics**: Detailed interaction metrics
- **Model Performance**: AI model effectiveness tracking
- **Real-time Monitoring**: System health and performance

### 7. File Management
- **Hierarchical Structure**: Organized temporary directories
- **Automatic Cleanup**: Resource management on deletion
- **Cascading Deletion**: Complete resource removal
- **Error Handling**: Comprehensive error responses

## Prerequisites

### Required Services
1. **N8N Instance**: Version 1.0+ with webhook support
2. **Ollama Server**: Running on localhost:11434 with models:
   - llama3.1:8b-instruct-q4_K_M
   - mxbai-embed-large:latest
   - llama3.2-vision:11b-instruct-q4_K_M
3. **ChromaDB Server**: Running on localhost:8000
4. **SQLite Databases**: Accessible file paths for:
   - Main database (pdf_documents, source_urls, etc.)
   - User management database
   - Chat history database

### Required N8N Nodes
- HTTP Request nodes
- Webhook nodes
- SQLite nodes
- Code (JavaScript) nodes
- IF condition nodes
- Respond to Webhook nodes

## Installation Instructions

### 1. Import Workflow
1. Open your N8N instance
2. Go to Workflows → Import from File
3. Select the `n8n_document_management_workflow.json` file
4. Click Import

### 2. Configure Database Connections
Update SQLite node configurations with your database paths:
```
Database Path: /path/to/your/database.db
User Database Path: /path/to/your/user_management.db
Chat Database Path: /path/to/your/chat_history.db
```

### 3. Configure API Endpoints
Update HTTP Request nodes with correct URLs:
- Ollama API: `http://localhost:11434`
- ChromaDB API: `http://localhost:8000`
- Flask App API: `http://localhost:5000` (if using existing system)

### 4. Set Environment Variables
Configure the following in your N8N environment:
```
OLLAMA_BASE_URL=http://localhost:11434
CHROMA_PATH=./chroma
TEMP_FOLDER=./_temp
LLM_MODEL=llama3.1:8b-instruct-q4_K_M
TEXT_EMBEDDING_MODEL=mxbai-embed-large:latest
VISION_MODEL=llama3.2-vision:11b-instruct-q4_K_M
```

## Webhook Endpoints

Once activated, the workflow provides these webhook endpoints:

### Document Upload
```
POST /webhook-document-upload
Content-Type: multipart/form-data

Body:
- file: PDF file
- category: Document category
- source_url: Optional source URL
- use_vision: Boolean for vision analysis
- filter_sensitivity: 0.0-1.0 for image filtering
- max_images: Maximum images to process
```

### Chat Query
```
POST /webhook-chat-query
Content-Type: application/json

Body:
{
  "category": "research",
  "question": "What are the key findings?",
  "anti_hallucination_mode": "strict",
  "session_token": "user_session_token",
  "client_name": "John Doe"
}
```

### User Management
```
POST /webhook-user-management
Content-Type: application/json

Body (Registration):
{
  "action": "register",
  "username": "newuser",
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "full_name": "New User"
}

Body (Login):
{
  "action": "login",
  "username": "existinguser",
  "password": "UserPass123!"
}
```

### Document Deletion
```
DELETE /webhook-delete-document
Content-Type: application/json

Body:
{
  "document_id": 123
}
```

## Error Handling

The workflow includes comprehensive error handling:

- **File Validation Errors**: Returns 400 for invalid file types
- **Duplicate Detection**: Returns 409 for duplicate files
- **Authentication Errors**: Returns 401 for invalid sessions
- **Validation Errors**: Returns 400 for invalid user data
- **Processing Errors**: Detailed error responses with troubleshooting info

## Monitoring and Debugging

### Execution Logs
Monitor workflow executions in N8N:
1. Go to Executions tab
2. View detailed logs for each node
3. Check error messages and data flow

### Performance Metrics
Track key metrics:
- Document processing time
- AI model response time
- Database query performance
- Error rates by node

## Customization

### Adding New Models
To add support for additional AI models:
1. Update the "Check Available Models" node
2. Add new model options in conditional nodes
3. Configure new HTTP Request nodes for model APIs

### Extending Database Schema
To add new database tables:
1. Create new SQLite nodes for table operations
2. Update connection flows
3. Add corresponding cleanup operations

### Custom Validation Rules
Modify JavaScript code nodes to implement:
- Custom file validation rules
- Enhanced user validation
- Business-specific logic

## Troubleshooting

### Common Issues

1. **Webhook Not Responding**
   - Check N8N webhook URLs are accessible
   - Verify workflow is activated
   - Check firewall settings

2. **Database Connection Errors**
   - Verify SQLite file paths
   - Check file permissions
   - Ensure database schema exists

3. **AI Model Errors**
   - Confirm Ollama is running
   - Verify models are downloaded
   - Check API endpoint URLs

4. **ChromaDB Issues**
   - Ensure ChromaDB server is running
   - Verify collection names match
   - Check vector dimensions

### Debug Mode
Enable debug mode in N8N:
1. Set LOG_LEVEL=debug in environment
2. Enable "Save manual executions"
3. Use "Execute Workflow" for testing

## Security Considerations

- **API Authentication**: Implement proper authentication for webhooks
- **Input Validation**: All user inputs are validated before processing
- **SQL Injection Prevention**: Parameterized queries used throughout
- **File Upload Security**: File type and size validation implemented
- **Session Management**: Secure token generation and validation

## Performance Optimization

- **Batch Processing**: Group operations where possible
- **Async Operations**: Use parallel execution for independent tasks
- **Caching**: Implement caching for frequently accessed data
- **Resource Cleanup**: Automatic cleanup of temporary resources

## Support and Maintenance

For issues or questions:
1. Check N8N execution logs
2. Review error responses from nodes
3. Verify all prerequisite services are running
4. Consult the original Flask application documentation

This workflow provides a complete, production-ready implementation of the document management system using N8N's visual workflow capabilities.
