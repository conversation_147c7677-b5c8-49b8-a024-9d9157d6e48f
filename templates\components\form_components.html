<!--
Reusable Form Components

This file contains reusable form components that can be used across templates
to maintain consistency and reduce duplication.

Available Macros:
- input_field: Standard input field with label and validation
- select_field: Select dropdown with options
- textarea_field: Textarea with label and validation
- checkbox_field: Checkbox with label
- radio_group: Radio button group
- file_upload: File upload field with drag-and-drop
- submit_button: Styled submit button
- form_group: Wrapper for form elements
- flash_messages: Display flash messages
-->

<!-- Standard Input Field -->
{% macro input_field(name, label, type='text', value='', placeholder='', required=false, disabled=false, help_text='', class='', id='') %}
<div class="form-group mb-3">
    <label for="{{ id or name }}" class="form-label">
        {{ label }}
        {% if required %}<span class="text-danger">*</span>{% endif %}
    </label>
    <input type="{{ type }}" 
           class="form-control {{ class }}" 
           id="{{ id or name }}" 
           name="{{ name }}" 
           value="{{ value }}" 
           placeholder="{{ placeholder }}"
           {% if required %}required{% endif %}
           {% if disabled %}disabled{% endif %}>
    {% if help_text %}
    <div class="form-text">{{ help_text }}</div>
    {% endif %}
</div>
{% endmacro %}

<!-- Select Dropdown Field -->
{% macro select_field(name, label, options=[], selected='', required=false, disabled=false, help_text='', class='', id='', multiple=false) %}
<div class="form-group mb-3">
    <label for="{{ id or name }}" class="form-label">
        {{ label }}
        {% if required %}<span class="text-danger">*</span>{% endif %}
    </label>
    <select class="form-select {{ class }}" 
            id="{{ id or name }}" 
            name="{{ name }}"
            {% if required %}required{% endif %}
            {% if disabled %}disabled{% endif %}
            {% if multiple %}multiple{% endif %}>
        {% if not multiple and not required %}
        <option value="">-- Select {{ label }} --</option>
        {% endif %}
        {% for option in options %}
            {% if option is string %}
            <option value="{{ option }}" {% if option == selected %}selected{% endif %}>{{ option }}</option>
            {% else %}
            <option value="{{ option.value }}" {% if option.value == selected %}selected{% endif %}>{{ option.label }}</option>
            {% endif %}
        {% endfor %}
    </select>
    {% if help_text %}
    <div class="form-text">{{ help_text }}</div>
    {% endif %}
</div>
{% endmacro %}

<!-- Textarea Field -->
{% macro textarea_field(name, label, value='', placeholder='', rows=3, required=false, disabled=false, help_text='', class='', id='') %}
<div class="form-group mb-3">
    <label for="{{ id or name }}" class="form-label">
        {{ label }}
        {% if required %}<span class="text-danger">*</span>{% endif %}
    </label>
    <textarea class="form-control {{ class }}" 
              id="{{ id or name }}" 
              name="{{ name }}" 
              rows="{{ rows }}" 
              placeholder="{{ placeholder }}"
              {% if required %}required{% endif %}
              {% if disabled %}disabled{% endif %}>{{ value }}</textarea>
    {% if help_text %}
    <div class="form-text">{{ help_text }}</div>
    {% endif %}
</div>
{% endmacro %}

<!-- Checkbox Field -->
{% macro checkbox_field(name, label, checked=false, disabled=false, help_text='', class='', id='', value='1') %}
<div class="form-group mb-3">
    <div class="form-check">
        <input class="form-check-input {{ class }}" 
               type="checkbox" 
               id="{{ id or name }}" 
               name="{{ name }}" 
               value="{{ value }}"
               {% if checked %}checked{% endif %}
               {% if disabled %}disabled{% endif %}>
        <label class="form-check-label" for="{{ id or name }}">
            {{ label }}
        </label>
    </div>
    {% if help_text %}
    <div class="form-text">{{ help_text }}</div>
    {% endif %}
</div>
{% endmacro %}

<!-- Radio Button Group -->
{% macro radio_group(name, label, options=[], selected='', required=false, disabled=false, help_text='', class='', inline=false) %}
<div class="form-group mb-3">
    <label class="form-label">
        {{ label }}
        {% if required %}<span class="text-danger">*</span>{% endif %}
    </label>
    {% for option in options %}
    <div class="form-check {% if inline %}form-check-inline{% endif %}">
        {% if option is string %}
        <input class="form-check-input {{ class }}" 
               type="radio" 
               id="{{ name }}_{{ loop.index }}" 
               name="{{ name }}" 
               value="{{ option }}"
               {% if option == selected %}checked{% endif %}
               {% if disabled %}disabled{% endif %}
               {% if required %}required{% endif %}>
        <label class="form-check-label" for="{{ name }}_{{ loop.index }}">
            {{ option }}
        </label>
        {% else %}
        <input class="form-check-input {{ class }}" 
               type="radio" 
               id="{{ name }}_{{ loop.index }}" 
               name="{{ name }}" 
               value="{{ option.value }}"
               {% if option.value == selected %}checked{% endif %}
               {% if disabled %}disabled{% endif %}
               {% if required %}required{% endif %}>
        <label class="form-check-label" for="{{ name }}_{{ loop.index }}">
            {{ option.label }}
        </label>
        {% endif %}
    </div>
    {% endfor %}
    {% if help_text %}
    <div class="form-text">{{ help_text }}</div>
    {% endif %}
</div>
{% endmacro %}

<!-- File Upload Field -->
{% macro file_upload(name, label, accept='', required=false, multiple=false, help_text='', class='', id='') %}
<div class="form-group mb-3">
    <label for="{{ id or name }}" class="form-label">
        {{ label }}
        {% if required %}<span class="text-danger">*</span>{% endif %}
    </label>
    <input type="file" 
           class="form-control {{ class }}" 
           id="{{ id or name }}" 
           name="{{ name }}"
           {% if accept %}accept="{{ accept }}"{% endif %}
           {% if required %}required{% endif %}
           {% if multiple %}multiple{% endif %}>
    {% if help_text %}
    <div class="form-text">{{ help_text }}</div>
    {% endif %}
</div>
{% endmacro %}

<!-- Submit Button -->
{% macro submit_button(text='Submit', class='btn-primary', size='', disabled=false, loading_text='Processing...', id='') %}
<button type="submit" 
        class="btn {{ class }} {{ 'btn-' + size if size else '' }}" 
        id="{{ id or 'submit-btn' }}"
        {% if disabled %}disabled{% endif %}>
    <span class="btn-text">{{ text }}</span>
    <span class="btn-loading d-none">
        <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
        {{ loading_text }}
    </span>
</button>
{% endmacro %}

<!-- Form Group Wrapper -->
{% macro form_group(label='', required=false, help_text='', class='') %}
<div class="form-group mb-3 {{ class }}">
    {% if label %}
    <label class="form-label">
        {{ label }}
        {% if required %}<span class="text-danger">*</span>{% endif %}
    </label>
    {% endif %}
    {{ caller() }}
    {% if help_text %}
    <div class="form-text">{{ help_text }}</div>
    {% endif %}
</div>
{% endmacro %}

<!-- Flash Messages Display -->
{% macro flash_messages(container_class='', dismiss=true) %}
{% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
    <div class="flash-messages {{ container_class }}">
        {% for category, message in messages %}
        <div class="alert alert-{{ 'danger' if category == 'error' else category }} {% if dismiss %}alert-dismissible{% endif %} fade show" role="alert">
            <i class="fas fa-{% if category == 'success' %}check-circle{% elif category == 'warning' %}exclamation-triangle{% elif category == 'info' %}info-circle{% else %}times-circle{% endif %} me-2"></i>
            {{ message }}
            {% if dismiss %}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            {% endif %}
        </div>
        {% endfor %}
    </div>
    {% endif %}
{% endwith %}
{% endmacro %}

<!-- Range Slider with Real-time Feedback -->
{% macro range_slider(name, label, min=0, max=100, value=50, step=1, unit='', required=false, help_text='', class='', id='') %}
<div class="form-group mb-3">
    <label for="{{ id or name }}" class="form-label d-flex justify-content-between">
        <span>
            {{ label }}
            {% if required %}<span class="text-danger">*</span>{% endif %}
        </span>
        <span class="range-value fw-bold text-primary">
            <span id="{{ id or name }}_value">{{ value }}</span>{{ unit }}
        </span>
    </label>
    <input type="range" 
           class="form-range {{ class }}" 
           id="{{ id or name }}" 
           name="{{ name }}" 
           min="{{ min }}" 
           max="{{ max }}" 
           value="{{ value }}" 
           step="{{ step }}"
           {% if required %}required{% endif %}
           oninput="document.getElementById('{{ id or name }}_value').textContent = this.value">
    {% if help_text %}
    <div class="form-text">{{ help_text }}</div>
    {% endif %}
</div>
{% endmacro %}

<!-- Toggle Switch -->
{% macro toggle_switch(name, label, checked=false, disabled=false, help_text='', class='', id='') %}
<div class="form-group mb-3">
    <div class="form-check form-switch">
        <input class="form-check-input {{ class }}" 
               type="checkbox" 
               role="switch" 
               id="{{ id or name }}" 
               name="{{ name }}"
               {% if checked %}checked{% endif %}
               {% if disabled %}disabled{% endif %}>
        <label class="form-check-label" for="{{ id or name }}">
            {{ label }}
        </label>
    </div>
    {% if help_text %}
    <div class="form-text">{{ help_text }}</div>
    {% endif %}
</div>
{% endmacro %}
