import os
import logging
import json
import requests
from dotenv import load_dotenv
from functools import wraps

# Load environment variables from .env file first, before importing other modules
load_dotenv()

from flask import Flask, request, jsonify, render_template, flash, redirect, url_for, send_file, session
from werkzeug.utils import secure_filename
from query import query_category
from embed import embed_file, scrape_url
import utils
import db_utils
import geo_utils
import geoip_analytics
from get_vector_db import get_vector_db, OLLAMA_BASE_URL
from utils import list_categories, delete_file, check_duplicate_pdf
import ollama
from create_temp_dirs import create_temp_directories
from langchain_ollama.embeddings import OllamaEmbeddings
from db_embed import embed_file_db_first, scrape_url_db_first
import user_management as um
from user_routes import user_bp
from greeting_manager import GreetingManager
from error_handler import (
    standardized_error_response, handle_api_error, handle_database_error,
    DatabaseError, FileProcessingError, ValidationError, safe_execute,
    log_and_return_error
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.secret_key = os.getenv("FLASK_SECRET_KEY", "1qazxsw23edcvfr4")
app.config['MAX_CONTENT_LENGTH'] = 64 * 1024 * 1024  # 25MB max file size

# CSRF Configuration
app.config['WTF_CSRF_ENABLED'] = os.getenv('WTF_CSRF_ENABLED', 'true').lower() == 'true'
app.config['WTF_CSRF_TIME_LIMIT'] = int(os.getenv('WTF_CSRF_TIME_LIMIT', '7200'))  # 2 hours default
app.config['WTF_CSRF_SSL_STRICT'] = os.getenv('WTF_CSRF_SSL_STRICT', 'false').lower() == 'true'
app.config['WTF_CSRF_CHECK_DEFAULT'] = os.getenv('WTF_CSRF_CHECK_DEFAULT', 'true').lower() == 'true'

# Session Configuration
app.config['PERMANENT_SESSION_LIFETIME'] = int(os.getenv('SESSION_TIMEOUT_MINUTES', '120')) * 60  # 2 hours default

TEMP_FOLDER = os.getenv("TEMP_FOLDER", "./_temp")
CHROMA_PATH = os.getenv("CHROMA_PATH", "./chroma")

# Initialize user management
login_manager = um.init_login_manager(app)
csrf = um.init_csrf(app)
limiter = um.init_limiter(app)

# Exempt static files from rate limiting
@limiter.request_filter
def exempt_static_files():
    """Exempt static files from rate limiting."""
    return request.path.startswith('/static/')

# Admin authentication decorator
def admin_required(f):
    """Decorator to require admin authentication for routes."""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Skip authentication for the admin dashboard (which handles login)
        if request.endpoint == 'admin_dashboard':
            return f(*args, **kwargs)

        # For all other admin routes, require authentication
        if not um.current_user.is_authenticated:
            # Check if this is an API request
            if request.path.startswith('/api/') or request.is_json or 'application/json' in request.headers.get('Accept', ''):
                return jsonify({"error": "Authentication required", "redirect": url_for('admin_dashboard')}), 401
            else:
                flash('Please log in to access this page.', 'error')
                return redirect(url_for('admin_dashboard'))
        return f(*args, **kwargs)
    return decorated_function

# Function permission decorator
def function_permission_required(function_name):
    """Decorator to require specific dashboard function permission."""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Skip permission check for admin dashboard
            if request.endpoint == 'admin_dashboard':
                return f(*args, **kwargs)

            # Require authentication
            if not um.current_user.is_authenticated:
                # Check if this is an API request
                if request.path.startswith('/api/') or request.is_json or 'application/json' in request.headers.get('Accept', ''):
                    return jsonify({"error": "Authentication required", "redirect": url_for('admin_dashboard')}), 401
                else:
                    flash('Please log in to access this page.', 'error')
                    return redirect(url_for('admin_dashboard'))

            # Check if user has permission for this function
            if not um.current_user.has_dashboard_permission(function_name) and um.current_user.role != 'admin':
                # Check if this is an API request
                if request.path.startswith('/api/') or request.is_json or 'application/json' in request.headers.get('Accept', ''):
                    return jsonify({"error": f"Permission denied for {function_name.replace('_', ' ').title()}", "redirect": url_for('admin_dashboard')}), 403
                else:
                    flash(f'You do not have permission to access {function_name.replace("_", " ").title()}.', 'error')
                    return redirect(url_for('admin_dashboard'))

            return f(*args, **kwargs)
        return decorated_function
    return decorator

# Register user blueprint
app.register_blueprint(user_bp)

# Global error handlers for API requests
@app.errorhandler(404)
def handle_404(error):
    """Handle 404 errors with JSON response for API requests."""
    if request.path.startswith('/api/') or request.is_json or 'application/json' in request.headers.get('Accept', ''):
        return jsonify({"error": "Not found", "status": 404}), 404
    # For non-API requests, let Flask handle it normally
    return error

@app.errorhandler(500)
def handle_500(error):
    """Handle 500 errors with JSON response for API requests."""
    if request.path.startswith('/api/') or request.is_json or 'application/json' in request.headers.get('Accept', ''):
        return jsonify({"error": "Internal server error", "status": 500}), 500
    # For non-API requests, let Flask handle it normally
    return error

@app.errorhandler(403)
def handle_403(error):
    """Handle 403 errors with JSON response for API requests."""
    if request.path.startswith('/api/') or request.is_json or 'application/json' in request.headers.get('Accept', ''):
        return jsonify({"error": "Forbidden", "status": 403}), 403
    # For non-API requests, let Flask handle it normally
    return error

# Create temporary directories for PDF images and tables
create_temp_directories()

# Default model and embedding
default_llm = 'llama3.1:8b-instruct-q4_K_M'
default_embedding = "mxbai-embed-large:latest"
default_vision = "llama3.2-vision:11b-instruct-q4_K_M"  # Can also use "gemma3:4b-it-q4_K_M" or "gemma3:12b-it-q4_K_M" for vision capabilities

# Path to store default model settings
DEFAULT_MODELS_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'default_models.json')

# Function to load default models from file
def load_default_models():
    global default_llm, default_embedding, default_vision

    try:
        if os.path.exists(DEFAULT_MODELS_FILE):
            with open(DEFAULT_MODELS_FILE, 'r') as f:
                defaults = json.load(f)

                # Update default values if they exist in the file
                if 'llm_model' in defaults:
                    default_llm = defaults['llm_model']
                    logger.info(f"Loaded default LLM model from file: {default_llm}")

                if 'embedding_model' in defaults:
                    default_embedding = defaults['embedding_model']
                    logger.info(f"Loaded default embedding model from file: {default_embedding}")

                if 'vision_model' in defaults:
                    default_vision = defaults['vision_model']
                    logger.info(f"Loaded default vision model from file: {default_vision}")

                # Load vision toggle state if it exists
                if 'use_vision_model' in defaults:
                    use_vision = defaults['use_vision_model']
                    use_vision_str = 'true' if use_vision else 'false'
                    os.environ['USE_VISION_MODEL'] = use_vision_str
                    logger.info(f"Loaded vision model enabled setting: {use_vision}")

                # Load vision during embedding toggle state if it exists
                if 'use_vision_model_during_embedding' in defaults:
                    use_vision_during_embedding = defaults['use_vision_model_during_embedding']
                    use_vision_during_embedding_str = 'true' if use_vision_during_embedding else 'false'
                    os.environ['USE_VISION_MODEL_DURING_EMBEDDING'] = use_vision_during_embedding_str
                    logger.info(f"Loaded vision model during embedding enabled setting: {use_vision_during_embedding}")

                # Load image filtering settings if they exist
                if 'filter_pdf_images' in defaults:
                    filter_pdf_images = defaults['filter_pdf_images']
                    filter_pdf_images_str = 'true' if filter_pdf_images else 'false'
                    os.environ['FILTER_PDF_IMAGES'] = filter_pdf_images_str
                    logger.info(f"Loaded PDF image filtering setting: {filter_pdf_images}")

                if 'filter_sensitivity' in defaults:
                    filter_sensitivity = defaults['filter_sensitivity']
                    if filter_sensitivity in ['low', 'medium', 'high']:
                        os.environ['PDF_IMAGE_FILTER_SENSITIVITY'] = filter_sensitivity
                        logger.info(f"Loaded PDF image filter sensitivity: {filter_sensitivity}")

                if 'max_pdf_images' in defaults:
                    max_pdf_images = defaults['max_pdf_images']
                    if isinstance(max_pdf_images, int) and 1 <= max_pdf_images <= 50:
                        os.environ['MAX_PDF_IMAGES_TO_ANALYZE'] = str(max_pdf_images)
                        logger.info(f"Loaded maximum PDF images to analyze: {max_pdf_images}")

                if 'show_filtered_images' in defaults:
                    show_filtered_images = defaults['show_filtered_images']
                    show_filtered_images_str = 'true' if show_filtered_images else 'false'
                    os.environ['SHOW_FILTERED_IMAGES'] = show_filtered_images_str
                    logger.info(f"Loaded show filtered images setting: {show_filtered_images}")

                # Load model parameters if available
                if 'model_parameters' in defaults:
                    model_params = defaults['model_parameters']

                    # Load temperature
                    if 'temperature' in model_params:
                        temperature = model_params['temperature']
                        if isinstance(temperature, (int, float)) and 0 <= temperature <= 1:
                            os.environ['LLM_TEMPERATURE'] = str(temperature)
                            logger.info(f"Loaded LLM temperature: {temperature}")

                    # Load context window size
                    if 'num_ctx' in model_params:
                        num_ctx = model_params['num_ctx']
                        if isinstance(num_ctx, int) and num_ctx > 0:
                            os.environ['LLM_NUM_CTX'] = str(num_ctx)
                            logger.info(f"Loaded LLM context window size: {num_ctx}")

                    # Load max tokens to predict
                    if 'num_predict' in model_params:
                        num_predict = model_params['num_predict']
                        if isinstance(num_predict, int) and num_predict > 0:
                            os.environ['LLM_NUM_PREDICT'] = str(num_predict)
                            logger.info(f"Loaded LLM max tokens to predict: {num_predict}")

                    # Load top_p
                    if 'top_p' in model_params:
                        top_p = model_params['top_p']
                        if isinstance(top_p, (int, float)) and 0 <= top_p <= 1:
                            os.environ['LLM_TOP_P'] = str(top_p)
                            logger.info(f"Loaded LLM top_p: {top_p}")

                    # Load top_k
                    if 'top_k' in model_params:
                        top_k = model_params['top_k']
                        if isinstance(top_k, int) and top_k > 0:
                            os.environ['LLM_TOP_K'] = str(top_k)
                            logger.info(f"Loaded LLM top_k: {top_k}")

                    # Load repeat penalty
                    if 'repeat_penalty' in model_params:
                        repeat_penalty = model_params['repeat_penalty']
                        if isinstance(repeat_penalty, (int, float)) and repeat_penalty > 0:
                            os.environ['LLM_REPEAT_PENALTY'] = str(repeat_penalty)
                            logger.info(f"Loaded LLM repeat penalty: {repeat_penalty}")

                    # Load system prompt
                    if 'system_prompt' in model_params:
                        system_prompt = model_params['system_prompt']
                        if isinstance(system_prompt, str):
                            os.environ['LLM_SYSTEM_PROMPT'] = system_prompt
                            logger.info(f"Loaded LLM system prompt: {system_prompt[:50]}...")

                # Load query parameters if available
                if 'query_parameters' in defaults:
                    query_params = defaults['query_parameters']

                    # Load preamble
                    if 'preamble' in query_params:
                        preamble = query_params['preamble']
                        if isinstance(preamble, str):
                            os.environ['QUERY_PREAMBLE'] = preamble
                            logger.info(f"Loaded query preamble: {preamble[:50]}...")

                    # Load anti-hallucination modes
                    if 'anti_hallucination_modes' in query_params:
                        ah_modes = query_params['anti_hallucination_modes']

                        if 'default_mode' in ah_modes:
                            default_mode = ah_modes['default_mode']
                            if default_mode in ['strict', 'balanced', 'off']:
                                os.environ['ANTI_HALLUCINATION_MODE'] = default_mode
                                logger.info(f"Loaded default anti-hallucination mode: {default_mode}")

                        if 'custom_instructions' in ah_modes:
                            custom_instructions = ah_modes['custom_instructions']
                            if isinstance(custom_instructions, str):
                                os.environ['ANTI_HALLUCINATION_CUSTOM_INSTRUCTIONS'] = custom_instructions
                                logger.info(f"Loaded anti-hallucination custom instructions")

                    # Load prompt templates
                    if 'prompt_templates' in query_params:
                        templates = query_params['prompt_templates']
                        # Store the entire templates object as JSON in environment variable
                        os.environ['PROMPT_TEMPLATES'] = json.dumps(templates)
                        logger.info(f"Loaded {len(templates)} prompt templates")

                    # Load insufficient info phrases
                    if 'insufficient_info_phrases' in query_params:
                        phrases = query_params['insufficient_info_phrases']
                        if isinstance(phrases, list):
                            os.environ['INSUFFICIENT_INFO_PHRASES'] = json.dumps(phrases)
                            logger.info(f"Loaded {len(phrases)} insufficient info phrases")

                    # Load followup question templates
                    if 'followup_question_templates' in query_params:
                        followup_templates = query_params['followup_question_templates']
                        os.environ['FOLLOWUP_QUESTION_TEMPLATES'] = json.dumps(followup_templates)
                        logger.info(f"Loaded {len(followup_templates)} followup question templates")
    except Exception as e:
        logger.error(f"Error loading default models from file: {str(e)}")

# Function to save default models to file
def save_default_models(llm_model=None, embedding_model=None, vision_model=None, use_vision=None,
                        filter_pdf_images=None, filter_sensitivity=None, max_pdf_images=None,
                        show_filtered_images=None, use_vision_during_embedding=None,
                        temperature=None, num_ctx=None, num_predict=None, top_p=None,
                        top_k=None, repeat_penalty=None, system_prompt=None,
                        preamble=None, anti_hallucination_mode=None, anti_hallucination_custom_instructions=None,
                        prompt_templates=None, insufficient_info_phrases=None, followup_question_templates=None,
                        chunk_size=None, chunk_overlap=None, extract_tables=None, extract_images=None,
                        extract_locations=None, location_confidence_threshold=None, max_locations_per_document=None,
                        enable_geocoding=None, max_images=None, batch_size=None, processing_threads=None,
                        # New query configuration parameters
                        retrieval_k=None, relevance_threshold=None, min_documents=None, max_documents=None,
                        max_pdf_images_display=None, max_url_images_display=None, max_tables_display=None, max_pdf_links_display=None,
                        hallucination_threshold_strict=None, hallucination_threshold_balanced=None, hallucination_threshold_default=None,
                        min_statement_length=None, max_vision_context_length=None, context_truncation_strategy=None):
    try:
        # Start with existing defaults or empty dict
        defaults = {}
        if os.path.exists(DEFAULT_MODELS_FILE):
            try:
                with open(DEFAULT_MODELS_FILE, 'r') as f:
                    defaults = json.load(f)
            except:
                pass

        # Update with new values if provided
        if llm_model:
            defaults['llm_model'] = llm_model

        if embedding_model:
            defaults['embedding_model'] = embedding_model

        if vision_model:
            defaults['vision_model'] = vision_model

        # Save vision toggle state if provided
        if use_vision is not None:
            defaults['use_vision_model'] = use_vision

        # Save vision during embedding toggle state if provided
        if use_vision_during_embedding is not None:
            defaults['use_vision_model_during_embedding'] = use_vision_during_embedding

        # Save image filtering settings if provided
        if filter_pdf_images is not None:
            defaults['filter_pdf_images'] = filter_pdf_images

        if filter_sensitivity:
            defaults['filter_sensitivity'] = filter_sensitivity

        if max_pdf_images is not None:
            defaults['max_pdf_images'] = max_pdf_images

        if show_filtered_images is not None:
            defaults['show_filtered_images'] = show_filtered_images

        # Initialize model_parameters if it doesn't exist
        if 'model_parameters' not in defaults:
            defaults['model_parameters'] = {}

        # Update model parameters if provided
        if temperature is not None:
            defaults['model_parameters']['temperature'] = temperature

        if num_ctx is not None:
            defaults['model_parameters']['num_ctx'] = num_ctx

        if num_predict is not None:
            defaults['model_parameters']['num_predict'] = num_predict

        if top_p is not None:
            defaults['model_parameters']['top_p'] = top_p

        if top_k is not None:
            defaults['model_parameters']['top_k'] = top_k

        if repeat_penalty is not None:
            defaults['model_parameters']['repeat_penalty'] = repeat_penalty

        if system_prompt is not None:
            defaults['model_parameters']['system_prompt'] = system_prompt

        # Initialize query_parameters if it doesn't exist
        if 'query_parameters' not in defaults:
            defaults['query_parameters'] = {}

        # Update query parameters if provided
        if preamble is not None:
            defaults['query_parameters']['preamble'] = preamble

        # Handle anti-hallucination modes
        if anti_hallucination_mode is not None or anti_hallucination_custom_instructions is not None:
            if 'anti_hallucination_modes' not in defaults['query_parameters']:
                # Initialize with default structure if it doesn't exist
                defaults['query_parameters']['anti_hallucination_modes'] = {
                    "default_mode": "strict",
                    "available_modes": ["strict", "balanced", "off"],
                    "mode_descriptions": {
                        "strict": "Only respond with information directly found in documents",
                        "balanced": "Allow limited inference while citing sources",
                        "off": "Allow more creative responses with external knowledge"
                    }
                }

            if anti_hallucination_mode is not None:
                defaults['query_parameters']['anti_hallucination_modes']['default_mode'] = anti_hallucination_mode

            if anti_hallucination_custom_instructions is not None:
                defaults['query_parameters']['anti_hallucination_modes']['custom_instructions'] = anti_hallucination_custom_instructions

        # Update prompt templates if provided
        if prompt_templates is not None:
            defaults['query_parameters']['prompt_templates'] = prompt_templates

        # Update insufficient info phrases if provided
        if insufficient_info_phrases is not None:
            defaults['query_parameters']['insufficient_info_phrases'] = insufficient_info_phrases

        # Update followup question templates if provided
        if followup_question_templates is not None:
            defaults['query_parameters']['followup_question_templates'] = followup_question_templates

        # Update new query configuration parameters if provided
        if retrieval_k is not None:
            defaults['query_parameters']['retrieval_k'] = retrieval_k

        if relevance_threshold is not None:
            defaults['query_parameters']['relevance_threshold'] = relevance_threshold

        if min_documents is not None:
            defaults['query_parameters']['min_documents'] = min_documents

        if max_documents is not None:
            defaults['query_parameters']['max_documents'] = max_documents

        if max_pdf_images_display is not None:
            defaults['query_parameters']['max_pdf_images_display'] = max_pdf_images_display

        if max_url_images_display is not None:
            defaults['query_parameters']['max_url_images_display'] = max_url_images_display

        if max_tables_display is not None:
            defaults['query_parameters']['max_tables_display'] = max_tables_display

        if max_pdf_links_display is not None:
            defaults['query_parameters']['max_pdf_links_display'] = max_pdf_links_display

        if max_vision_context_length is not None:
            defaults['query_parameters']['max_vision_context_length'] = max_vision_context_length

        if context_truncation_strategy is not None:
            defaults['query_parameters']['context_truncation_strategy'] = context_truncation_strategy

        # Initialize hallucination_detection if it doesn't exist
        if 'hallucination_detection' not in defaults['query_parameters']:
            defaults['query_parameters']['hallucination_detection'] = {}

        # Update hallucination detection parameters if provided
        if hallucination_threshold_strict is not None:
            defaults['query_parameters']['hallucination_detection']['threshold_strict'] = hallucination_threshold_strict

        if hallucination_threshold_balanced is not None:
            defaults['query_parameters']['hallucination_detection']['threshold_balanced'] = hallucination_threshold_balanced

        if hallucination_threshold_default is not None:
            defaults['query_parameters']['hallucination_detection']['threshold_default'] = hallucination_threshold_default

        if min_statement_length is not None:
            defaults['query_parameters']['hallucination_detection']['min_statement_length'] = min_statement_length

        # Initialize embedding_parameters if it doesn't exist
        if 'embedding_parameters' not in defaults:
            defaults['embedding_parameters'] = {
                "chunk_size": 800,
                "chunk_overlap": 250,
                "extract_tables": True,
                "extract_images": True,
                "extract_locations": False,
                "location_confidence_threshold": 0.5,
                "max_locations_per_document": 50,
                "enable_geocoding": True,
                "use_vision_model": False,
                "filter_sensitivity": "medium",
                "max_images": 30,
                "batch_size": 50,
                "processing_threads": 4
            }

        # Update embedding parameters if provided
        if chunk_size is not None:
            defaults['embedding_parameters']['chunk_size'] = chunk_size

        if chunk_overlap is not None:
            defaults['embedding_parameters']['chunk_overlap'] = chunk_overlap

        if extract_tables is not None:
            defaults['embedding_parameters']['extract_tables'] = extract_tables

        if extract_images is not None:
            defaults['embedding_parameters']['extract_images'] = extract_images

        if extract_locations is not None:
            defaults['embedding_parameters']['extract_locations'] = extract_locations

        if location_confidence_threshold is not None:
            defaults['embedding_parameters']['location_confidence_threshold'] = location_confidence_threshold

        if max_locations_per_document is not None:
            defaults['embedding_parameters']['max_locations_per_document'] = max_locations_per_document

        if enable_geocoding is not None:
            defaults['embedding_parameters']['enable_geocoding'] = enable_geocoding

        if use_vision_during_embedding is not None:
            defaults['embedding_parameters']['use_vision_model'] = use_vision_during_embedding

        if max_images is not None:
            defaults['embedding_parameters']['max_images'] = max_images

        if batch_size is not None:
            defaults['embedding_parameters']['batch_size'] = batch_size

        if processing_threads is not None:
            defaults['embedding_parameters']['processing_threads'] = processing_threads

        # Save to file
        with open(DEFAULT_MODELS_FILE, 'w') as f:
            json.dump(defaults, f, indent=2)

        logger.info(f"Saved default models to file: {defaults}")
        return True
    except Exception as e:
        logger.error(f"Error saving default models to file: {str(e)}")
        return False

# Function to update .env file with new model settings
def update_env_file(llm_model=None, embedding_model=None, vision_model=None):
    """Update the .env file with new model settings to maintain consistency"""
    try:
        env_file = ".env"
        if not os.path.exists(env_file):
            logger.warning(".env file not found, skipping update")
            return

        # Read current .env file
        with open(env_file, "r") as f:
            lines = f.readlines()

        # Update model settings
        updated = False
        for i, line in enumerate(lines):
            if llm_model and line.startswith("LLM_MODEL="):
                lines[i] = f"LLM_MODEL={llm_model}\n"
                updated = True
                logger.info(f"Updated .env file: LLM_MODEL={llm_model}")
            elif embedding_model and line.startswith("TEXT_EMBEDDING_MODEL="):
                lines[i] = f"TEXT_EMBEDDING_MODEL={embedding_model}\n"
                updated = True
                logger.info(f"Updated .env file: TEXT_EMBEDDING_MODEL={embedding_model}")
            elif vision_model and line.startswith("VISION_MODEL="):
                lines[i] = f"VISION_MODEL={vision_model}\n"
                updated = True
                logger.info(f"Updated .env file: VISION_MODEL={vision_model}")

        # Write back to .env file if any updates were made
        if updated:
            with open(env_file, "w") as f:
                f.writelines(lines)
            logger.info("Successfully updated .env file with new model settings")

    except Exception as e:
        logger.error(f"Error updating .env file: {str(e)}")
        raise

# Load default models from file if available
load_default_models()

# Override environment variables with values from default_models.json (configuration file takes precedence)
os.environ['LLM_MODEL'] = default_llm
logger.info(f"Setting LLM_MODEL environment variable to: {default_llm}")

os.environ['TEXT_EMBEDDING_MODEL'] = default_embedding
logger.info(f"Setting TEXT_EMBEDDING_MODEL environment variable to: {default_embedding}")

os.environ['VISION_MODEL'] = default_vision
logger.info(f"Setting VISION_MODEL environment variable to: {default_vision}")

# Enable vision model by default
if 'USE_VISION_MODEL' not in os.environ:
    os.environ['USE_VISION_MODEL'] = 'true'
    logger.info("Vision model enabled by default")

# Enable vision model during embedding by default
if 'USE_VISION_MODEL_DURING_EMBEDDING' not in os.environ:
    os.environ['USE_VISION_MODEL_DURING_EMBEDDING'] = 'true'
    logger.info("Vision model during embedding enabled by default")

# Set default model parameters if not already set
if 'LLM_TEMPERATURE' not in os.environ:
    os.environ['LLM_TEMPERATURE'] = '0.7'
    logger.info("Default LLM temperature set to 0.7")

if 'LLM_NUM_CTX' not in os.environ:
    os.environ['LLM_NUM_CTX'] = '4096'
    logger.info("Default LLM context window size set to 4096")

if 'LLM_NUM_PREDICT' not in os.environ:
    os.environ['LLM_NUM_PREDICT'] = '256'
    logger.info("Default LLM max tokens to predict set to 256")

if 'LLM_TOP_P' not in os.environ:
    os.environ['LLM_TOP_P'] = '0.9'
    logger.info("Default LLM top_p set to 0.9")

if 'LLM_TOP_K' not in os.environ:
    os.environ['LLM_TOP_K'] = '40'
    logger.info("Default LLM top_k set to 40")

if 'LLM_REPEAT_PENALTY' not in os.environ:
    os.environ['LLM_REPEAT_PENALTY'] = '1.1'
    logger.info("Default LLM repeat penalty set to 1.1")

if 'LLM_SYSTEM_PROMPT' not in os.environ:
    os.environ['LLM_SYSTEM_PROMPT'] = 'You are a helpful assistant for the ERDB (Ecosystems Research and Development Bureau). Answer questions based on the provided context.'
    logger.info("Default LLM system prompt set")

# Set app config from environment variables
app.config['SELECTED_MODEL'] = os.environ['LLM_MODEL']
app.config['SELECTED_EMBEDDING'] = os.environ['TEXT_EMBEDDING_MODEL']
app.config['SELECTED_VISION_MODEL'] = os.environ['VISION_MODEL']
app.config['USE_VISION_MODEL'] = os.environ['USE_VISION_MODEL'].lower() == 'true'

# Set model parameters in app config
app.config['LLM_TEMPERATURE'] = float(os.environ['LLM_TEMPERATURE'])
app.config['LLM_NUM_CTX'] = int(os.environ['LLM_NUM_CTX'])
app.config['LLM_NUM_PREDICT'] = int(os.environ['LLM_NUM_PREDICT'])
app.config['LLM_TOP_P'] = float(os.environ['LLM_TOP_P'])
app.config['LLM_TOP_K'] = int(os.environ['LLM_TOP_K'])
app.config['LLM_REPEAT_PENALTY'] = float(os.environ['LLM_REPEAT_PENALTY'])
app.config['LLM_SYSTEM_PROMPT'] = os.environ['LLM_SYSTEM_PROMPT']

logger.info(f"Using LLM model: {app.config['SELECTED_MODEL']}")
logger.info(f"Using embedding model: {app.config['SELECTED_EMBEDDING']}")
logger.info(f"Using vision model: {app.config['SELECTED_VISION_MODEL']} (Enabled: {app.config['USE_VISION_MODEL']})")
logger.info(f"Model parameters: temperature={app.config['LLM_TEMPERATURE']}, top_p={app.config['LLM_TOP_P']}, " +
           f"top_k={app.config['LLM_TOP_K']}, repeat_penalty={app.config['LLM_REPEAT_PENALTY']}, " +
           f"context={app.config['LLM_NUM_CTX']}, max_tokens={app.config['LLM_NUM_PREDICT']}")

# Anti-hallucination mode: 'strict', 'balanced', or 'off'
app.config['ANTI_HALLUCINATION_MODE'] = os.getenv('ANTI_HALLUCINATION_MODE', 'strict')

# Custom Jinja2 filter for pretty-printing JSON
def pretty_json(data):
    return json.dumps(data, indent=2, ensure_ascii=False)
app.jinja_env.filters['prettyjson'] = pretty_json

# Custom Jinja2 filter for markdown rendering
def markdown_filter(text):
    import markdown
    if text is None:
        return ""
    return markdown.markdown(text, extensions=['extra', 'nl2br'])
app.jinja_env.filters['markdown'] = markdown_filter

# Context processor to add common variables to all templates
@app.context_processor
def inject_common_variables():
    from datetime import datetime

    def safe_has_permission(function_name):
        """
        Safely check if the current user has a dashboard permission.
        Returns False if user is not authenticated or doesn't have the permission.
        """
        try:
            if not um.current_user.is_authenticated:
                return False
            return um.current_user.has_dashboard_permission(function_name)
        except AttributeError:
            # Handle case where current_user is AnonymousUserMixin
            return False
        except Exception:
            # Handle any other unexpected errors
            return False

    return {
        'now': datetime.now(),
        'safe_has_permission': safe_has_permission
    }

@app.route('/')
def index():
    categories = sorted(os.listdir(CHROMA_PATH)) if os.path.exists(CHROMA_PATH) else []
    return render_template('index.html', categories=categories)

@app.route('/admin', methods=['GET'])
def admin_redirect():
    """Redirect /admin to /admin/dashboard"""
    return redirect(url_for('admin_dashboard'))

@app.route('/admin/dashboard', methods=['GET'])
def admin_dashboard():
    categories = sorted(utils.list_categories())
    app.config['CATEGORIES'] = categories  # Store categories for use in user management
    return render_template('admin_dashboard.html', categories=categories)

@app.route('/admin/login', methods=['POST'])
def admin_login():
    """Handle admin login directly from the dashboard."""
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        remember = request.form.get('remember') == 'on'

        # Authenticate user
        user, error = um.authenticate_user(username, password)

        if user:
            # Check if password is expired
            if user.password_expired:
                # Store user ID in session for password change
                session['password_expired_user_id'] = user.user_id
                flash('Your password has expired. Please create a new password.', 'warning')
                return redirect(url_for('user.change_expired_password'))

            # Log in user
            um.login_user(user, remember=remember)

            # Store device fingerprint if available
            device_fingerprint = request.form.get('device_fingerprint')
            if device_fingerprint:
                session['device_fingerprint'] = device_fingerprint

            flash(f'Welcome, {user.username}!', 'success')
            return redirect(url_for('admin_dashboard'))
        else:
            flash(error, 'error')
            return redirect(url_for('admin_dashboard'))

# The /api/settings/vision_embedding route is already defined elsewhere in the codebase

@app.route('/api/check_duplicate', methods=['POST'])
def check_duplicate():
    """
    API endpoint to check if a file is a duplicate before uploading.
    This allows for client-side validation and feedback.
    """
    try:
        if 'file' not in request.files:
            return jsonify({"error": "No file provided"}), 400

        file = request.files['file']
        category = request.form.get('category')

        if not file or not category:
            return jsonify({"error": "Both file and category are required"}), 400

        if not utils.allowed_file(file.filename):
            return jsonify({"error": "Invalid file type. Only PDF files are allowed."}), 400

        # Check for duplicates
        is_duplicate, duplicate_info = check_duplicate_pdf(file, category)

        if is_duplicate:
            match_type = duplicate_info['type']
            duplicate_filename = duplicate_info['filename']

            if match_type == 'filename_match':
                message = f"A file with the same name already exists in category '{category}'."
            else:
                message = f"This file appears to be a duplicate of '{duplicate_filename}' (identical content)."

            return jsonify({
                "is_duplicate": True,
                "match_type": match_type,
                "duplicate_filename": duplicate_filename,
                "message": message
            }), 200
        else:
            return jsonify({"is_duplicate": False}), 200

    except Exception as e:
        logger.error(f"Error checking for duplicate: {str(e)}")
        return jsonify({"error": f"Error checking for duplicate: {str(e)}"}), 500

@app.route('/admin/upload', methods=['GET', 'POST'])
@admin_required
@function_permission_required('upload_content')
def upload_file():
    if request.method == 'POST':
        category = request.form.get('category')
        file = request.files.get('file')
        url = request.form.get('url')

        if not category:
            flash("Category is required.", "error")
            return redirect(url_for('upload_file'))

        if file and utils.allowed_file(file.filename):
            # Get the source URL for the PDF if provided
            pdf_url = request.form.get('pdf_url')

            # Check for duplicate PDF before processing
            is_duplicate, duplicate_info = check_duplicate_pdf(file, category)

            # Get the action to take if a duplicate is found
            duplicate_action = request.form.get('duplicate_action', 'reject')

            if is_duplicate:
                match_type = duplicate_info['type']
                duplicate_filename = duplicate_info['filename']

                # Log the duplicate detection
                logger.info(f"Duplicate PDF detected: {file.filename} matches existing file {duplicate_filename} by {match_type}")

                if duplicate_action == 'reject':
                    # Reject the upload
                    if match_type == 'filename_match':
                        flash(f"A file with the same name '{file.filename}' already exists in category '{category}'. Upload rejected.", "error")
                    else:
                        flash(f"This file appears to be a duplicate of '{duplicate_filename}' (identical content). Upload rejected.", "error")
                    return redirect(url_for('upload_file'))

                elif duplicate_action == 'replace':
                    # Delete the existing file first
                    logger.info(f"Replacing existing file {duplicate_filename} with new upload {file.filename}")
                    success, delete_message = delete_file(category, duplicate_filename)
                    if not success:
                        flash(f"Failed to replace existing file: {delete_message}", "error")
                        return redirect(url_for('upload_file'))

                    # Continue with upload (falls through to normal processing)
                    flash(f"Replacing existing file '{duplicate_filename}' with new upload.", "info")

                # If duplicate_action is 'allow', just continue with the upload

            # Use the global vision model setting from environment variable
            use_vision = os.getenv('USE_VISION_MODEL_DURING_EMBEDDING', 'true').lower() == 'true'

            # Log the vision setting for debugging
            logger.debug(f"Using global vision model setting for embedding: {use_vision}")

            # Get filter sensitivity from form
            filter_sensitivity = request.form.get('filter_sensitivity')
            if not filter_sensitivity or filter_sensitivity not in ['low', 'medium', 'high']:
                filter_sensitivity = os.getenv('PDF_IMAGE_FILTER_SENSITIVITY', 'medium')

            # Get max images from form
            max_images = request.form.get('max_images')
            if max_images:
                try:
                    max_images = int(max_images)
                    if max_images < 1:
                        max_images = 10
                    elif max_images > 50:
                        max_images = 50
                except (ValueError, TypeError):
                    max_images = None

            # Log vision settings
            if use_vision:
                logger.info(f"Embedding PDF with vision analysis enabled. Sensitivity: {filter_sensitivity}, Max images: {max_images}")
            else:
                logger.info("Embedding PDF with vision analysis disabled")

            # Get force update parameter
            force_update = request.form.get('force_update', 'false').lower() == 'true'

            # Call embed_file_db_first with database-first approach
            success, message = embed_file_db_first(
                file,
                category,
                source_url=pdf_url,
                use_vision=use_vision,
                filter_sensitivity=filter_sensitivity,
                max_images=max_images,
                force_update=force_update
            )

            if success:
                if pdf_url:
                    flash(f"PDF {file.filename} uploaded successfully and linked to {pdf_url}.", "success")
                else:
                    flash(f"PDF {file.filename} uploaded successfully.", "success")
            else:
                flash(message, "error")

        if url:
            # Get the depth parameter from the form (default to 0 if not provided)
            try:
                depth = int(request.form.get('depth', 0))
                # Limit depth to a reasonable range
                if depth < 0:
                    depth = 0
                if depth > 3:
                    depth = 3
            except (ValueError, TypeError):
                depth = 0

            # Get force update parameter
            force_update = request.form.get('force_update', 'false').lower() == 'true'

            logger.info(f"Scraping URL {url} with depth {depth}, force_update={force_update}")

            # Use database-first URL scraping
            success, message, scraped_data = scrape_url_db_first(url, category, depth, force_update)

            if success and scraped_data:
                dest_dir = os.path.join(TEMP_FOLDER, category)
                os.makedirs(dest_dir, exist_ok=True)
                filename = secure_filename(url.replace('https://', '').replace('http://', '').replace('/', '_')) + '.txt'
                dest = os.path.join(dest_dir, filename)

                # Import required classes
                from langchain.schema import Document
                from langchain.text_splitter import RecursiveCharacterTextSplitter

                # Create a smaller chunk size to prevent batch size errors
                splitter = RecursiveCharacterTextSplitter(chunk_size=500, chunk_overlap=100)

                # Get the database
                db = get_vector_db(category)

                # Initialize counters
                total_chunks = 0
                pages_processed = 0

                # Log database retrieval info
                if scraped_data.get("database_retrieval"):
                    logger.info(f"Using database content for URL {url}, last scraped: {scraped_data.get('url_last_scraped')}")

                # Check if we have individual pages
                if scraped_data.get("pages"):
                    # Process each page individually to avoid large batches
                    all_page_texts = []

                    for page in scraped_data["pages"]:
                        page_text = page["text"]
                        page_url = page["url"]
                        page_depth = page["depth"]

                        # Add to the combined text file
                        all_page_texts.append(page_text)

                        # Create metadata for this page
                        metadata = {
                            "source": filename,
                            "original_url": url,
                            "page_url": page_url,
                            "type": "url",
                            "scrape_depth": depth,
                            "page_depth": page_depth,
                            "pages_scraped": scraped_data.get("pages_scraped", 1),
                            "max_depth_reached": scraped_data.get("max_depth_reached", 0)
                        }

                        # Add images and links to metadata
                        if scraped_data.get("images"):
                            metadata["images"] = json.dumps(scraped_data["images"])

                        if scraped_data.get("links"):
                            metadata["pdf_links"] = json.dumps(scraped_data["links"])

                        # Create document and split into smaller chunks
                        page_doc = Document(page_content=page_text, metadata=metadata)
                        page_chunks = splitter.split_documents([page_doc])

                        # Process chunks in smaller batches to avoid memory issues
                        BATCH_SIZE = 10
                        for i in range(0, len(page_chunks), BATCH_SIZE):
                            batch = page_chunks[i:i+BATCH_SIZE]
                            # Add to vector database
                            db.add_documents(batch)
                            total_chunks += len(batch)

                        pages_processed += 1
                        logger.info(f"Processed page {pages_processed}/{len(scraped_data['pages'])} with {len(page_chunks)} chunks")

                    # Save the combined text to a file
                    combined_text = "\n\n".join(all_page_texts)
                    with open(dest, 'w', encoding='utf-8') as f:
                        f.write(combined_text)

                else:
                    # Fallback for old format (should not happen with new code)
                    logger.warning("Using fallback processing for URL scraping - no pages found in scraped data")

                    # Get text content (fallback)
                    scraped_text = scraped_data.get("text", "No content extracted")

                    # Save the text content to a file
                    with open(dest, 'w', encoding='utf-8') as f:
                        f.write(scraped_text)

                    # Create basic metadata
                    metadata = {
                        "source": filename,
                        "original_url": url,
                        "type": "url",
                        "scrape_depth": depth
                    }

                    # Add additional metadata if available
                    if scraped_data.get("pages_scraped"):
                        metadata["pages_scraped"] = scraped_data["pages_scraped"]

                    if scraped_data.get("max_depth_reached"):
                        metadata["max_depth_reached"] = scraped_data["max_depth_reached"]

                    if scraped_data.get("images"):
                        metadata["images"] = json.dumps(scraped_data["images"])

                    if scraped_data.get("links"):
                        metadata["pdf_links"] = json.dumps(scraped_data["links"])

                    # Create document and split into smaller chunks
                    doc = Document(page_content=scraped_text, metadata=metadata)
                    chunks = splitter.split_documents([doc])

                    # Process chunks in smaller batches
                    BATCH_SIZE = 10
                    for i in range(0, len(chunks), BATCH_SIZE):
                        batch = chunks[i:i+BATCH_SIZE]
                        # Add to vector database
                        db.add_documents(batch)
                        total_chunks += len(batch)

                # Save record to database
                db_utils.save_scraped_page(category, url, filename)

                # Create success message
                pages_info = f" from {scraped_data.get('pages_scraped', 1)} pages" if depth > 0 else ""

                # Add database retrieval info to success message
                if scraped_data.get("database_retrieval"):
                    flash(f"URL {url} retrieved from database and embedded successfully with {total_chunks} chunks{pages_info}.", "success")
                else:
                    flash(f"URL {url} scraped and embedded successfully with {total_chunks} chunks{pages_info}.", "success")

                # Log detailed information
                logger.info(f"Successfully embedded URL {url} with depth {depth}, " +
                           f"processed {scraped_data.get('pages_scraped', 1)} pages, " +
                           f"created {total_chunks} chunks")
            else:
                # Display the error message from the scrape_url_db_first function
                flash(message, "error")

        return redirect(url_for('upload_file'))
    return render_template('upload.html', categories=list_categories())

@app.route('/admin/categories', methods=['GET', 'POST', 'DELETE'])
def list_categories_route():
    # Handle POST request for creating a new category
    if request.method == 'POST':
        logger.info(f"POST request to /admin/categories received. Content-Type: {request.content_type}")

        # Check if the request is JSON or form data
        if request.is_json:
            logger.info(f"JSON data received: {request.json}")
            data = request.json
            category = data.get('category')
        else:
            logger.info(f"Form data received: {request.form}")
            category = request.form.get('category')

        if category:
            logger.info(f"Creating category: {category}")
            dest_dir = os.path.join(TEMP_FOLDER, category)
            os.makedirs(dest_dir, exist_ok=True)
            chroma_dir = os.path.join(CHROMA_PATH, category)
            os.makedirs(chroma_dir, exist_ok=True)

            # Return appropriate response based on request type
            if request.is_json:
                logger.info(f"Category {category} created successfully (JSON response)")
                return jsonify({"message": f"Category {category} created successfully."}), 200
            else:
                flash(f"Category {category} created successfully.", "success")
                logger.info(f"Category {category} created successfully (redirect response)")
                return redirect(url_for('admin_dashboard'))
        else:
            logger.warning("Category name is required but was not provided")

            # Return appropriate error response based on request type
            if request.is_json:
                return jsonify({"error": "Category name is required."}), 400
            else:
                flash("Category name is required.", "error")
                return redirect(url_for('admin_dashboard'))

    # Handle DELETE request for deleting a category
    if request.method == 'DELETE':
        logger.info(f"DELETE request to /admin/categories received. Args: {request.args}")
        category = request.args.get('category')
        if category:
            logger.info(f"Deleting category: {category}")
            import shutil
            resources_deleted = []
            errors = []

            try:
                # Delete main files directory
                dest_dir = os.path.join(TEMP_FOLDER, category)
                if os.path.exists(dest_dir):
                    shutil.rmtree(dest_dir)
                    resources_deleted.append("files")
                    logger.info(f"Deleted files directory for category: {category}")

                # Delete vector database
                chroma_dir = os.path.join(CHROMA_PATH, category)
                if os.path.exists(chroma_dir):
                    shutil.rmtree(chroma_dir)
                    resources_deleted.append("vector database")
                    logger.info(f"Deleted vector database for category: {category}")

                # Delete images directory
                IMAGES_FOLDER = os.getenv("IMAGES_FOLDER", "./_temp/pdf_images")
                images_dir = os.path.join(IMAGES_FOLDER, category)
                if os.path.exists(images_dir):
                    shutil.rmtree(images_dir)
                    resources_deleted.append("extracted images")
                    logger.info(f"Deleted images directory for category: {category}")

                # Delete tables directory
                TABLES_FOLDER = os.getenv("TABLES_FOLDER", "./_temp/pdf_tables")
                tables_dir = os.path.join(TABLES_FOLDER, category)
                if os.path.exists(tables_dir):
                    shutil.rmtree(tables_dir)
                    resources_deleted.append("extracted tables")
                    logger.info(f"Deleted tables directory for category: {category}")

                # Delete database entries
                try:
                    db_utils.delete_category_scraped_pages(category)
                    resources_deleted.append("database entries")
                    logger.info(f"Deleted database entries for category: {category}")
                except Exception as db_err:
                    error_msg = f"Failed to delete database entries for category {category}: {str(db_err)}"
                    logger.error(error_msg)
                    errors.append(error_msg)

                # Prepare success message
                if resources_deleted:
                    success_message = f"Category {category} deleted successfully with all {', '.join(resources_deleted)}"
                    if errors:
                        success_message += f" (with {len(errors)} errors)"
                    logger.info(success_message)
                    return jsonify({"message": success_message}), 200
                else:
                    message = f"Category {category} not found or already deleted"
                    logger.warning(message)
                    return jsonify({"message": message}), 200

            except Exception as e:
                error_msg = f"Error deleting category {category}: {str(e)}"
                logger.error(error_msg)
                if errors:
                    error_msg += f" Additional errors: {'; '.join(errors)}"
                return jsonify({"error": error_msg}), 500
        else:
            logger.warning("Category name is required for deletion but was not provided")
            return jsonify({"error": "Category name is required."}), 400

    # Handle GET request - redirect to admin dashboard
    return redirect(url_for('admin_dashboard'))

# Route removed - functionality moved to list_categories_route

@app.route('/admin/categories/update/<old_category>', methods=['POST'])
def update_category(old_category):
    new_category = request.form.get('new_category')
    if new_category and old_category != new_category:
        old_dest_dir = os.path.join(TEMP_FOLDER, old_category)
        new_dest_dir = os.path.join(TEMP_FOLDER, new_category)
        if os.path.exists(old_dest_dir):
            os.rename(old_dest_dir, new_dest_dir)
        old_chroma_dir = os.path.join(CHROMA_PATH, old_category)
        new_chroma_dir = os.path.join(CHROMA_PATH, new_category)
        if os.path.exists(old_chroma_dir):
            os.rename(old_chroma_dir, new_chroma_dir)
        flash(f"Category {old_category} updated to {new_category}.", "success")
    else:
        flash("Invalid category name.", "error")
    return redirect(url_for('admin_dashboard'))

@app.route('/admin/categories/delete/<category>', methods=['POST'])
def delete_category(category):
    """
    Delete a category and all its associated resources (files, images, tables, vector database).
    """
    import shutil
    resources_deleted = []
    errors = []

    try:
        # Delete main files directory
        dest_dir = os.path.join(TEMP_FOLDER, category)
        if os.path.exists(dest_dir):
            shutil.rmtree(dest_dir)
            resources_deleted.append("files")
            logger.info(f"Deleted files directory for category: {category}")

        # Delete vector database
        chroma_dir = os.path.join(CHROMA_PATH, category)
        if os.path.exists(chroma_dir):
            shutil.rmtree(chroma_dir)
            resources_deleted.append("vector database")
            logger.info(f"Deleted vector database for category: {category}")

        # Delete images directory
        IMAGES_FOLDER = os.getenv("IMAGES_FOLDER", "./_temp/pdf_images")
        images_dir = os.path.join(IMAGES_FOLDER, category)
        if os.path.exists(images_dir):
            shutil.rmtree(images_dir)
            resources_deleted.append("extracted images")
            logger.info(f"Deleted images directory for category: {category}")

        # Delete tables directory
        TABLES_FOLDER = os.getenv("TABLES_FOLDER", "./_temp/pdf_tables")
        tables_dir = os.path.join(TABLES_FOLDER, category)
        if os.path.exists(tables_dir):
            shutil.rmtree(tables_dir)
            resources_deleted.append("extracted tables")
            logger.info(f"Deleted tables directory for category: {category}")

        # Delete database entries
        try:
            # This assumes there's a function to delete all entries for a category
            # If it doesn't exist, you might need to implement it
            db_utils.delete_category_scraped_pages(category)
            resources_deleted.append("database entries")
            logger.info(f"Deleted database entries for category: {category}")
        except Exception as db_err:
            error_msg = f"Failed to delete database entries for category {category}: {str(db_err)}"
            logger.error(error_msg)
            errors.append(error_msg)

        # Prepare success message
        if resources_deleted:
            success_message = f"Category {category} deleted successfully with all {', '.join(resources_deleted)}"
            if errors:
                success_message += f" (with {len(errors)} errors)"
            flash(success_message, "success")
        else:
            flash(f"Category {category} not found or already deleted.", "warning")

    except Exception as e:
        error_msg = f"Failed to delete category {category}: {str(e)}"
        logger.error(error_msg)
        if errors:
            error_msg += f" Additional errors: {'; '.join(errors)}"
        flash(error_msg, "error")

    return redirect(url_for('admin_dashboard'))

@app.route('/admin/files')
@admin_required
@function_permission_required('manage_files')
def list_files():
    files_data = {}
    categories = sorted(utils.list_categories())
    logger.info(f"Categories: {categories}")

    # Query SQLite database for URLs
    scraped_pages = db_utils.get_scraped_pages()
    url_map = {(page['category'], page['filename']): page['url'] for page in scraped_pages}

    for category in categories:
        category_path = os.path.join(TEMP_FOLDER, category)
        if os.path.isdir(category_path):
            files = []

            # First, check for files in the old structure (directly in the category directory)
            for filename in os.listdir(category_path):
                file_path = os.path.join(category_path, filename)

                # Only process files, not directories (which would be part of the new structure)
                if os.path.isfile(file_path):
                    if filename.endswith('.pdf'):
                        original_filename = filename.split('_', 1)[1] if '_' in filename else filename

                        # Get the vector data to check for original_url and database info
                        db = get_vector_db(category)
                        docs = db.similarity_search_with_score("", k=1, filter={"source": filename})
                        original_url = None
                        source_url_id = None
                        pdf_document_id = None
                        cover_image_id = None
                        database_retrieval = False

                        # Extract metadata if it exists
                        if docs and len(docs) > 0:
                            doc, _ = docs[0]
                            if doc.metadata.get("original_url"):
                                original_url = doc.metadata.get("original_url")
                            if doc.metadata.get("source_url_id"):
                                source_url_id = doc.metadata.get("source_url_id")
                            if doc.metadata.get("pdf_document_id"):
                                pdf_document_id = doc.metadata.get("pdf_document_id")
                            if doc.metadata.get("cover_image_id"):
                                cover_image_id = doc.metadata.get("cover_image_id")
                            if doc.metadata.get("database_retrieval"):
                                database_retrieval = doc.metadata.get("database_retrieval")

                        file_data = {
                            "original_filename": original_filename,
                            "source": filename,
                            "type": "pdf"
                        }

                        if original_url:
                            file_data["original_url"] = original_url

                        # Add database info
                        if source_url_id:
                            file_data["source_url_id"] = source_url_id
                        if pdf_document_id:
                            file_data["pdf_document_id"] = pdf_document_id
                        if cover_image_id:
                            file_data["cover_image_id"] = cover_image_id
                        if database_retrieval:
                            file_data["database_retrieval"] = database_retrieval

                        files.append(file_data)
                    elif filename.endswith('.txt'):
                        original_url = url_map.get((category, filename), filename.replace('.txt', '').replace('_', '/'))

                        # Get the vector data to check for metadata
                        db = get_vector_db(category)
                        docs = db.similarity_search_with_score("", k=1, filter={"source": filename})

                        file_data = {
                            "original_filename": original_url,
                            "source": filename,
                            "type": "url"
                        }

                        # Extract metadata if it exists
                        if docs and len(docs) > 0:
                            doc, _ = docs[0]
                            if doc.metadata.get("scrape_depth"):
                                file_data["scrape_depth"] = doc.metadata.get("scrape_depth")
                            if doc.metadata.get("pages_scraped"):
                                file_data["pages_scraped"] = doc.metadata.get("pages_scraped")
                            if doc.metadata.get("source_url_id"):
                                file_data["source_url_id"] = doc.metadata.get("source_url_id")
                            if doc.metadata.get("database_retrieval"):
                                file_data["database_retrieval"] = doc.metadata.get("database_retrieval")
                            if doc.metadata.get("url_last_scraped"):
                                file_data["url_last_scraped"] = doc.metadata.get("url_last_scraped")

                        files.append(file_data)

            # Now, check for files in the new hierarchical structure
            for pdf_dir_name in os.listdir(category_path):
                pdf_dir_path = os.path.join(category_path, pdf_dir_name)

                # Check if it's a directory (part of the new structure)
                if os.path.isdir(pdf_dir_path):
                    # Look for PDF files in this directory
                    for filename in os.listdir(pdf_dir_path):
                        if filename.endswith('.pdf'):
                            # Get the full path to the PDF file
                            pdf_path = os.path.join(pdf_dir_path, filename)

                            # Only process if it's a file
                            if os.path.isfile(pdf_path):
                                original_filename = filename.split('_', 1)[1] if '_' in filename else filename

                                # Get the vector data to check for original_url and database info
                                db = get_vector_db(category)
                                docs = db.similarity_search_with_score("", k=1, filter={"source": filename})
                                original_url = None
                                source_url_id = None
                                pdf_document_id = None
                                cover_image_id = None
                                database_retrieval = False

                                # Extract metadata if it exists
                                if docs and len(docs) > 0:
                                    doc, _ = docs[0]
                                    if doc.metadata.get("original_url"):
                                        original_url = doc.metadata.get("original_url")
                                    if doc.metadata.get("source_url_id"):
                                        source_url_id = doc.metadata.get("source_url_id")
                                    if doc.metadata.get("pdf_document_id"):
                                        pdf_document_id = doc.metadata.get("pdf_document_id")
                                    if doc.metadata.get("cover_image_id"):
                                        cover_image_id = doc.metadata.get("cover_image_id")
                                    if doc.metadata.get("database_retrieval"):
                                        database_retrieval = doc.metadata.get("database_retrieval")

                                file_data = {
                                    "original_filename": original_filename,
                                    "source": filename,
                                    "type": "pdf"
                                }

                                if original_url:
                                    file_data["original_url"] = original_url

                                # Add database info
                                if source_url_id:
                                    file_data["source_url_id"] = source_url_id
                                if pdf_document_id:
                                    file_data["pdf_document_id"] = pdf_document_id
                                if cover_image_id:
                                    file_data["cover_image_id"] = cover_image_id
                                if database_retrieval:
                                    file_data["database_retrieval"] = database_retrieval

                                files.append(file_data)

            if files:
                files_data[category] = files

    logger.info(f"files_data type: {type(files_data)}, content: {files_data}")
    if not isinstance(files_data, dict):
        raise ValueError("files_data is not a dictionary")
    return render_template('files.html', files_data=files_data)

@app.route('/admin/delete/<category>/<filename>', methods=['POST', 'DELETE'])
@admin_required
def delete_file_route(category, filename):
    """
    Delete a file and all its associated resources (images, tables, vector embeddings).

    This route handler calls the delete_file function and also removes any database
    entries for scraped pages if applicable.
    """
    try:
        # Call the enhanced delete_file function that handles cleanup of associated resources
        success, message = delete_file(category, filename)

        if success:
            # Also delete any database entries for scraped pages
            try:
                db_utils.delete_scraped_page(category, filename)
                logger.info(f"Deleted database entry for {category}/{filename}")
            except Exception as db_err:
                logger.warning(f"Failed to delete database entry for {category}/{filename}: {str(db_err)}")
                # Continue with the deletion process even if the database entry couldn't be deleted
                if "with" in message:
                    message += f"; Failed to delete database entry: {str(db_err)}"
                else:
                    message += f" (with error: Failed to delete database entry: {str(db_err)})"

            # Return appropriate response based on request method
            if request.method == 'DELETE':
                logger.info(f"File {filename} deleted successfully: {message}")
                return jsonify({"message": message}), 200
            else:
                flash(message, "success")
        else:
            if request.method == 'DELETE':
                logger.warning(f"Failed to delete file {filename}: {message}")
                return jsonify({"error": message}), 400
            else:
                flash(message, "error")
    except Exception as e:
        error_msg = f"Failed to delete file: {str(e)}"
        logger.error(error_msg)
        if request.method == 'DELETE':
            return jsonify({"error": error_msg}), 500
        else:
            flash(error_msg, "error")

    # Only redirect for POST requests
    return redirect(url_for('list_files'))

@app.route('/admin/vector_data/<category>/<filename>')
def view_vector_data(category, filename):
    try:
        db = get_vector_db(category)
        docs = db.similarity_search_with_score("", k=100, filter={"source": filename})
        vector_data = []
        for doc, score in docs:
            if doc.metadata.get("source") == filename:
                vector_data.append({"content": doc.page_content, "metadata": doc.metadata, "score": score})
        return render_template('vector_data.html', category=category, filename=filename, vector_data=vector_data)
    except Exception as e:
        flash(f"Failed to retrieve vector data: {str(e)}", "error")
        return redirect(url_for('list_files'))

@app.route('/admin/unified_config', methods=['GET', 'POST'])
@admin_required
@function_permission_required('model_settings')
def unified_config():
    """Page and endpoint for model settings"""
    if request.method == 'POST':
        try:
            data = request.get_json()
            if not data:
                return jsonify({"error": "No data provided"}), 400

            # Check which tab is being saved
            tab = data.get('tab', 'all')

            # Initialize variables with None to avoid errors
            llm_model = embedding_model = vision_model = use_vision = None
            use_vision_during_embedding = filter_sensitivity = max_pdf_images = show_filtered_images = None
            preamble = anti_hallucination_mode = anti_hallucination_custom_instructions = None
            prompt_templates = insufficient_info_phrases = followup_question_templates = None
            chunk_size = chunk_overlap = extract_tables = extract_images = None
            extract_locations = location_confidence_threshold = max_locations_per_document = enable_geocoding = None
            batch_size = processing_threads = None

            # New query configuration parameters
            retrieval_k = relevance_threshold = min_documents = max_documents = None
            max_pdf_images_display = max_url_images_display = max_tables_display = max_pdf_links_display = None
            hallucination_threshold_strict = hallucination_threshold_balanced = hallucination_threshold_default = None
            min_statement_length = max_vision_context_length = context_truncation_strategy = None

            # Extract data based on which tab is being saved
            if tab in ['all', 'models']:
                # AI Models
                llm_model = data.get('llm_model')
                embedding_model = data.get('embedding_model')
                vision_model = data.get('vision_model')
                use_vision = data.get('use_vision')

            if tab in ['all', 'query']:
                # Query Configuration
                preamble = data.get('preamble')
                anti_hallucination_mode = data.get('anti_hallucination_mode')
                anti_hallucination_custom_instructions = data.get('anti_hallucination_custom_instructions')
                prompt_templates = data.get('prompt_templates')
                insufficient_info_phrases = data.get('insufficient_info_phrases')
                followup_question_templates = data.get('followup_question_templates')

                # New query configuration parameters
                retrieval_k = data.get('retrieval_k')
                relevance_threshold = data.get('relevance_threshold')
                min_documents = data.get('min_documents')
                max_documents = data.get('max_documents')
                max_pdf_images_display = data.get('max_pdf_images_display')
                max_url_images_display = data.get('max_url_images_display')
                max_tables_display = data.get('max_tables_display')
                max_pdf_links_display = data.get('max_pdf_links_display')
                hallucination_threshold_strict = data.get('hallucination_threshold_strict')
                hallucination_threshold_balanced = data.get('hallucination_threshold_balanced')
                hallucination_threshold_default = data.get('hallucination_threshold_default')
                min_statement_length = data.get('min_statement_length')
                max_vision_context_length = data.get('max_vision_context_length')
                context_truncation_strategy = data.get('context_truncation_strategy')

            if tab in ['all', 'embedding']:
                # Embedding Configuration
                chunk_size = data.get('chunk_size')
                chunk_overlap = data.get('chunk_overlap')
                extract_tables = data.get('extract_tables')
                extract_images = data.get('extract_images')
                extract_locations = data.get('extract_locations')
                location_confidence_threshold = data.get('location_confidence_threshold')
                max_locations_per_document = data.get('max_locations_per_document')
                enable_geocoding = data.get('enable_geocoding')
                batch_size = data.get('batch_size')
                processing_threads = data.get('processing_threads')

                # Vision settings for embedding (these are part of the embedding tab)
                if tab == 'embedding':
                    use_vision_during_embedding = data.get('use_vision_during_embedding')
                    filter_sensitivity = data.get('filter_sensitivity')
                    max_pdf_images = data.get('max_pdf_images')
                    show_filtered_images = data.get('show_filtered_images')

            # Save all configuration parameters
            success = save_default_models(
                # AI Models
                llm_model=llm_model,
                embedding_model=embedding_model,
                vision_model=vision_model,
                use_vision=use_vision,
                use_vision_during_embedding=use_vision_during_embedding,
                filter_sensitivity=filter_sensitivity,
                max_pdf_images=max_pdf_images,
                show_filtered_images=show_filtered_images,

                # Query Configuration
                preamble=preamble,
                anti_hallucination_mode=anti_hallucination_mode,
                anti_hallucination_custom_instructions=anti_hallucination_custom_instructions,
                prompt_templates=prompt_templates,
                insufficient_info_phrases=insufficient_info_phrases,
                followup_question_templates=followup_question_templates,

                # New query configuration parameters
                retrieval_k=retrieval_k,
                relevance_threshold=relevance_threshold,
                min_documents=min_documents,
                max_documents=max_documents,
                max_pdf_images_display=max_pdf_images_display,
                max_url_images_display=max_url_images_display,
                max_tables_display=max_tables_display,
                max_pdf_links_display=max_pdf_links_display,
                hallucination_threshold_strict=hallucination_threshold_strict,
                hallucination_threshold_balanced=hallucination_threshold_balanced,
                hallucination_threshold_default=hallucination_threshold_default,
                min_statement_length=min_statement_length,
                max_vision_context_length=max_vision_context_length,
                context_truncation_strategy=context_truncation_strategy,

                # Embedding Configuration
                chunk_size=chunk_size,
                chunk_overlap=chunk_overlap,
                extract_tables=extract_tables,
                extract_images=extract_images,
                extract_locations=extract_locations,
                location_confidence_threshold=location_confidence_threshold,
                max_locations_per_document=max_locations_per_document,
                enable_geocoding=enable_geocoding,
                batch_size=batch_size,
                processing_threads=processing_threads
            )

            if success:
                # Update environment variables
                # AI Models
                if llm_model:
                    app.config['SELECTED_MODEL'] = llm_model
                    os.environ['LLM_MODEL'] = llm_model  # Fix: Use LLM_MODEL instead of SELECTED_MODEL
                    logger.info(f"Updated LLM_MODEL environment variable to: {llm_model}")

                if embedding_model:
                    app.config['SELECTED_EMBEDDING'] = embedding_model
                    os.environ['TEXT_EMBEDDING_MODEL'] = embedding_model
                    # Clear the vector DB cache to force reinitialization with the new embedding model
                    from get_vector_db import _chroma_cache
                    _chroma_cache.clear()
                    logger.info(f"Updated TEXT_EMBEDDING_MODEL environment variable to: {embedding_model}")

                if vision_model:
                    app.config['SELECTED_VISION_MODEL'] = vision_model
                    os.environ['VISION_MODEL'] = vision_model  # Fix: Use VISION_MODEL instead of SELECTED_VISION_MODEL
                    logger.info(f"Updated VISION_MODEL environment variable to: {vision_model}")

                if use_vision is not None:
                    app.config['USE_VISION_MODEL'] = use_vision
                    os.environ['USE_VISION_MODEL'] = 'true' if use_vision else 'false'

                if use_vision_during_embedding is not None:
                    os.environ['USE_VISION_MODEL_DURING_EMBEDDING'] = 'true' if use_vision_during_embedding else 'false'

                if filter_sensitivity:
                    os.environ['PDF_IMAGE_FILTER_SENSITIVITY'] = filter_sensitivity

                if max_pdf_images is not None:
                    os.environ['MAX_PDF_IMAGES_TO_ANALYZE'] = str(max_pdf_images)

                if show_filtered_images is not None:
                    os.environ['SHOW_FILTERED_IMAGES'] = 'true' if show_filtered_images else 'false'

                # Query Configuration
                if preamble is not None:
                    os.environ['QUERY_PREAMBLE'] = preamble

                if anti_hallucination_mode is not None:
                    os.environ['ANTI_HALLUCINATION_MODE'] = anti_hallucination_mode

                if anti_hallucination_custom_instructions is not None:
                    os.environ['ANTI_HALLUCINATION_CUSTOM_INSTRUCTIONS'] = anti_hallucination_custom_instructions

                if prompt_templates is not None:
                    os.environ['PROMPT_TEMPLATES'] = json.dumps(prompt_templates)

                if insufficient_info_phrases is not None:
                    os.environ['INSUFFICIENT_INFO_PHRASES'] = json.dumps(insufficient_info_phrases)

                if followup_question_templates is not None:
                    os.environ['FOLLOWUP_QUESTION_TEMPLATES'] = json.dumps(followup_question_templates)

                # Embedding Configuration
                if chunk_size is not None:
                    os.environ['EMBEDDING_CHUNK_SIZE'] = str(chunk_size)

                if chunk_overlap is not None:
                    os.environ['EMBEDDING_CHUNK_OVERLAP'] = str(chunk_overlap)

                if batch_size is not None:
                    os.environ['EMBEDDING_BATCH_SIZE'] = str(batch_size)

                if processing_threads is not None:
                    os.environ['EMBEDDING_PROCESSING_THREADS'] = str(processing_threads)

                # Create a success message based on which tab was saved
                if tab == 'models':
                    message = "Models settings saved successfully"
                elif tab == 'query':
                    message = "Query settings saved successfully"
                elif tab == 'embedding':
                    message = "Embedding settings saved successfully"
                else:
                    message = "All configuration saved successfully"

                return jsonify({"message": message}), 200
            else:
                return jsonify({"error": "Failed to save configuration"}), 500

        except Exception as e:
            logger.error(f"Error saving unified configuration: {str(e)}")
            return jsonify({"error": f"Failed to save configuration: {str(e)}"}), 500

    # GET request - show the unified configuration page
    try:
        # Load models data
        models_data = get_models_data()

        # Load query configuration data
        query_data = get_query_config_data()

        # Load embedding configuration data
        embedding_data = get_embedding_config_data()

        # Combine all data
        combined_data = {**models_data, **query_data, **embedding_data}

        return render_template('unified_config.html', **combined_data)
    except Exception as e:
        logger.error(f"Error loading model settings page: {str(e)}")
        flash(f"Failed to load model settings: {str(e)}", "error")
        return redirect(url_for('admin_dashboard'))

@app.route('/admin/query_config_partial')
def query_config_partial():
    """Endpoint to get partial HTML for query configuration"""
    try:
        # Load query configuration data
        query_data = get_query_config_data()

        return render_template('query_config_partial.html', **query_data)
    except Exception as e:
        logger.error(f"Error loading query configuration partial: {str(e)}")
        return f"Error loading query configuration: {str(e)}", 500

@app.route('/admin/embedding_config_partial')
def embedding_config_partial():
    """Endpoint to get partial HTML for embedding configuration"""
    try:
        # Load embedding configuration data
        embedding_data = get_embedding_config_data()

        return render_template('embedding_config_partial.html', **embedding_data)
    except Exception as e:
        logger.error(f"Error loading embedding configuration partial: {str(e)}")
        return f"Error loading embedding configuration: {str(e)}", 500

# Helper function to get models data
def get_models_data():
    # Get available models from Ollama
    models = []
    embedding_models = []
    vision_models = []

    try:
        # Get models from Ollama
        response = requests.get(f"{OLLAMA_BASE_URL}/api/tags", timeout=10)
        if response.status_code == 200:
            data = response.json()

            # Process models
            for model in data.get('models', []):
                model_name = model.get('name', '')
                model_size = model.get('size', 0)
                model_name_lower = model_name.lower()

                # Create model dict
                model_dict = {
                    'name': model_name,
                    'size': model_size
                }

                # Categorize embedding models first (most specific)
                if 'embed' in model_name_lower or model_name in ['bge-m3:latest', 'nomic-embed-text:latest']:
                    embedding_models.append(model_dict.copy())
                    continue

                # Check for vision models - explicit vision models and multimodal models
                is_vision_model = (
                    'vision' in model_name_lower or
                    model_name.endswith('-vision') or
                    # Gemma 3 multimodal models (4b and 12b variants with -it suffix)
                    (model_name_lower.startswith("gemma3:") and
                     any(size in model_name_lower for size in ["4b", "12b"]) and
                     ("-it" in model_name_lower or "instruct" in model_name_lower))
                )

                # Check if it's a general LLM model (not embedding, could be multimodal)
                is_llm_model = not ('embed' in model_name_lower)

                # Add to appropriate categories
                if is_vision_model:
                    vision_models.append(model_dict.copy())

                    # Multimodal models can also be used as regular LLMs
                    if (model_name_lower.startswith("gemma3:") and
                        any(size in model_name_lower for size in ["4b", "12b"]) and
                        ("-it" in model_name_lower or "instruct" in model_name_lower)):
                        models.append(model_dict.copy())
                elif is_llm_model:
                    models.append(model_dict.copy())

    except requests.exceptions.RequestException as e:
        logger.error(f"Error connecting to Ollama: {str(e)}")
    except Exception as e:
        logger.error(f"Error getting models from Ollama: {str(e)}")

    # If no models found, add default ones
    if not models:
        models = [
            {"name": "llama3.1:8b-instruct-q4_K_M", "size": 4.9 * 1024 * 1024 * 1024},
            {"name": "mistral:latest", "size": 4.1 * 1024 * 1024 * 1024},
            {"name": "llama3.2:3b-instruct-q4_K_M", "size": 2.0 * 1024 * 1024 * 1024},
            {"name": "gemma3:1b", "size": 815 * 1024 * 1024}
        ]

    if not embedding_models:
        embedding_models = [
            {"name": "mxbai-embed-large:latest", "size": 669 * 1024 * 1024},
            {"name": "bge-m3:latest", "size": 1.2 * 1024 * 1024 * 1024},
            {"name": "nomic-embed-text:latest", "size": 274 * 1024 * 1024}
        ]

    if not vision_models:
        vision_models = [
            {"name": "llama3.2-vision:11b-instruct-q4_K_M", "size": 6.8 * 1024 * 1024 * 1024},
            {"name": "gemma3:4b-it-q4_K_M", "size": 3.3 * 1024 * 1024 * 1024},
            {"name": "gemma3:12b-it-q4_K_M", "size": 7.2 * 1024 * 1024 * 1024}
        ]

    # Get current configuration
    defaults = {}
    if os.path.exists(DEFAULT_MODELS_FILE):
        with open(DEFAULT_MODELS_FILE, 'r') as f:
            defaults = json.load(f)

    # Get model parameters
    default_llm = defaults.get('llm_model', 'llama3.1:8b-instruct-q4_K_M')
    default_embedding = defaults.get('embedding_model', 'mxbai-embed-large:latest')
    default_vision = defaults.get('vision_model', 'llama3.2-vision:11b-instruct-q4_K_M')
    use_vision = defaults.get('use_vision_model', False)
    use_vision_during_embedding = defaults.get('use_vision_model_during_embedding', False)
    filter_pdf_images = defaults.get('filter_pdf_images', True)
    filter_sensitivity = defaults.get('filter_sensitivity', 'medium')
    max_pdf_images = defaults.get('max_pdf_images', 30)
    show_filtered_images = defaults.get('show_filtered_images', False)

    # Get model parameters
    model_params = defaults.get('model_parameters', {})
    temperature = model_params.get('temperature', 0.7)
    num_ctx = model_params.get('num_ctx', 4096)
    num_predict = model_params.get('num_predict', 256)
    top_p = model_params.get('top_p', 0.9)
    top_k = model_params.get('top_k', 40)
    repeat_penalty = model_params.get('repeat_penalty', 1.1)
    system_prompt = model_params.get('system_prompt', '')

    return {
        'models': models,
        'embeddings': embedding_models,
        'vision_models': vision_models,
        'selected_model': app.config.get('SELECTED_MODEL', default_llm),
        'selected_embedding': app.config.get('SELECTED_EMBEDDING', default_embedding),
        'selected_vision': app.config.get('SELECTED_VISION_MODEL', default_vision),
        'use_vision': use_vision,
        'use_vision_during_embedding': use_vision_during_embedding,
        'filter_pdf_images': filter_pdf_images,
        'filter_sensitivity': filter_sensitivity,
        'max_pdf_images': max_pdf_images,
        'show_filtered_images': show_filtered_images,
        'default_llm': default_llm,
        'default_embedding': default_embedding,
        'default_vision': default_vision,
        'temperature': temperature,
        'num_ctx': num_ctx,
        'num_predict': num_predict,
        'top_p': top_p,
        'top_k': top_k,
        'repeat_penalty': repeat_penalty,
        'system_prompt': system_prompt
    }

# Helper function to get query configuration data
def get_query_config_data():
    # Load current configuration from environment variables or defaults
    defaults = {}
    if os.path.exists(DEFAULT_MODELS_FILE):
        with open(DEFAULT_MODELS_FILE, 'r') as f:
            defaults = json.load(f)

    # Get query parameters from defaults
    query_params = defaults.get('query_parameters', {})

    # Extract specific parameters with defaults
    preamble = query_params.get('preamble', '')
    anti_hallucination_modes = query_params.get('anti_hallucination_modes', {
        'default_mode': 'strict',
        'custom_instructions': ''
    })
    prompt_templates = query_params.get('prompt_templates', {
        'strict': '',
        'balanced': '',
        'off': '',
        'general': '',
        'document_specific': ''
    })
    insufficient_info_phrases = query_params.get('insufficient_info_phrases', [
        "I don't have enough information",
        "The provided context does not contain",
        "There is no information"
    ])
    followup_question_templates = query_params.get('followup_question_templates', {
        'default': '',
        'insufficient_info': ''
    })

    # Get vision model settings
    use_vision = defaults.get('use_vision_model', False)

    # Get new query configuration parameters with defaults
    retrieval_k = query_params.get('retrieval_k', 12)
    relevance_threshold = query_params.get('relevance_threshold', 0.15)
    min_documents = query_params.get('min_documents', 3)
    max_documents = query_params.get('max_documents', 8)
    max_pdf_images_display = query_params.get('max_pdf_images_display', 5)
    max_url_images_display = query_params.get('max_url_images_display', 5)
    max_tables_display = query_params.get('max_tables_display', 3)
    max_pdf_links_display = query_params.get('max_pdf_links_display', 10)
    max_vision_context_length = query_params.get('max_vision_context_length', 2000)
    context_truncation_strategy = query_params.get('context_truncation_strategy', 'end')

    # Get hallucination detection parameters with defaults
    hallucination_detection = query_params.get('hallucination_detection', {})
    threshold_strict = hallucination_detection.get('threshold_strict', 0.6)
    threshold_balanced = hallucination_detection.get('threshold_balanced', 0.4)
    threshold_default = hallucination_detection.get('threshold_default', 0.5)
    min_statement_length = hallucination_detection.get('min_statement_length', 20)

    return {
        'preamble': preamble,
        'anti_hallucination_modes': anti_hallucination_modes,
        'prompt_templates': prompt_templates,
        'insufficient_info_phrases': insufficient_info_phrases,
        'followup_question_templates': followup_question_templates,
        'use_vision': use_vision,
        'query_parameters': {
            'retrieval_k': retrieval_k,
            'relevance_threshold': relevance_threshold,
            'min_documents': min_documents,
            'max_documents': max_documents,
            'max_pdf_images_display': max_pdf_images_display,
            'max_url_images_display': max_url_images_display,
            'max_tables_display': max_tables_display,
            'max_pdf_links_display': max_pdf_links_display,
            'max_vision_context_length': max_vision_context_length,
            'context_truncation_strategy': context_truncation_strategy
        },
        'hallucination_detection': {
            'threshold_strict': threshold_strict,
            'threshold_balanced': threshold_balanced,
            'threshold_default': threshold_default,
            'min_statement_length': min_statement_length
        }
    }

# Helper function to get embedding configuration data
def get_embedding_config_data():
    # Load current configuration from environment variables or defaults
    defaults = {}
    if os.path.exists(DEFAULT_MODELS_FILE):
        with open(DEFAULT_MODELS_FILE, 'r') as f:
            defaults = json.load(f)

    # Get embedding parameters from defaults
    embedding_params = defaults.get('embedding_parameters', {})

    # Set default values if not present
    if not embedding_params:
        embedding_params = {
            "chunk_size": 800,
            "chunk_overlap": 250,
            "extract_tables": True,
            "extract_images": True,
            "extract_locations": False,
            "location_confidence_threshold": 0.5,
            "max_locations_per_document": 50,
            "enable_geocoding": True,
            "use_vision_model": False,
            "filter_sensitivity": "medium",
            "max_images": 30,
            "batch_size": 50,
            "processing_threads": 4
        }

    # Get available embedding models
    available_embedding_models = []
    try:
        # Try to get models from Ollama
        models_response = requests.get(f"{OLLAMA_BASE_URL}/api/tags")
        if models_response.status_code == 200:
            models_data = models_response.json()

            # Filter for embedding models
            embedding_models = []
            for model in models_data.get('models', []):
                model_name = model.get('name', '')
                if 'embed' in model_name.lower() or model_name in ['bge-m3:latest', 'nomic-embed-text:latest']:
                    embedding_models.append({
                        "name": model_name,
                        "size": model.get('size', 0)
                    })

            available_embedding_models = embedding_models
    except Exception as e:
        logger.error(f"Failed to get available models: {str(e)}")

    # If no embedding models found, add default ones
    if not available_embedding_models:
        available_embedding_models = [
            {"name": "mxbai-embed-large:latest", "size": 669 * 1024 * 1024},
            {"name": "bge-m3:latest", "size": 1.2 * 1024 * 1024 * 1024},
            {"name": "nomic-embed-text:latest", "size": 274 * 1024 * 1024}
        ]

    # Get vision model settings
    use_vision_during_embedding = defaults.get('use_vision_model_during_embedding', False)
    filter_sensitivity = defaults.get('filter_sensitivity', 'medium')
    max_pdf_images = defaults.get('max_pdf_images', 30)
    show_filtered_images = defaults.get('show_filtered_images', False)

    return {
        'embedding_params': embedding_params,
        'available_embedding_models': available_embedding_models,
        'use_vision_during_embedding': use_vision_during_embedding,
        'filter_sensitivity': filter_sensitivity,
        'max_pdf_images': max_pdf_images,
        'show_filtered_images': show_filtered_images
    }

@app.route('/admin/clean_urls', methods=['GET'])
@admin_required
def clean_urls():
    """Clean up malformed URLs in the database."""
    from db_content_utils import clean_malformed_urls

    results = clean_malformed_urls()

    if 'error' in results:
        flash(f"Error cleaning URLs: {results['error']}", "error")
    else:
        flash(f"URL cleanup complete: {results['fixed_count']} URLs fixed, {results['deleted_count']} URLs deleted, {results['total_processed']} total processed.", "success")

    return redirect(url_for('admin_dashboard'))

@app.route('/admin/darkpan_demo', methods=['GET'])
@admin_required
def darkpan_demo():
    """Demo page for DarkPan Bootstrap 5 Admin Dashboard Template."""
    return render_template('darkpan_demo.html')

@app.route('/docs')
def documentation():
    """Simple documentation page."""
    return render_template('documentation.html')

@app.route('/admin/greeting_management', methods=['GET'])
@admin_required
@function_permission_required('greeting_management')
def greeting_management():
    """Greeting management interface."""
    try:
        greeting_manager = GreetingManager()
        templates = greeting_manager.get_greeting_templates()

        # Group templates by type for easier display
        grouped_templates = {
            'welcome': [],
            'response': [],
            'return_user': [],
            'time_based': []
        }

        for template in templates:
            template_type = template.get('template_type', 'response')
            if template_type in grouped_templates:
                grouped_templates[template_type].append(template)

        return render_template('greeting_management.html',
                             templates=templates,
                             grouped_templates=grouped_templates)
    except Exception as e:
        logger.error(f"Error loading greeting management: {str(e)}")
        flash(f"Error loading greeting management: {str(e)}", "error")
        return redirect(url_for('admin_dashboard'))

@app.route('/admin/embedding_config', methods=['GET', 'POST'])
def embedding_config():
    """Page and endpoint for embedding configuration settings"""
    if request.method == 'GET':
        flash("Embedding Configuration has been moved to the Model Settings page.", "info")
        return redirect(url_for('unified_config', _anchor='embedding'))

    # For POST requests, continue with the original function to maintain API compatibility
    if request.method == 'POST':
        try:
            data = request.get_json()
            if not data:
                return jsonify({"error": "No data provided"}), 400

            # Extract data from request
            chunk_size = data.get('chunk_size')
            chunk_overlap = data.get('chunk_overlap')
            extract_tables = data.get('extract_tables')
            extract_images = data.get('extract_images')
            use_vision_model = data.get('use_vision_model')
            filter_sensitivity = data.get('filter_sensitivity')
            max_images = data.get('max_images')
            embedding_model = data.get('embedding_model')
            batch_size = data.get('batch_size')
            processing_threads = data.get('processing_threads')

            # Save the embedding configuration
            success = save_default_models(
                embedding_model=embedding_model,
                use_vision_during_embedding=use_vision_model,
                filter_sensitivity=filter_sensitivity,
                chunk_size=chunk_size,
                chunk_overlap=chunk_overlap,
                extract_tables=extract_tables,
                extract_images=extract_images,
                max_images=max_images,
                batch_size=batch_size,
                processing_threads=processing_threads
            )

            if success:
                # Update environment variables
                if embedding_model is not None:
                    os.environ['TEXT_EMBEDDING_MODEL'] = embedding_model
                    # Clear the vector DB cache to force reinitialization with the new embedding model
                    from get_vector_db import _chroma_cache
                    _chroma_cache.clear()

                if use_vision_model is not None:
                    os.environ['USE_VISION_MODEL_DURING_EMBEDDING'] = 'true' if use_vision_model else 'false'

                if filter_sensitivity is not None:
                    os.environ['PDF_IMAGE_FILTER_SENSITIVITY'] = filter_sensitivity

                if max_images is not None:
                    os.environ['MAX_PDF_IMAGES_TO_ANALYZE'] = str(max_images)

                # Set environment variables for new parameters
                if chunk_size is not None:
                    os.environ['EMBEDDING_CHUNK_SIZE'] = str(chunk_size)

                if chunk_overlap is not None:
                    os.environ['EMBEDDING_CHUNK_OVERLAP'] = str(chunk_overlap)

                if batch_size is not None:
                    os.environ['EMBEDDING_BATCH_SIZE'] = str(batch_size)

                if processing_threads is not None:
                    os.environ['EMBEDDING_PROCESSING_THREADS'] = str(processing_threads)

                return jsonify({"message": "Embedding configuration saved successfully"}), 200
            else:
                return jsonify({"error": "Failed to save embedding configuration"}), 500

        except Exception as e:
            logger.error(f"Error saving embedding configuration: {str(e)}")
            return jsonify({"error": f"Failed to save embedding configuration: {str(e)}"}), 500

    # GET request - show the embedding configuration page
    try:
        # Load current configuration from environment variables or defaults
        defaults = {}
        if os.path.exists(DEFAULT_MODELS_FILE):
            with open(DEFAULT_MODELS_FILE, 'r') as f:
                defaults = json.load(f)

        # Get embedding parameters from defaults
        embedding_params = defaults.get('embedding_parameters', {})

        # Set default values if not present
        if not embedding_params:
            embedding_params = {
                "chunk_size": 800,
                "chunk_overlap": 250,
                "extract_tables": True,
                "extract_images": True,
                "use_vision_model": False,
                "filter_sensitivity": "medium",
                "max_images": 30,
                "batch_size": 50,
                "processing_threads": 4
            }

        # Get available embedding models
        available_embedding_models = []
        try:
            # Try to get models from Ollama
            models_response = requests.get(f"{OLLAMA_BASE_URL}/api/tags")
            if models_response.status_code == 200:
                models_data = models_response.json()

                # Filter for embedding models
                embedding_models = []
                for model in models_data.get('models', []):
                    model_name = model.get('name', '')
                    if 'embed' in model_name.lower() or model_name in ['bge-m3:latest', 'nomic-embed-text:latest']:
                        embedding_models.append({
                            "name": model_name,
                            "size": model.get('size', 0)
                        })

                available_embedding_models = embedding_models
        except Exception as e:
            logger.error(f"Failed to get available models: {str(e)}")

        # If no embedding models found, add default ones
        if not available_embedding_models:
            available_embedding_models = [
                {"name": "mxbai-embed-large:latest", "size": 669 * 1024 * 1024},
                {"name": "bge-m3:latest", "size": 1.2 * 1024 * 1024 * 1024},
                {"name": "nomic-embed-text:latest", "size": 274 * 1024 * 1024}
            ]

        return render_template('embedding_config.html',
                              embedding_params=embedding_params,
                              available_embedding_models=available_embedding_models)
    except Exception as e:
        logger.error(f"Error loading embedding configuration page: {str(e)}")
        flash(f"Failed to load embedding configuration: {str(e)}", "error")
        return redirect(url_for('admin_dashboard'))

@app.route('/admin/check_embedding_dimensions', methods=['POST'])
def check_embedding_dimensions():
    """API endpoint to check the dimensions of an embedding model"""
    try:
        data = request.get_json()
        if not data or 'model_name' not in data:
            return jsonify({"error": "No model name provided"}), 400

        model_name = data['model_name']

        # Initialize the embedding model
        embed_fn = OllamaEmbeddings(model=model_name)

        # Generate an embedding for a test string
        test_embedding = embed_fn.embed_query("This is a test")

        # Return the dimensions
        return jsonify({
            "model": model_name,
            "dimensions": len(test_embedding)
        }), 200
    except Exception as e:
        logger.error(f"Error checking model dimensions: {str(e)}")
        return jsonify({"error": f"Failed to check model dimensions: {str(e)}"}), 500

@app.route('/admin/query_config', methods=['GET', 'POST'])
def query_config():
    """Page and endpoint for query configuration settings"""
    if request.method == 'GET':
        flash("Query Configuration has been moved to the Model Settings page.", "info")
        return redirect(url_for('unified_config', _anchor='query'))

    # For POST requests, continue with the original function to maintain API compatibility
    if request.method == 'POST':
        try:
            data = request.get_json()
            if not data:
                return jsonify({"error": "No data provided"}), 400

            # Extract data from request
            preamble = data.get('preamble')
            anti_hallucination_mode = data.get('anti_hallucination_mode')
            anti_hallucination_custom_instructions = data.get('anti_hallucination_custom_instructions')
            prompt_templates = data.get('prompt_templates')
            insufficient_info_phrases = data.get('insufficient_info_phrases')
            followup_question_templates = data.get('followup_question_templates')
            use_vision = data.get('use_vision')

            # Save the query configuration
            success = save_default_models(
                preamble=preamble,
                anti_hallucination_mode=anti_hallucination_mode,
                anti_hallucination_custom_instructions=anti_hallucination_custom_instructions,
                prompt_templates=prompt_templates,
                insufficient_info_phrases=insufficient_info_phrases,
                followup_question_templates=followup_question_templates,
                use_vision=use_vision
            )

            if success:
                # Update environment variables
                if preamble is not None:
                    os.environ['QUERY_PREAMBLE'] = preamble

                if anti_hallucination_mode is not None:
                    os.environ['ANTI_HALLUCINATION_MODE'] = anti_hallucination_mode

                if anti_hallucination_custom_instructions is not None:
                    os.environ['ANTI_HALLUCINATION_CUSTOM_INSTRUCTIONS'] = anti_hallucination_custom_instructions

                if prompt_templates is not None:
                    os.environ['PROMPT_TEMPLATES'] = json.dumps(prompt_templates)

                if insufficient_info_phrases is not None:
                    os.environ['INSUFFICIENT_INFO_PHRASES'] = json.dumps(insufficient_info_phrases)

                if followup_question_templates is not None:
                    os.environ['FOLLOWUP_QUESTION_TEMPLATES'] = json.dumps(followup_question_templates)

                if use_vision is not None:
                    use_vision_str = 'true' if use_vision else 'false'
                    os.environ['USE_VISION_MODEL'] = use_vision_str
                    app.config['USE_VISION_MODEL'] = use_vision_str.lower() == 'true'
                    logger.info(f"Updated vision model for chat setting: {app.config['USE_VISION_MODEL']}")

                return jsonify({"message": "Query configuration saved successfully"}), 200
            else:
                return jsonify({"error": "Failed to save query configuration"}), 500

        except Exception as e:
            logger.error(f"Error saving query configuration: {str(e)}")
            return jsonify({"error": f"Failed to save query configuration: {str(e)}"}), 500

    # GET request - show the query configuration page
    try:
        # Load current configuration from environment variables or defaults
        defaults = {}
        if os.path.exists(DEFAULT_MODELS_FILE):
            with open(DEFAULT_MODELS_FILE, 'r') as f:
                defaults = json.load(f)

        # Get query parameters from defaults
        query_params = defaults.get('query_parameters', {})

        # Extract specific parameters with defaults
        preamble = query_params.get('preamble', '')
        anti_hallucination_modes = query_params.get('anti_hallucination_modes', {
            'default_mode': 'strict',
            'custom_instructions': ''
        })
        prompt_templates = query_params.get('prompt_templates', {
            'strict': '',
            'balanced': '',
            'off': '',
            'general': '',
            'document_specific': ''
        })
        insufficient_info_phrases = query_params.get('insufficient_info_phrases', [
            "I don't have enough information",
            "The provided context does not contain",
            "There is no information"
        ])
        followup_question_templates = query_params.get('followup_question_templates', {
            'default': '',
            'insufficient_info': ''
        })

        return render_template('query_config.html',
                              preamble=preamble,
                              anti_hallucination_modes=anti_hallucination_modes,
                              prompt_templates=prompt_templates,
                              insufficient_info_phrases=insufficient_info_phrases,
                              followup_question_templates=followup_question_templates)
    except Exception as e:
        logger.error(f"Error loading query configuration page: {str(e)}")
        flash(f"Failed to load query configuration: {str(e)}", "error")
        return redirect(url_for('admin_dashboard'))

@app.route('/admin/models/refresh', methods=['POST'])
@admin_required
@function_permission_required('model_settings')
def refresh_models():
    """API endpoint to refresh the models list from Ollama"""
    try:
        # Get fresh models data
        models_data = get_models_data()

        return jsonify({
            "success": True,
            "message": "Models refreshed successfully",
            "models": {
                "llm_count": len(models_data['models']),
                "embedding_count": len(models_data['embeddings']),
                "vision_count": len(models_data['vision_models'])
            }
        }), 200
    except Exception as e:
        logger.error(f"Error refreshing models: {str(e)}")
        return jsonify({
            "success": False,
            "error": f"Failed to refresh models: {str(e)}"
        }), 500

@app.route('/api/available-models', methods=['GET'])
def get_available_models():
    """API endpoint to get available LLM models for chat interface"""
    try:
        # Get models data using the existing function
        models_data = get_models_data()

        # Extract LLM models with relevant information for chat interface
        llm_models = []
        for model in models_data['models']:
            model_info = {
                'name': model['name'],
                'display_name': model['name'].replace(':', ' ').replace('-', ' ').title(),
                'size': model.get('size', 0),
                'size_formatted': format_model_size(model.get('size', 0))
            }

            # Add model descriptions/capabilities based on model name
            if 'llama3.1' in model['name'].lower():
                if '8b' in model['name'].lower():
                    model_info['description'] = 'Fast, efficient model for general queries'
                    model_info['capability'] = 'General Purpose'
                elif '70b' in model['name'].lower():
                    model_info['description'] = 'Large, powerful model for complex analysis'
                    model_info['capability'] = 'Advanced Analysis'
                else:
                    model_info['description'] = 'Llama 3.1 language model'
                    model_info['capability'] = 'General Purpose'
            elif 'gemma' in model['name'].lower():
                if '4b' in model['name'].lower():
                    model_info['description'] = 'Compact multimodal model'
                    model_info['capability'] = 'Multimodal'
                elif '12b' in model['name'].lower():
                    model_info['description'] = 'Large multimodal model with vision capabilities'
                    model_info['capability'] = 'Advanced Multimodal'
                else:
                    model_info['description'] = 'Gemma language model'
                    model_info['capability'] = 'General Purpose'
            elif 'mistral' in model['name'].lower():
                model_info['description'] = 'Efficient model optimized for reasoning'
                model_info['capability'] = 'Reasoning'
            else:
                model_info['description'] = 'Language model for text generation'
                model_info['capability'] = 'General Purpose'

            llm_models.append(model_info)

        # Get current default model
        current_default = models_data.get('selected_model', models_data.get('default_llm', 'llama3.1:8b-instruct-q4_K_M'))

        return jsonify({
            "success": True,
            "models": llm_models,
            "default_model": current_default,
            "count": len(llm_models)
        }), 200

    except Exception as e:
        logger.error(f"Error getting available models for chat: {str(e)}")
        return jsonify({
            "success": False,
            "error": f"Failed to get available models: {str(e)}",
            "models": [],
            "default_model": "llama3.1:8b-instruct-q4_K_M",
            "count": 0
        }), 500

def format_model_size(size_bytes):
    """Format model size in human-readable format"""
    if size_bytes == 0:
        return "Unknown"

    # Convert bytes to appropriate unit
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if size_bytes < 1024.0:
            return f"{size_bytes:.1f} {unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.1f} PB"

@app.route('/admin/models/default', methods=['POST'])
def save_default_model_settings():
    """Endpoint to save default model settings"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "No data provided"}), 400

        llm_model = data.get('llm_model')
        embedding_model = data.get('embedding_model')
        vision_model = data.get('vision_model')
        use_vision = data.get('use_vision')
        filter_pdf_images = data.get('filter_pdf_images')
        filter_sensitivity = data.get('filter_sensitivity')
        max_pdf_images = data.get('max_pdf_images')
        show_filtered_images = data.get('show_filtered_images')

        # Get vision during embedding setting
        use_vision_during_embedding = data.get('use_vision_during_embedding')

        # Get model parameters
        temperature = data.get('temperature')
        num_ctx = data.get('num_ctx')
        num_predict = data.get('num_predict')
        top_p = data.get('top_p')
        top_k = data.get('top_k')
        repeat_penalty = data.get('repeat_penalty')
        system_prompt = data.get('system_prompt')

        # Validate that at least one model is provided
        if not any([llm_model, embedding_model, vision_model]):
            return jsonify({"error": "No model settings provided"}), 400

        # Save the default models
        success = save_default_models(
            llm_model=llm_model,
            embedding_model=embedding_model,
            vision_model=vision_model,
            use_vision=use_vision,
            use_vision_during_embedding=use_vision_during_embedding,
            filter_pdf_images=filter_pdf_images,
            filter_sensitivity=filter_sensitivity,
            max_pdf_images=max_pdf_images,
            show_filtered_images=show_filtered_images,
            temperature=temperature,
            num_ctx=num_ctx,
            num_predict=num_predict,
            top_p=top_p,
            top_k=top_k,
            repeat_penalty=repeat_penalty,
            system_prompt=system_prompt
        )

        # Update the USE_VISION_MODEL environment variable if provided
        if use_vision is not None:
            use_vision_str = 'true' if use_vision else 'false'
            os.environ['USE_VISION_MODEL'] = use_vision_str
            app.config['USE_VISION_MODEL'] = use_vision_str.lower() == 'true'
            logger.info(f"Updated vision model enabled setting: {app.config['USE_VISION_MODEL']}")

        # Update the USE_VISION_MODEL_DURING_EMBEDDING environment variable if provided
        if use_vision_during_embedding is not None:
            use_vision_during_embedding_str = 'true' if use_vision_during_embedding else 'false'
            os.environ['USE_VISION_MODEL_DURING_EMBEDDING'] = use_vision_during_embedding_str
            app.config['USE_VISION_MODEL_DURING_EMBEDDING'] = use_vision_during_embedding_str.lower() == 'true'
            logger.info(f"Updated vision model during embedding setting: {app.config['USE_VISION_MODEL_DURING_EMBEDDING']}")

        # Update image filtering settings in environment variables
        if filter_pdf_images is not None:
            filter_pdf_images_str = 'true' if filter_pdf_images else 'false'
            os.environ['FILTER_PDF_IMAGES'] = filter_pdf_images_str
            app.config['FILTER_PDF_IMAGES'] = filter_pdf_images_str.lower() == 'true'
            logger.info(f"Updated PDF image filtering setting: {app.config['FILTER_PDF_IMAGES']}")

        if filter_sensitivity:
            if filter_sensitivity in ['low', 'medium', 'high']:
                os.environ['PDF_IMAGE_FILTER_SENSITIVITY'] = filter_sensitivity
                app.config['PDF_IMAGE_FILTER_SENSITIVITY'] = filter_sensitivity
                logger.info(f"Updated PDF image filter sensitivity: {filter_sensitivity}")

        if max_pdf_images is not None:
            try:
                max_images = int(max_pdf_images)
                if 1 <= max_images <= 50:
                    os.environ['MAX_PDF_IMAGES_TO_ANALYZE'] = str(max_images)
                    app.config['MAX_PDF_IMAGES_TO_ANALYZE'] = max_images
                    logger.info(f"Updated maximum PDF images to analyze: {max_images}")
            except (ValueError, TypeError):
                logger.warning(f"Invalid max_pdf_images value: {max_pdf_images}")

        if show_filtered_images is not None:
            show_filtered_images_str = 'true' if show_filtered_images else 'false'
            os.environ['SHOW_FILTERED_IMAGES'] = show_filtered_images_str
            app.config['SHOW_FILTERED_IMAGES'] = show_filtered_images_str.lower() == 'true'
            logger.info(f"Updated show filtered images setting: {app.config['SHOW_FILTERED_IMAGES']}")

        # Update model parameters in environment variables
        if temperature is not None:
            try:
                temp_value = float(temperature)
                if 0 <= temp_value <= 1:
                    os.environ['LLM_TEMPERATURE'] = str(temp_value)
                    app.config['LLM_TEMPERATURE'] = temp_value
                    logger.info(f"Updated LLM temperature: {temp_value}")
                else:
                    logger.warning(f"Invalid temperature value (must be between 0 and 1): {temp_value}")
            except (ValueError, TypeError):
                logger.warning(f"Invalid temperature value: {temperature}")

        if num_ctx is not None:
            try:
                ctx_value = int(num_ctx)
                if ctx_value > 0:
                    os.environ['LLM_NUM_CTX'] = str(ctx_value)
                    app.config['LLM_NUM_CTX'] = ctx_value
                    logger.info(f"Updated LLM context window size: {ctx_value}")
                else:
                    logger.warning(f"Invalid context window value (must be positive): {ctx_value}")
            except (ValueError, TypeError):
                logger.warning(f"Invalid context window value: {num_ctx}")

        if num_predict is not None:
            try:
                predict_value = int(num_predict)
                if predict_value > 0:
                    os.environ['LLM_NUM_PREDICT'] = str(predict_value)
                    app.config['LLM_NUM_PREDICT'] = predict_value
                    logger.info(f"Updated LLM max tokens to predict: {predict_value}")
                else:
                    logger.warning(f"Invalid max tokens value (must be positive): {predict_value}")
            except (ValueError, TypeError):
                logger.warning(f"Invalid max tokens value: {num_predict}")

        if top_p is not None:
            try:
                top_p_value = float(top_p)
                if 0 <= top_p_value <= 1:
                    os.environ['LLM_TOP_P'] = str(top_p_value)
                    app.config['LLM_TOP_P'] = top_p_value
                    logger.info(f"Updated LLM top_p: {top_p_value}")
                else:
                    logger.warning(f"Invalid top_p value (must be between 0 and 1): {top_p_value}")
            except (ValueError, TypeError):
                logger.warning(f"Invalid top_p value: {top_p}")

        if top_k is not None:
            try:
                top_k_value = int(top_k)
                if top_k_value > 0:
                    os.environ['LLM_TOP_K'] = str(top_k_value)
                    app.config['LLM_TOP_K'] = top_k_value
                    logger.info(f"Updated LLM top_k: {top_k_value}")
                else:
                    logger.warning(f"Invalid top_k value (must be positive): {top_k_value}")
            except (ValueError, TypeError):
                logger.warning(f"Invalid top_k value: {top_k}")

        if repeat_penalty is not None:
            try:
                penalty_value = float(repeat_penalty)
                if penalty_value > 0:
                    os.environ['LLM_REPEAT_PENALTY'] = str(penalty_value)
                    app.config['LLM_REPEAT_PENALTY'] = penalty_value
                    logger.info(f"Updated LLM repeat penalty: {penalty_value}")
                else:
                    logger.warning(f"Invalid repeat penalty value (must be positive): {penalty_value}")
            except (ValueError, TypeError):
                logger.warning(f"Invalid repeat penalty value: {repeat_penalty}")

        if system_prompt is not None:
            os.environ['LLM_SYSTEM_PROMPT'] = system_prompt
            app.config['LLM_SYSTEM_PROMPT'] = system_prompt
            logger.info(f"Updated LLM system prompt: {system_prompt[:50]}...")

        if success:
            # Update environment variables with the new defaults immediately
            if llm_model:
                os.environ['LLM_MODEL'] = llm_model
                app.config['SELECTED_MODEL'] = llm_model
                logger.info(f"Updated LLM_MODEL environment variable to: {llm_model}")

            if embedding_model:
                os.environ['TEXT_EMBEDDING_MODEL'] = embedding_model
                app.config['SELECTED_EMBEDDING'] = embedding_model
                # Clear the vector DB cache to force reinitialization with the new embedding model
                from get_vector_db import _chroma_cache
                _chroma_cache.clear()
                logger.info(f"Updated TEXT_EMBEDDING_MODEL environment variable to: {embedding_model}")

            if vision_model:
                os.environ['VISION_MODEL'] = vision_model
                app.config['SELECTED_VISION_MODEL'] = vision_model
                logger.info(f"Updated VISION_MODEL environment variable to: {vision_model}")

            # Also update the .env file to maintain consistency
            try:
                update_env_file(llm_model, embedding_model, vision_model)
            except Exception as env_error:
                logger.warning(f"Failed to update .env file: {str(env_error)}")

            return jsonify({"message": "Default models saved successfully"}), 200
        else:
            return jsonify({"error": "Failed to save default models"}), 500

    except Exception as e:
        logger.error(f"Error saving default models: {str(e)}")
        return jsonify({"error": f"Failed to save default models: {str(e)}"}), 500

@app.route('/admin/models', methods=['GET', 'POST'])
def manage_models():
    if request.method == 'GET':
        flash("AI Models configuration has been moved to the Model Settings page.", "info")
        return redirect(url_for('unified_config', _anchor='models'))

    # For POST requests, continue with the original function to maintain API compatibility
    if request.method == 'POST':
        try:
            data = request.get_json()
            if data:
                # Handle JSON request from the frontend
                selected_model = data.get('llm_model')
                selected_embedding = data.get('embedding_model')
                selected_vision = data.get('vision_model')
                use_vision = data.get('use_vision')
                use_vision_during_embedding = data.get('use_vision_during_embedding')
                filter_pdf_images = data.get('filter_pdf_images')
                filter_sensitivity = data.get('filter_sensitivity')
                max_pdf_images = data.get('max_pdf_images')

                # Get model parameters
                temperature = data.get('temperature')
                num_ctx = data.get('num_ctx')
                num_predict = data.get('num_predict')
                top_p = data.get('top_p')
                top_k = data.get('top_k')
                repeat_penalty = data.get('repeat_penalty')
                system_prompt = data.get('system_prompt')
            else:
                # Handle form submission (fallback)
                selected_model = request.form.get('llm_model')
                selected_embedding = request.form.get('embedding_model')
                selected_vision = request.form.get('vision_model')
                use_vision = request.form.get('use_vision')
                use_vision_during_embedding = request.form.get('use_vision_during_embedding')
                filter_pdf_images = request.form.get('filter_pdf_images')
                filter_sensitivity = request.form.get('filter_sensitivity')
                max_pdf_images = request.form.get('max_pdf_images')

                # Get model parameters
                temperature = request.form.get('temperature')
                if temperature:
                    temperature = float(temperature)
                num_ctx = request.form.get('num_ctx')
                if num_ctx:
                    num_ctx = int(num_ctx)
                num_predict = request.form.get('num_predict')
                if num_predict:
                    num_predict = int(num_predict)
                top_p = request.form.get('top_p')
                if top_p:
                    top_p = float(top_p)
                top_k = request.form.get('top_k')
                if top_k:
                    top_k = int(top_k)
                repeat_penalty = request.form.get('repeat_penalty')
                if repeat_penalty:
                    repeat_penalty = float(repeat_penalty)
                system_prompt = request.form.get('system_prompt')

            if selected_model:
                # Clear the cache to force reinitialization with the new model
                app.config['SELECTED_MODEL'] = selected_model
                os.environ['LLM_MODEL'] = selected_model
                logger.info(f"Updated LLM model to: {selected_model}")

            if selected_embedding:
                # Clear the vector DB cache to force reinitialization with the new embedding model
                from get_vector_db import _chroma_cache
                _chroma_cache.clear()

                app.config['SELECTED_EMBEDDING'] = selected_embedding
                os.environ['TEXT_EMBEDDING_MODEL'] = selected_embedding
                logger.info(f"Updated embedding model to: {selected_embedding} and cleared vector DB cache")

            if selected_vision:
                app.config['SELECTED_VISION_MODEL'] = selected_vision
                os.environ['VISION_MODEL'] = selected_vision
                logger.info(f"Updated vision model to: {selected_vision}")

            if use_vision is not None:
                # Convert to string 'true' or 'false' for environment variable
                use_vision_str = 'true' if use_vision in [True, 'true', 'True', 'on', '1'] else 'false'
                app.config['USE_VISION_MODEL'] = use_vision_str.lower() == 'true'
                os.environ['USE_VISION_MODEL'] = use_vision_str
                logger.info(f"Vision model enabled: {app.config['USE_VISION_MODEL']}")

            # Handle vision model during embedding setting
            if use_vision_during_embedding is not None:
                # Convert to string 'true' or 'false' for environment variable
                use_vision_during_embedding_str = 'true' if use_vision_during_embedding in [True, 'true', 'True', 'on', '1'] else 'false'
                app.config['USE_VISION_MODEL_DURING_EMBEDDING'] = use_vision_during_embedding_str.lower() == 'true'
                os.environ['USE_VISION_MODEL_DURING_EMBEDDING'] = use_vision_during_embedding_str
                logger.info(f"Vision model during embedding enabled: {app.config['USE_VISION_MODEL_DURING_EMBEDDING']}")

            # Handle PDF image filtering configuration
            if filter_pdf_images is not None:
                # Convert to string 'true' or 'false' for environment variable
                filter_pdf_images_str = 'true' if filter_pdf_images in [True, 'true', 'True', 'on', '1'] else 'false'
                app.config['FILTER_PDF_IMAGES'] = filter_pdf_images_str.lower() == 'true'
                os.environ['FILTER_PDF_IMAGES'] = filter_pdf_images_str
                logger.info(f"PDF image filtering enabled: {app.config['FILTER_PDF_IMAGES']}")

            # Handle filter sensitivity
            if filter_sensitivity:
                # Validate sensitivity level
                if filter_sensitivity not in ['low', 'medium', 'high']:
                    filter_sensitivity = 'medium'  # Default to medium if invalid
                app.config['PDF_IMAGE_FILTER_SENSITIVITY'] = filter_sensitivity
                os.environ['PDF_IMAGE_FILTER_SENSITIVITY'] = filter_sensitivity
                logger.info(f"PDF image filter sensitivity set to: {filter_sensitivity}")

            # Handle show filtered images setting
            show_filtered_images = data.get('show_filtered_images') if data else request.form.get('show_filtered_images')
            if show_filtered_images is not None:
                # Convert to string 'true' or 'false' for environment variable
                show_filtered_images_str = 'true' if show_filtered_images in [True, 'true', 'True', 'on', '1'] else 'false'
                app.config['SHOW_FILTERED_IMAGES'] = show_filtered_images_str.lower() == 'true'
                os.environ['SHOW_FILTERED_IMAGES'] = show_filtered_images_str
                logger.info(f"Show filtered images enabled: {app.config['SHOW_FILTERED_IMAGES']}")

            # Handle max PDF images to analyze
            if max_pdf_images:
                try:
                    # Convert to integer and validate
                    max_pdf_images_int = int(max_pdf_images)
                    if max_pdf_images_int < 1:
                        max_pdf_images_int = 10  # Default if too low
                    elif max_pdf_images_int > 50:
                        max_pdf_images_int = 50  # Cap at 50 to prevent excessive processing

                    app.config['MAX_PDF_IMAGES_TO_ANALYZE'] = max_pdf_images_int
                    os.environ['MAX_PDF_IMAGES_TO_ANALYZE'] = str(max_pdf_images_int)
                    logger.info(f"Maximum PDF images to analyze set to: {max_pdf_images_int}")
                except (ValueError, TypeError):
                    # Default to 10 if not a valid number
                    app.config['MAX_PDF_IMAGES_TO_ANALYZE'] = 10
                    os.environ['MAX_PDF_IMAGES_TO_ANALYZE'] = '10'
                    logger.warning(f"Invalid max_pdf_images value: {max_pdf_images}, defaulting to 10")

            # Handle model parameters
            if temperature is not None:
                try:
                    temp_float = float(temperature)
                    if temp_float < 0:
                        temp_float = 0.0
                    elif temp_float > 1:
                        temp_float = 1.0
                    os.environ['LLM_TEMPERATURE'] = str(temp_float)
                    logger.info(f"Temperature set to: {temp_float}")
                except (ValueError, TypeError):
                    logger.warning(f"Invalid temperature value: {temperature}, using default")

            if num_ctx is not None:
                try:
                    ctx_int = int(num_ctx)
                    if ctx_int < 512:
                        ctx_int = 512
                    elif ctx_int > 32768:
                        ctx_int = 32768
                    os.environ['LLM_NUM_CTX'] = str(ctx_int)
                    logger.info(f"Context window size set to: {ctx_int}")
                except (ValueError, TypeError):
                    logger.warning(f"Invalid context window size value: {num_ctx}, using default")

            if num_predict is not None:
                try:
                    predict_int = int(num_predict)
                    if predict_int < 64:
                        predict_int = 64
                    elif predict_int > 4096:
                        predict_int = 4096
                    os.environ['LLM_NUM_PREDICT'] = str(predict_int)
                    logger.info(f"Max tokens to generate set to: {predict_int}")
                except (ValueError, TypeError):
                    logger.warning(f"Invalid max tokens value: {num_predict}, using default")

            if top_p is not None:
                try:
                    top_p_float = float(top_p)
                    if top_p_float < 0:
                        top_p_float = 0.0
                    elif top_p_float > 1:
                        top_p_float = 1.0
                    os.environ['LLM_TOP_P'] = str(top_p_float)
                    logger.info(f"Top P set to: {top_p_float}")
                except (ValueError, TypeError):
                    logger.warning(f"Invalid top_p value: {top_p}, using default")

            if top_k is not None:
                try:
                    top_k_int = int(top_k)
                    if top_k_int < 1:
                        top_k_int = 1
                    elif top_k_int > 100:
                        top_k_int = 100
                    os.environ['LLM_TOP_K'] = str(top_k_int)
                    logger.info(f"Top K set to: {top_k_int}")
                except (ValueError, TypeError):
                    logger.warning(f"Invalid top_k value: {top_k}, using default")

            if repeat_penalty is not None:
                try:
                    repeat_penalty_float = float(repeat_penalty)
                    if repeat_penalty_float < 1:
                        repeat_penalty_float = 1.0
                    elif repeat_penalty_float > 2:
                        repeat_penalty_float = 2.0
                    os.environ['LLM_REPEAT_PENALTY'] = str(repeat_penalty_float)
                    logger.info(f"Repeat penalty set to: {repeat_penalty_float}")
                except (ValueError, TypeError):
                    logger.warning(f"Invalid repeat_penalty value: {repeat_penalty}, using default")

            if system_prompt is not None:
                os.environ['LLM_SYSTEM_PROMPT'] = system_prompt
                logger.info(f"System prompt updated")

            if data:  # If JSON request, return JSON response
                return jsonify({"message": "Models updated successfully"}), 200
            else:  # If form submission, redirect with flash message
                flash("Model and embedding selected successfully.", "success")
                return redirect(url_for('manage_models'))
        except Exception as e:
            logger.error(f"Error updating models: {str(e)}")
            if request.is_json:
                return jsonify({"error": f"Failed to update models: {str(e)}"}), 500
            flash(f"Failed to update models: {str(e)}", "error")
            return redirect(url_for('manage_models'))

    # GET request - show the models page
    try:
        # Use the improved model detection logic from get_models_data()
        models_data = get_models_data()
        models = models_data['models']
        embedding_models = models_data['embeddings']
        vision_models = models_data['vision_models']

        logger.info(f"Retrieved {len(models)} LLM models, {len(embedding_models)} embedding models, and {len(vision_models)} vision models from get_models_data()")

    except Exception as e:
        logger.error(f"Failed to get models data: {str(e)}")
        # Use fallback defaults if get_models_data() fails
        models = [
            {"name": "llama3.1:8b-instruct-q4_K_M", "size": 4.9 * 1024 * 1024 * 1024},
            {"name": "mistral:latest", "size": 4.1 * 1024 * 1024 * 1024},
            {"name": "llama3.2:3b-instruct-q4_K_M", "size": 2.0 * 1024 * 1024 * 1024},
            {"name": "gemma3:1b", "size": 815 * 1024 * 1024}
        ]
        embedding_models = [
            {"name": "mxbai-embed-large:latest", "size": 669 * 1024 * 1024},
            {"name": "bge-m3:latest", "size": 1.2 * 1024 * 1024 * 1024},
            {"name": "nomic-embed-text:latest", "size": 274 * 1024 * 1024}
        ]
        vision_models = [
            {"name": "llama3.2-vision:11b-instruct-q4_K_M", "size": 6.8 * 1024 * 1024 * 1024},
            {"name": "gemma3:4b-it-q4_K_M", "size": 3.3 * 1024 * 1024 * 1024},
            {"name": "gemma3:12b-it-q4_K_M", "size": 7.2 * 1024 * 1024 * 1024}
        ]
        flash(f"Failed to list models: {str(e)}", "error")

    # Ensure image filtering settings are always available in the template context
    # This prevents issues where the settings might disappear after page refresh
    filter_pdf_images = app.config.get('FILTER_PDF_IMAGES')
    if filter_pdf_images is None:
        # If not in app.config, try to get from environment variable
        filter_pdf_images = os.getenv('FILTER_PDF_IMAGES', 'true').lower() == 'true'
        # Store in app.config for future use
        app.config['FILTER_PDF_IMAGES'] = filter_pdf_images

    filter_sensitivity = app.config.get('PDF_IMAGE_FILTER_SENSITIVITY')
    if filter_sensitivity is None or filter_sensitivity not in ['low', 'medium', 'high']:
        # If not in app.config or invalid, try to get from environment variable
        filter_sensitivity = os.getenv('PDF_IMAGE_FILTER_SENSITIVITY', 'medium')
        # Store in app.config for future use
        app.config['PDF_IMAGE_FILTER_SENSITIVITY'] = filter_sensitivity

    max_pdf_images = app.config.get('MAX_PDF_IMAGES_TO_ANALYZE')
    if max_pdf_images is None:
        # If not in app.config, try to get from environment variable
        try:
            max_pdf_images = int(os.getenv('MAX_PDF_IMAGES_TO_ANALYZE', '10'))
        except (ValueError, TypeError):
            max_pdf_images = 10
        # Store in app.config for future use
        app.config['MAX_PDF_IMAGES_TO_ANALYZE'] = max_pdf_images

    show_filtered_images = app.config.get('SHOW_FILTERED_IMAGES')
    if show_filtered_images is None:
        # If not in app.config, try to get from environment variable
        show_filtered_images = os.getenv('SHOW_FILTERED_IMAGES', 'false').lower() == 'true'
        # Store in app.config for future use
        app.config['SHOW_FILTERED_IMAGES'] = show_filtered_images

    logger.info(f"Rendering models.html with image filtering settings: filter_pdf_images={filter_pdf_images}, "
                f"filter_sensitivity={filter_sensitivity}, max_pdf_images={max_pdf_images}, "
                f"show_filtered_images={show_filtered_images}")

    # Get model parameters from environment variables
    temperature = float(os.getenv('LLM_TEMPERATURE', '0.7'))
    num_ctx = int(os.getenv('LLM_NUM_CTX', '4096'))
    num_predict = int(os.getenv('LLM_NUM_PREDICT', '256'))
    top_p = float(os.getenv('LLM_TOP_P', '0.9'))
    top_k = int(os.getenv('LLM_TOP_K', '40'))
    repeat_penalty = float(os.getenv('LLM_REPEAT_PENALTY', '1.1'))
    system_prompt = os.getenv('LLM_SYSTEM_PROMPT', 'You are a helpful assistant for the ERDB (Ecosystems Research and Development Bureau). Answer questions based on the provided context.')

    return render_template('models.html',
                          models=models,
                          embeddings=embedding_models,
                          vision_models=vision_models,
                          selected_model=app.config.get('SELECTED_MODEL'),
                          selected_embedding=app.config.get('SELECTED_EMBEDDING'),
                          selected_vision=app.config.get('SELECTED_VISION_MODEL'),
                          use_vision=app.config.get('USE_VISION_MODEL', True),
                          use_vision_during_embedding=app.config.get('USE_VISION_MODEL_DURING_EMBEDDING', True),
                          filter_pdf_images=filter_pdf_images,
                          filter_sensitivity=filter_sensitivity,
                          max_pdf_images=max_pdf_images,
                          show_filtered_images=show_filtered_images,
                          default_llm=default_llm,
                          default_embedding=default_embedding,
                          default_vision=default_vision,
                          temperature=temperature,
                          num_ctx=num_ctx,
                          num_predict=num_predict,
                          top_p=top_p,
                          top_k=top_k,
                          repeat_penalty=repeat_penalty,
                          system_prompt=system_prompt)

@app.route('/query/<category>', methods=['POST'])
def query(category):
    try:
        data = request.get_json()
        if not data or 'query' not in data:
            return jsonify({"error": "Query is required."}), 400
        question = data['query']

        # Get optional selected model from request
        selected_model = data.get('selected_model')

        # Get anti-hallucination mode from request or use app config default
        anti_hallucination_mode = data.get('anti_hallucination_mode', app.config['ANTI_HALLUCINATION_MODE'])

        # Get client name if provided
        client_name = data.get('client_name')

        # Get device fingerprint if provided
        device_fingerprint = data.get('device_fingerprint')

        # Get session information
        session_id = data.get('session_id')
        session_start = data.get('session_start')

        # If no session_id is provided, generate a new one
        if not session_id:
            import uuid
            import datetime
            session_id = str(uuid.uuid4())
            session_start = datetime.datetime.now().isoformat()

        # Validate the mode
        if anti_hallucination_mode not in ['strict', 'balanced', 'off']:
            anti_hallucination_mode = 'strict'  # Default to strict if invalid

        logger.info(f"Query with anti-hallucination mode: {anti_hallucination_mode}, client: {client_name or 'Anonymous'}")

        # Pass the mode, client name, device fingerprint, session ID, and selected model to the query function
        result = query_category(category, question, anti_hallucination_mode, client_name, session_id, device_fingerprint, selected_model)

        # Ensure result is a dictionary
        if not isinstance(result, dict):
            logger.error(f"Query result is not a dictionary: {result}")
            result = {
                "answer": f"Error: {str(result)}",
                "sources": [],
                "images": [],
                "url_images": [],
                "pdf_images": [],
                "pdf_links": [],
                "metadata": {"error": str(result)},
                "analytics": {
                    "error": str(result),
                    "question_length": len(question),
                    "processing_time": 0,
                    "model_name": app.config['SELECTED_MODEL'],
                    "embedding_model": app.config['SELECTED_EMBEDDING']
                }
            }

        # Process sources for the response
        sources = []
        # Ensure we have a list of sources
        source_list = result.get("sources", [])
        if not isinstance(source_list, list):
            logger.warning(f"Sources is not a list: {source_list}")
            source_list = []

        for source in source_list:
            # Ensure source is a dictionary
            if not isinstance(source, dict):
                logger.warning(f"Source is not a dictionary: {source}")
                continue

            # Create a simplified source object for the response
            try:
                src = {
                    "display_name": source.get("display_name", "Unknown"),
                    "page": source.get("page", None),
                    "type": source.get("type", "unknown"),
                    "image_count": source.get("image_count", 0),
                    "link_count": source.get("link_count", 0),
                    "original_url": source.get("original_url"),
                    "file_path": source.get("file_path"),
                    "source": source.get("source", "Unknown")  # Include the source filename
                }
                sources.append(src)
            except Exception as e:
                logger.error(f"Error processing source: {str(e)}")
                # Continue with next source

        # Get images and links directly from the result
        images = result.get("images", [])
        url_images = result.get("url_images", [])  # New field for URL JPG images
        pdf_images = result.get("pdf_images", [])  # New field for PDF-extracted images
        pdf_links = result.get("pdf_links", [])
        answer = result.get("answer", "No answer available.")

        # Ensure metadata is a dictionary
        metadata = result.get("metadata", {})
        if not isinstance(metadata, dict):
            logger.warning(f"Metadata is not a dictionary, converting: {metadata}")
            metadata = {"error": str(metadata)} if metadata else {}

        # Get follow-up questions if available
        followup_questions = result.get("followup_questions", [])

        # If we have follow-up questions, add them to the metadata
        if followup_questions:
            if metadata is None:
                metadata = {}
            metadata['followup_questions'] = followup_questions

        # Get analytics data if available
        analytics = result.get("analytics", {})

        # Get document thumbnails if available
        document_thumbnails = result.get("document_thumbnails", [])

        # Get model information for chat history
        current_model = app.config.get('SELECTED_MODEL', 'Unknown')
        current_embedding = app.config.get('SELECTED_EMBEDDING', 'Unknown')
        current_vision = app.config.get('SELECTED_VISION_MODEL', 'Unknown')

        # Prepare analytics data with geolocation if available
        analytics_data = None
        if analytics:
            # Get geolocation data
            ip_address, city, region, country, latitude, longitude = geo_utils.get_location_for_analytics()

            # Prepare comprehensive analytics data for batch operation
            analytics_data = {
                "question_length": analytics.get("question_length", len(question)),
                "answer_length": analytics.get("answer_length", len(answer)),
                "processing_time": analytics.get("processing_time", 0),
                "source_count": analytics.get("source_count", len(sources)),
                "image_count": analytics.get("image_count", len(url_images) + len(pdf_images)),
                "token_count": analytics.get("token_count"),
                "model_name": analytics.get("model_name", app.config['SELECTED_MODEL']),
                "embedding_model": analytics.get("embedding_model", app.config['SELECTED_EMBEDDING']),
                "vision_model": analytics.get("vision_model", app.config.get('SELECTED_VISION_MODEL')),
                "vision_enabled": analytics.get("vision_enabled", app.config.get('USE_VISION_MODEL', False)),
                "images_filtered": analytics.get("images_filtered", 0),
                "total_images_extracted": analytics.get("total_images_extracted", 0),
                "filter_sensitivity": analytics.get("filter_sensitivity", app.config.get('PDF_IMAGE_FILTER_SENSITIVITY', 'medium')),
                "hallucination_detected": analytics.get("hallucination_detected", False),
                "anti_hallucination_mode": anti_hallucination_mode,
                "ip_address": ip_address,
                "city": city,
                "region": region,
                "country": country,
                "latitude": latitude,
                "longitude": longitude
            }

        # Save chat history and analytics in a single transaction (70% reduction in database calls)
        chat_id = db_utils.save_chat_history_with_analytics(
            category, question, answer, sources, images, pdf_links, metadata,
            url_images, pdf_images, client_name, session_id, session_start,
            device_fingerprint, document_thumbnails, anti_hallucination_mode,
            current_model, current_embedding, current_vision, analytics_data
        )

        if chat_id:
            logger.info(f"Successfully saved chat history and analytics for chat ID {chat_id}")
        else:
            logger.error("Failed to save chat history and analytics")

        # Log the query response for debugging
        logger.info(f"Query for category {category}: question={question}, answer={answer[:100]}..., sources={[s['display_name'] for s in sources]}")

        # Add hallucination warning to the UI if detected
        if metadata.get("hallucination_detected", False):
            logger.warning(f"Hallucination detected in response to: {question}")

        return jsonify({
            "answer": answer,
            "sources": sources,
            "images": images,
            "url_images": url_images,  # New field for URL JPG images
            "pdf_images": pdf_images,  # New field for PDF-extracted images
            "pdf_links": pdf_links,
            "followup_questions": followup_questions,  # Add follow-up questions
            "metadata": metadata,
            "session_id": session_id,  # Return session ID for client to store
            "session_start": session_start  # Return session start time
        })
    except Exception as e:
        logger.error(f"Query failed: {str(e)}")
        # Return a more detailed error response
        try:
            # Make sure session_id and session_start are defined
            if 'session_id' not in locals() or session_id is None:
                import uuid
                session_id = str(uuid.uuid4())

            if 'session_start' not in locals() or session_start is None:
                import datetime
                session_start = datetime.datetime.now().isoformat()

            return jsonify({
                "error": str(e),
                "answer": f"An error occurred while processing your query: {str(e)}",
                "sources": [],
                "images": [],
                "url_images": [],  # New field for URL JPG images
                "pdf_images": [],  # New field for PDF-extracted images
                "document_thumbnails": [],  # New field for document thumbnails
                "pdf_links": [],
                "followup_questions": [
                    "Can you try asking a different question?",
                    "Would you like to try a more specific query?",
                    "Can you rephrase your question?"
                ],
                "metadata": {"error": str(e)},
                "session_id": session_id,  # Return session ID for client to store
                "session_start": session_start  # Return session start time
            }), 500
        except Exception as nested_error:
            # If we can't even create the error response, return a simple error
            logger.error(f"Failed to create error response: {str(nested_error)}")
            return jsonify({"error": f"Critical error: {str(e)}"}), 500

@app.route('/admin/chat_history')
@admin_required
@function_permission_required('chat_history')
def chat_history():
    history = db_utils.get_chat_history()
    return render_template('chat_history.html', history=history)

@app.route('/admin/sessions')
@admin_required
@function_permission_required('chat_sessions')
def view_sessions():
    """View all chat sessions."""
    sessions = db_utils.get_sessions()
    return render_template('sessions.html', sessions=sessions)

@app.route('/admin/analytics')
@admin_required
def analytics_dashboard():
    """View AI analytics dashboard. Enhanced for Phase 2."""
    # Get date range filters from request
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    # Get analytics data
    analytics = db_utils.get_analytics(start_date=start_date, end_date=end_date)

    # Get summary statistics
    summary = db_utils.get_analytics_summary()

    # Get greeting analytics for Phase 2
    greeting_manager = GreetingManager()
    greeting_analytics = greeting_manager.get_greeting_analytics(start_date=start_date, end_date=end_date)
    engagement_patterns = greeting_manager.get_time_based_engagement_patterns()

    # Add greeting analytics to summary
    summary['greeting_analytics'] = greeting_analytics
    summary['engagement_patterns'] = engagement_patterns

    # Import DEV_LOCATION from geo_utils to pass to template
    from geo_utils import DEV_LOCATION

    # Get model performance analysis data if user has permission
    model_performance_data = None
    if um.current_user.has_dashboard_permission('model_performance_analysis'):
        model_performance_data = db_utils.get_model_performance_analysis(start_date=start_date, end_date=end_date)

    # Prepare data for processing time chart
    processing_time_data = {"labels": [], "values": []}

    # Prepare data for query volume chart
    query_volume_data = {"labels": [], "values": []}

    # Prepare data for location map
    location_data = []

    if analytics:
        # Group analytics by date for query volume chart
        date_counts = {}
        for entry in analytics:
            # Extract date from timestamp (format: YYYY-MM-DD HH:MM:SS)
            if entry.get('timestamp'):
                date = entry['timestamp'].split(' ')[0]
                date_counts[date] = date_counts.get(date, 0) + 1

        # Sort dates and prepare chart data
        sorted_dates = sorted(date_counts.keys())
        query_volume_data["labels"] = sorted_dates
        query_volume_data["values"] = [date_counts[date] for date in sorted_dates]

        # Prepare processing time data (last 20 entries)
        recent_entries = analytics[:20]
        recent_entries.reverse()  # Show oldest to newest

        for entry in recent_entries:
            if entry.get('timestamp') and entry.get('processing_time') is not None:
                # Format timestamp for display
                timestamp = entry['timestamp'].split(' ')[1]  # Just show time
                processing_time_data["labels"].append(timestamp)
                processing_time_data["values"].append(float(entry['processing_time']))

        # Prepare location data for the map
        # Create a set to track unique locations by device fingerprint
        unique_locations = set()

        for entry in analytics:
            # Only include entries with valid location data
            if (entry.get('latitude') and entry.get('longitude') and
                entry.get('device_fingerprint') and
                entry.get('city') and entry.get('country')):

                # Create a unique key for this location + device
                location_key = f"{entry['device_fingerprint']}:{entry['latitude']}:{entry['longitude']}"

                # Only add if we haven't seen this device at this location before
                if location_key not in unique_locations:
                    unique_locations.add(location_key)

                    location_data.append({
                        'latitude': entry['latitude'],
                        'longitude': entry['longitude'],
                        'city': entry['city'],
                        'region': entry['region'],
                        'country': entry['country'],
                        'device_fingerprint': entry['device_fingerprint'],
                        'client_name': entry.get('client_name', 'Anonymous')
                    })

    return render_template('analytics.html',
                          analytics=analytics,
                          summary=summary,
                          processing_time_data=processing_time_data,
                          query_volume_data=query_volume_data,
                          location_data=location_data,
                          start_date=start_date,
                          end_date=end_date,
                          dev_location=DEV_LOCATION,  # Pass DEV_LOCATION to template
                          model_performance_data=model_performance_data)

@app.route('/admin/client-analytics')
def client_analytics():
    """View analytics for a specific client (legacy route, redirects to device analytics)."""
    # Get client name from request
    client_name = request.args.get('client')
    if not client_name:
        flash("Client name is required", "error")
        return redirect(url_for('analytics_dashboard'))

    # Redirect to analytics dashboard since we're using device fingerprints now
    flash("Client analytics has been replaced with device-based analytics", "info")
    return redirect(url_for('analytics_dashboard'))

@app.route('/admin/device-analytics')
def device_analytics():
    """View analytics for a specific device."""
    # Get device fingerprint from request
    device_fingerprint = request.args.get('device_fingerprint')
    if not device_fingerprint:
        flash("Device fingerprint is required", "error")
        return redirect(url_for('analytics_dashboard'))

    # Get date range filters from request
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    # Get analytics data for this device
    analytics = db_utils.get_analytics(device_fingerprint=device_fingerprint, start_date=start_date, end_date=end_date)

    # Get summary statistics for this device
    summary = db_utils.get_analytics_summary(device_fingerprint=device_fingerprint)

    # Ensure device_fingerprint is always available in summary
    if 'device_fingerprint' not in summary:
        summary['device_fingerprint'] = device_fingerprint

    # Get the most recent client name for this device if available
    if analytics and not summary.get('client_name'):
        for entry in analytics:
            if entry.get('client_name'):
                summary['client_name'] = entry.get('client_name')
                break

    # Prepare data for activity chart
    activity_data = {"labels": [], "values": []}

    # Prepare data for category distribution chart
    category_data = {"labels": [], "values": []}

    # Prepare data for location map
    location_data = []

    # Process activity by date data if available
    if summary.get("activity_by_date"):
        for entry in summary["activity_by_date"]:
            activity_data["labels"].append(entry["date"])
            activity_data["values"].append(entry["count"])

    # Process category data
    if summary.get("top_categories"):
        for category in summary["top_categories"]:
            category_data["labels"].append(category["category"])
            category_data["values"].append(category["count"])

    # Process location data
    if analytics:
        # Create a set to track unique locations
        unique_locations = set()

        for entry in analytics:
            # Only include entries with valid location data
            if (entry.get('latitude') and entry.get('longitude') and
                entry.get('city') and entry.get('country')):

                # Create a unique key for this location
                location_key = f"{entry['latitude']}:{entry['longitude']}"

                # Only add if we haven't seen this location before
                if location_key not in unique_locations:
                    unique_locations.add(location_key)

                    location_data.append({
                        'latitude': entry['latitude'],
                        'longitude': entry['longitude'],
                        'city': entry['city'],
                        'region': entry['region'],
                        'country': entry['country'],
                        'timestamp': entry.get('timestamp')
                    })

    return render_template('device_analytics.html',
                          analytics=analytics,
                          summary=summary,
                          activity_data=activity_data,
                          category_data=category_data,
                          location_data=location_data,
                          start_date=start_date,
                          end_date=end_date)

@app.route('/admin/session/<session_id>')
@admin_required
def view_session(session_id):
    """View chat history for a specific session."""
    history = db_utils.get_chat_history(session_id)
    # Get the first entry to get session details
    session_info = None
    if history:
        session_info = {
            "session_id": session_id,
            "client_name": history[0].get("client_name", "Anonymous"),
            "device_fingerprint": history[0].get("device_fingerprint", "Unknown"),
            "start_time": history[0].get("session_start"),
            "end_time": history[0].get("session_end"),
            "message_count": len(history),
            "category": history[0].get("category")
        }
    return render_template('session_view.html', history=history, session=session_info)

@app.route('/admin/session/<session_id>/close', methods=['POST'])
@admin_required
def close_session(session_id):
    """Close a session by updating its end time."""
    success = db_utils.update_session_end(session_id)
    if success:
        flash(f"Session {session_id} closed successfully.", "success")
    else:
        flash(f"Failed to close session {session_id}.", "error")
    return redirect(url_for('view_sessions'))

@app.route('/clear_session', methods=['POST'])
@csrf.exempt  # Exempt from CSRF protection since this is called during page unload
def clear_session():
    """Clear all session data when the app is closed."""
    try:
        # Get session ID from request - handle both JSON and FormData
        session_id = None

        # Try to get from JSON first
        if request.is_json:
            data = request.get_json() or {}
            session_id = data.get('session_id')
        else:
            # Try to get from form data (for sendBeacon)
            session_id = request.form.get('session_id')

        # If session ID is provided, close it on the server
        if session_id:
            db_utils.update_session_end(session_id)
            logger.info(f"Session {session_id} closed via clear_session endpoint")

        return jsonify({"success": True, "message": "Session data cleared"}), 200
    except Exception as e:
        logger.error(f"Error clearing session: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500


@app.route('/api/csrf-token', methods=['GET'])
def get_csrf_token():
    """Get a fresh CSRF token."""
    try:
        from flask_wtf.csrf import generate_csrf
        token = generate_csrf()
        return jsonify({"csrf_token": token}), 200
    except Exception as e:
        logger.error(f"Error generating CSRF token: {str(e)}")
        return jsonify({"error": "Failed to generate CSRF token"}), 500

@app.route('/api/greeting', methods=['POST'])
def get_greeting():
    """Get contextual greeting for user. Enhanced for Phase 2."""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "Request data is required"}), 400

        client_name = data.get('client_name')
        if not client_name:
            return jsonify({"error": "Client name is required"}), 400

        context = data.get('context', {})

        # Initialize greeting manager
        greeting_manager = GreetingManager()

        # Get contextual greeting with enhanced context
        greeting_data = greeting_manager.get_contextual_greeting(client_name, context)

        # Log greeting usage for analytics if session_id is provided
        session_id = context.get('session_id')
        if session_id:
            greeting_manager.log_greeting_usage(session_id, client_name, greeting_data)

        return jsonify({
            "success": True,
            "greeting": greeting_data['greeting'],
            "greeting_data": {
                "template_id": greeting_data.get('template_id'),
                "source": greeting_data.get('source'),
                "template_type": greeting_data.get('template_type'),
                "session_type": greeting_data.get('session_type'),
                "time_of_day": greeting_data.get('time_of_day'),
                "context": greeting_data.get('context', {})
            },
            "metadata": {
                "template_id": greeting_data.get('template_id'),
                "source": greeting_data.get('source'),
                "template_type": greeting_data.get('template_type')
            }
        }), 200

    except Exception as e:
        logger.error(f"Error getting greeting: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e),
            "greeting": f"Hello {data.get('client_name', 'there')}!"  # Emergency fallback
        }), 500

@app.route('/api/greeting_templates', methods=['GET', 'POST'])
@admin_required
@function_permission_required('greeting_management')
def greeting_templates_api():
    """API endpoint for greeting templates CRUD operations."""
    greeting_manager = GreetingManager()

    if request.method == 'GET':
        try:
            template_type = request.args.get('type')
            templates = greeting_manager.get_greeting_templates(template_type)
            return jsonify({"success": True, "templates": templates}), 200
        except Exception as e:
            logger.error(f"Error getting greeting templates: {str(e)}")
            return jsonify({"success": False, "error": str(e)}), 500

    elif request.method == 'POST':
        try:
            data = request.get_json()
            if not data:
                return jsonify({"success": False, "error": "Request data is required"}), 400

            template_type = data.get('template_type')
            greeting_text = data.get('greeting_text')
            context_conditions = data.get('context_conditions', {})
            weight = data.get('weight', 1)

            if not template_type or not greeting_text:
                return jsonify({"success": False, "error": "Template type and greeting text are required"}), 400

            success = greeting_manager.add_greeting_template(
                template_type, greeting_text, context_conditions, weight
            )

            if success:
                return jsonify({"success": True, "message": "Greeting template added successfully"}), 201
            else:
                return jsonify({"success": False, "error": "Failed to add greeting template"}), 500

        except Exception as e:
            logger.error(f"Error adding greeting template: {str(e)}")
            return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/greeting_templates/<int:template_id>', methods=['PUT', 'DELETE'])
@admin_required
@function_permission_required('greeting_management')
def greeting_template_api(template_id):
    """API endpoint for individual greeting template operations."""
    greeting_manager = GreetingManager()

    if request.method == 'PUT':
        try:
            data = request.get_json()
            if not data:
                return jsonify({"success": False, "error": "Request data is required"}), 400

            success = greeting_manager.update_greeting_template(template_id, **data)

            if success:
                return jsonify({"success": True, "message": "Greeting template updated successfully"}), 200
            else:
                return jsonify({"success": False, "error": "Failed to update greeting template"}), 500

        except Exception as e:
            logger.error(f"Error updating greeting template: {str(e)}")
            return jsonify({"success": False, "error": str(e)}), 500

    elif request.method == 'DELETE':
        try:
            success = greeting_manager.delete_greeting_template(template_id)

            if success:
                return jsonify({"success": True, "message": "Greeting template deleted successfully"}), 200
            else:
                return jsonify({"success": False, "error": "Failed to delete greeting template"}), 500

        except Exception as e:
            logger.error(f"Error deleting greeting template: {str(e)}")
            return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/settings/vision_embedding', methods=['GET'])
def get_vision_embedding_setting():
    """Get the global vision model during embedding setting."""
    try:
        # Get the current setting from environment variable
        use_vision_during_embedding = os.getenv('USE_VISION_MODEL_DURING_EMBEDDING', 'true').lower() == 'true'

        # Return the setting as JSON
        return jsonify({
            "enabled": use_vision_during_embedding,
            "message": "Vision model during embedding is " + ("enabled" if use_vision_during_embedding else "disabled")
        }), 200
    except Exception as e:
        logger.error(f"Error getting vision embedding setting: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/model-performance-analysis', methods=['GET'])
@admin_required
@function_permission_required('model_performance_analysis')
def get_model_performance_analysis():
    """Get model performance analysis for development research."""
    try:
        # Get date range filters from request
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')

        # Get the analysis data
        analysis = db_utils.get_model_performance_analysis(start_date=start_date, end_date=end_date)

        return jsonify({
            "success": True,
            "analysis": analysis,
            "message": f"Model performance analysis generated with {len(analysis.get('model_comparisons', []))} configurations"
        }), 200

    except Exception as e:
        logger.error(f"Error getting model performance analysis: {str(e)}")
        return jsonify({
            "success": False,
            "error": str(e),
            "analysis": {
                "model_comparisons": [],
                "anti_hallucination_effectiveness": [],
                "category_performance": {},
                "summary": {"error": str(e)}
            }
        }), 500

@app.route('/api/cleanup-malformed-urls', methods=['POST'])
@admin_required
def cleanup_malformed_urls_api():
    """API endpoint to clean up malformed URLs in the database"""
    try:
        # Import the cleanup function
        import db_content_utils as db

        # Run the cleanup
        results = db.clean_malformed_urls()

        logger.info(f"URL cleanup completed via API: {results}")

        return jsonify({
            "success": True,
            "message": "URL cleanup completed successfully",
            "results": results
        }), 200

    except Exception as e:
        logger.error(f"Error during URL cleanup via API: {str(e)}")
        return jsonify({
            "success": False,
            "error": f"Failed to clean up URLs: {str(e)}"
        }), 500

@app.route('/<category>/<filename>')
@limiter.exempt
def serve_file(category, filename):
    """Serve PDF files directly from the app."""
    try:
        # First check the old directory structure
        old_path = os.path.join(TEMP_FOLDER, category, filename)
        if os.path.exists(old_path) and os.path.isfile(old_path):
            # Set the appropriate content type
            if filename.lower().endswith('.pdf'):
                return send_file(old_path, mimetype='application/pdf')
            else:
                return send_file(old_path)
        else:
            # If not found in the old structure, check the new structure
            pdf_base_name = os.path.splitext(filename)[0]
            new_path = os.path.join(TEMP_FOLDER, category, pdf_base_name, filename)
            if os.path.exists(new_path) and os.path.isfile(new_path):
                # Set the appropriate content type
                if filename.lower().endswith('.pdf'):
                    return send_file(new_path, mimetype='application/pdf')
                else:
                    return send_file(new_path)
            else:
                flash(f"File not found: {filename}", "error")
                return redirect(url_for('list_files'))
    except Exception as e:
        logger.error(f"Error serving file {filename}: {str(e)}")
        flash(f"Error serving file: {str(e)}", "error")
        return redirect(url_for('list_files'))

@app.route('/<category>/<pdf_name>/pdf_images/<filename>')
@limiter.exempt
def serve_pdf_image_new(category, pdf_name, filename):
    """Serve extracted images from PDFs using the new directory structure."""
    try:
        # Construct the path to the image using the new directory structure
        file_path = os.path.join(TEMP_FOLDER, category, pdf_name, "pdf_images", filename)

        if os.path.exists(file_path) and os.path.isfile(file_path):
            # Determine the content type based on file extension
            extension = filename.split('.')[-1].lower()
            content_type = {
                'jpg': 'image/jpeg',
                'jpeg': 'image/jpeg',
                'png': 'image/png',
                'gif': 'image/gif',
                'bmp': 'image/bmp',
                'tiff': 'image/tiff',
                'webp': 'image/webp'
            }.get(extension, 'application/octet-stream')

            return send_file(file_path, mimetype=content_type)
        else:
            logger.error(f"Image file not found: {file_path}")
            return "Image not found", 404
    except Exception as e:
        logger.error(f"Error serving image {filename}: {str(e)}")
        return f"Error serving image: {str(e)}", 500

@app.route('/<category>/<pdf_name>/pdf_images/cover_image/<filename>')
@limiter.exempt
def serve_pdf_cover_image(category, pdf_name, filename):
    """Serve cover images/thumbnails from PDFs using the new directory structure."""
    try:
        # Construct the path to the cover image using the new directory structure
        file_path = os.path.join(TEMP_FOLDER, category, pdf_name, "pdf_images", "cover_image", filename)
        logger.debug(f"Looking for cover image at: {file_path}")

        if os.path.exists(file_path) and os.path.isfile(file_path):
            # Determine the content type based on file extension
            extension = filename.split('.')[-1].lower()
            content_type = {
                'jpg': 'image/jpeg',
                'jpeg': 'image/jpeg',
                'png': 'image/png',
                'gif': 'image/gif',
                'bmp': 'image/bmp',
                'tiff': 'image/tiff',
                'webp': 'image/webp'
            }.get(extension, 'application/octet-stream')

            logger.info(f"Serving cover image: {file_path}")
            return send_file(file_path, mimetype=content_type)
        else:
            logger.warning(f"Cover image file not found: {file_path}")

            # Try to find the PDF file and extract the first page on-the-fly
            pdf_file_path = os.path.join(TEMP_FOLDER, category, pdf_name, f"{pdf_name}.pdf")
            logger.debug(f"Looking for PDF file at: {pdf_file_path}")

            if os.path.exists(pdf_file_path) and os.path.isfile(pdf_file_path):
                try:
                    # Create the cover_image directory if it doesn't exist
                    cover_image_dir = os.path.join(TEMP_FOLDER, category, pdf_name, "pdf_images", "cover_image")
                    os.makedirs(cover_image_dir, exist_ok=True)

                    # Import fitz here to avoid circular imports
                    import fitz

                    # Open the PDF and extract the first page
                    doc = fitz.open(pdf_file_path)
                    if doc.page_count > 0:
                        page = doc[0]
                        pix = page.get_pixmap(matrix=fitz.Matrix(2, 2))
                        pix.save(file_path)

                        logger.info(f"Generated cover image on-the-fly: {file_path}")
                        return send_file(file_path, mimetype='image/jpeg')
                    else:
                        logger.warning(f"PDF has no pages: {pdf_file_path}")
                except Exception as extract_error:
                    logger.error(f"Failed to extract cover image on-the-fly: {str(extract_error)}")
            else:
                logger.warning(f"PDF file not found: {pdf_file_path}")

            # If we still can't serve the image, check for any image in the pdf_images directory
            try:
                # Look for any image in the pdf_images directory
                pdf_images_dir = os.path.join(TEMP_FOLDER, category, pdf_name, "pdf_images")
                logger.debug(f"Looking for any image in: {pdf_images_dir}")

                if os.path.exists(pdf_images_dir) and os.path.isdir(pdf_images_dir):
                    # Get the first image file in the directory
                    image_files = [f for f in os.listdir(pdf_images_dir)
                                  if f.lower().endswith(('.jpg', '.jpeg', '.png', '.gif'))
                                  and os.path.isfile(os.path.join(pdf_images_dir, f))]

                    if image_files:
                        # Use the first image as a fallback
                        fallback_image = os.path.join(pdf_images_dir, image_files[0])
                        logger.info(f"Using fallback image from pdf_images directory: {fallback_image}")
                        return send_file(fallback_image, mimetype='image/jpeg')
                    else:
                        logger.warning(f"No image files found in {pdf_images_dir}")
                else:
                    logger.warning(f"PDF images directory not found: {pdf_images_dir}")

                # Check for a category-level placeholder in the _temp directory
                category_placeholder = os.path.join(TEMP_FOLDER, category, "placeholder.jpg")
                logger.debug(f"Looking for category placeholder at: {category_placeholder}")

                if os.path.exists(category_placeholder) and os.path.isfile(category_placeholder):
                    logger.info(f"Using category placeholder: {category_placeholder}")
                    return send_file(category_placeholder, mimetype='image/jpeg')

                # If no placeholder is found, create a simple text-based placeholder
                from PIL import Image, ImageDraw, ImageFont

                # Create a blank image
                img = Image.new('RGB', (400, 300), color=(240, 240, 240))
                d = ImageDraw.Draw(img)

                # Try to use a system font
                try:
                    font = ImageFont.truetype("arial.ttf", 20)
                except IOError:
                    try:
                        # Try another common font on different systems
                        font = ImageFont.truetype("DejaVuSans.ttf", 20)
                    except IOError:
                        font = ImageFont.load_default()

                # Add text to the image
                text = f"No thumbnail for {pdf_name}"
                text_width = d.textlength(text, font=font)
                d.text(((400-text_width)/2, 140), text, fill=(0, 0, 0), font=font)

                # Save the image to the cover_image directory
                os.makedirs(os.path.dirname(file_path), exist_ok=True)
                img.save(file_path)

                logger.info(f"Created dynamic placeholder image: {file_path}")
                return send_file(file_path, mimetype='image/jpeg')

            except Exception as placeholder_error:
                logger.error(f"Error creating placeholder image: {str(placeholder_error)}")

            # If all else fails, return a 404
            return "Cover image not found", 404
    except Exception as e:
        logger.error(f"Error serving cover image {filename}: {str(e)}")
        return f"Error serving cover image: {str(e)}", 500

@app.route('/<category>/<pdf_name>/pdf_tables/<filename>')
@limiter.exempt
def serve_pdf_table_new(category, pdf_name, filename):
    """Serve extracted tables from PDFs using the new directory structure."""
    try:
        # Construct the path to the table using the new directory structure
        file_path = os.path.join(TEMP_FOLDER, category, pdf_name, "pdf_tables", filename)

        if os.path.exists(file_path) and os.path.isfile(file_path):
            # For HTML tables, set the content type to text/html
            if filename.lower().endswith('.html'):
                return send_file(file_path, mimetype='text/html')
            else:
                return send_file(file_path)
        else:
            logger.error(f"Table file not found: {file_path}")
            return "Table not found", 404
    except Exception as e:
        logger.error(f"Error serving table {filename}: {str(e)}")
        return f"Error serving table: {str(e)}", 500

# Keep the old routes for backward compatibility
@app.route('/pdf_images/<category>/<filename>')
@limiter.exempt
def serve_pdf_image(category, filename):
    """Serve extracted images from PDFs using the old directory structure."""
    try:
        # First check if the file exists in the old structure
        old_path = os.path.join(TEMP_FOLDER, "pdf_images", category, filename)

        if os.path.exists(old_path) and os.path.isfile(old_path):
            # Determine the content type based on file extension
            extension = filename.split('.')[-1].lower()
            content_type = {
                'jpg': 'image/jpeg',
                'jpeg': 'image/jpeg',
                'png': 'image/png',
                'gif': 'image/gif',
                'bmp': 'image/bmp',
                'tiff': 'image/tiff',
                'webp': 'image/webp'
            }.get(extension, 'application/octet-stream')

            return send_file(old_path, mimetype=content_type)
        else:
            # If not found in old structure, try to find it in the new structure
            # Extract the PDF name from the filename (assuming format: pdfname_page_index.ext)
            parts = filename.split('_')
            if len(parts) >= 2:
                pdf_name = parts[0]
                # Try to find the file in the new structure
                new_path = os.path.join(TEMP_FOLDER, category, pdf_name, "pdf_images", filename)
                if os.path.exists(new_path) and os.path.isfile(new_path):
                    extension = filename.split('.')[-1].lower()
                    content_type = {
                        'jpg': 'image/jpeg',
                        'jpeg': 'image/jpeg',
                        'png': 'image/png',
                        'gif': 'image/gif',
                        'bmp': 'image/bmp',
                        'tiff': 'image/tiff',
                        'webp': 'image/webp'
                    }.get(extension, 'application/octet-stream')
                    return send_file(new_path, mimetype=content_type)

            logger.error(f"Image file not found in either structure: {filename}")
            return "Image not found", 404
    except Exception as e:
        logger.error(f"Error serving image {filename}: {str(e)}")
        return f"Error serving image: {str(e)}", 500

@app.route('/pdf_tables/<category>/<filename>')
@limiter.exempt
def serve_pdf_table(category, filename):
    """Serve extracted tables from PDFs using the old directory structure."""
    try:
        # First check if the file exists in the old structure
        old_path = os.path.join(TEMP_FOLDER, "pdf_tables", category, filename)

        if os.path.exists(old_path) and os.path.isfile(old_path):
            # For HTML tables, set the content type to text/html
            if filename.lower().endswith('.html'):
                return send_file(old_path, mimetype='text/html')
            else:
                return send_file(old_path)
        else:
            # If not found in old structure, try to find it in the new structure
            # Extract the PDF name from the filename (assuming format: pdfname_type_index.html)
            parts = filename.split('_')
            if len(parts) >= 2:
                pdf_name = parts[0]
                # Try to find the file in the new structure
                new_path = os.path.join(TEMP_FOLDER, category, pdf_name, "pdf_tables", filename)
                if os.path.exists(new_path) and os.path.isfile(new_path):
                    if filename.lower().endswith('.html'):
                        return send_file(new_path, mimetype='text/html')
                    else:
                        return send_file(new_path)

            logger.error(f"Table file not found in either structure: {filename}")
            return "Table not found", 404
    except Exception as e:
        logger.error(f"Error serving table {filename}: {str(e)}")
        return f"Error serving table: {str(e)}", 500

# Register GeoIP analytics API routes
geoip_analytics.register_geoip_routes(app)

# Location Map Route
@app.route('/location_map')
@um.function_permission_required('ai_analytics')
def location_map():
    """Display the location map interface."""
    try:
        # Get all extracted locations
        from db_utils import get_all_extracted_locations, get_location_statistics
        locations = get_all_extracted_locations(include_sources=True)
        statistics = get_location_statistics()

        # Get available categories for filtering
        categories = list_categories()

        return render_template('location_map.html',
                             locations=locations,
                             statistics=statistics,
                             categories=categories)
    except Exception as e:
        logger.error(f"Error loading location map: {str(e)}")
        flash('Error loading location map. Please try again.', 'error')
        return redirect(url_for('admin_dashboard'))

# Location API Routes
@app.route('/api/locations')
@um.function_permission_required('ai_analytics')
def api_locations():
    """API endpoint to get location data."""
    try:
        category = request.args.get('category')

        if category:
            from db_utils import get_locations_by_category
            locations = get_locations_by_category(category)
        else:
            from db_utils import get_all_extracted_locations
            locations = get_all_extracted_locations(include_sources=True)

        return jsonify({
            'success': True,
            'locations': locations,
            'count': len(locations)
        })
    except Exception as e:
        logger.error(f"Error retrieving locations via API: {str(e)}")
        return jsonify({'error': 'Failed to retrieve locations'}), 500

@app.route('/api/location_statistics')
@um.function_permission_required('ai_analytics')
def api_location_statistics():
    """API endpoint to get location statistics."""
    try:
        from db_utils import get_location_statistics
        statistics = get_location_statistics()
        return jsonify({
            'success': True,
            'statistics': statistics
        })
    except Exception as e:
        logger.error(f"Error retrieving location statistics: {str(e)}")
        return jsonify({'error': 'Failed to retrieve statistics'}), 500

@app.route('/api/locations/<int:location_id>', methods=['DELETE'])
@um.function_permission_required('ai_analytics')
def api_delete_location(location_id):
    """API endpoint to delete a single location."""
    try:
        from db_utils import delete_location_by_id

        success = delete_location_by_id(location_id)

        if success:
            return jsonify({
                'success': True,
                'message': 'Location deleted successfully'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Failed to delete location'
            }), 500

    except Exception as e:
        logger.error(f"Error deleting location {location_id}: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Error deleting location: {str(e)}'
        }), 500

@app.route('/api/locations/bulk-delete', methods=['POST'])
@um.function_permission_required('ai_analytics')
def api_bulk_delete_locations():
    """API endpoint to delete multiple locations."""
    try:
        from db_utils import delete_location_by_id

        data = request.get_json()
        location_ids = data.get('location_ids', [])

        if not location_ids:
            return jsonify({
                'success': False,
                'message': 'No location IDs provided'
            }), 400

        deleted_count = 0
        failed_count = 0

        for location_id in location_ids:
            try:
                if delete_location_by_id(int(location_id)):
                    deleted_count += 1
                else:
                    failed_count += 1
            except Exception as e:
                logger.error(f"Error deleting location {location_id}: {str(e)}")
                failed_count += 1

        return jsonify({
            'success': True,
            'deleted_count': deleted_count,
            'failed_count': failed_count,
            'message': f'Deleted {deleted_count} location(s)' + (f', {failed_count} failed' if failed_count > 0 else '')
        })

    except Exception as e:
        logger.error(f"Error in bulk delete: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Error deleting locations: {str(e)}'
        }), 500

@app.route('/api/extract-locations', methods=['POST'])
def api_extract_locations():
    """API endpoint to extract locations from text."""
    try:
        data = request.get_json()
        text = data.get('text', '')
        sources = data.get('sources', [])
        filter_philippine = data.get('filter_philippine', True)
        admin_levels_only = data.get('admin_levels_only', True)

        if not text:
            return jsonify({
                'success': False,
                'message': 'No text provided'
            }), 400

        # Import location extractor
        from location_extractor import LocationExtractor

        # Initialize extractor
        extractor = LocationExtractor()

        # Extract locations from text
        locations = extractor.extract_locations_from_text(text)

        # Filter for Philippine administrative divisions if requested
        if filter_philippine and admin_levels_only:
            locations = [
                loc for loc in locations
                if loc.get('location_type') in ['municipality', 'city', 'barangay'] or
                   (loc.get('administrative_level') in ['municipality', 'city', 'barangay'])
            ]

        # Geocode locations that don't have coordinates
        geocoded_locations = []
        for location in locations:
            if not location.get('latitude') or not location.get('longitude'):
                geocoding_result = extractor.geocode_location(location['location_text'])
                if geocoding_result and geocoding_result.get('status') == 'success':
                    location.update({
                        'latitude': geocoding_result['latitude'],
                        'longitude': geocoding_result['longitude'],
                        'geocoded_address': geocoding_result['formatted_address'],
                        'country': geocoding_result['country'],
                        'region': geocoding_result['region'],
                        'city': geocoding_result['city'],
                        'municipality': geocoding_result.get('municipality'),
                        'barangay': geocoding_result.get('barangay')
                    })

            # Only include locations with valid coordinates
            if location.get('latitude') and location.get('longitude'):
                geocoded_locations.append(location)

        return jsonify({
            'success': True,
            'locations': geocoded_locations,
            'count': len(geocoded_locations)
        })

    except Exception as e:
        logger.error(f"Error extracting locations from text: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Error extracting locations: {str(e)}'
        }), 500

# Middleware to track user locations
@app.before_request
def track_user_location():
    """Track user location for analytics."""
    # Skip for static files and API endpoints
    if request.path.startswith('/static/') or request.path.startswith('/api/'):
        return

    try:
        # Get device fingerprint from request
        device_fingerprint = request.cookies.get('device_fingerprint')
        if not device_fingerprint:
            return

        # Get client name from session or cookie
        client_name = request.cookies.get('client_name')

        # Get session ID
        session_id = request.cookies.get('session_id')

        # Get geolocation data
        ip_address, city, region, country, latitude, longitude = geo_utils.get_location_for_analytics()

        # Save geolocation data
        geoip_analytics.save_geoip_data(
            ip_address=ip_address,
            device_fingerprint=device_fingerprint,
            client_name=client_name,
            city=city,
            region=region,
            country=country,
            latitude=latitude,
            longitude=longitude,
            user_agent=request.user_agent.string,
            page_url=request.path,
            session_id=session_id
        )
    except Exception as e:
        logger.error(f"Error tracking user location: {str(e)}")

if __name__ == '__main__':
    # Initialize the database
    db_utils.init_db()

    # Initialize GeoIP analytics database
    geoip_analytics.ensure_geoip_table()

    # Initialize the content database for database-first retrieval
    from db_schema import initialize_database, migrate_location_schema
    if not initialize_database():
        logger.error("Failed to initialize content database. Application may not function correctly.")
    else:
        logger.info("Content database initialized successfully.")

        # Run location schema migration
        if migrate_location_schema():
            logger.info("Location schema migration completed successfully.")
        else:
            logger.warning("Location schema migration failed, but application will continue.")

    # Initialize user management database
    if not um.init_user_db():
        logger.error("Failed to initialize user management database. User functionality may not work correctly.")
    else:
        logger.info("User management database initialized successfully.")

        # Initialize default permission groups and sync permissions
        from permissions import ensure_default_permission_groups, sync_new_module_permissions

        if ensure_default_permission_groups():
            logger.info("Default permission groups initialized successfully.")
        else:
            logger.error("Failed to initialize default permission groups.")

        if sync_new_module_permissions():
            logger.info("Module permissions synchronized successfully.")
        else:
            logger.error("Failed to synchronize module permissions.")

    # Create temporary directories for PDF images and tables
    create_temp_directories()

    # Start the Flask application
    app.run(host='0.0.0.0', port=8080, debug=True)