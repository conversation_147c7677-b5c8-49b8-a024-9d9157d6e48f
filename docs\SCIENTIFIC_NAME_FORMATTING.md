# Scientific Name Formatting Feature

## Overview

The ERDB Knowledge Products system now automatically detects and formats scientific names (binomial nomenclature) using LLM-based natural language understanding. This server-side approach leverages the AI model's contextual awareness to accurately identify and format scientific names in responses, follow-up questions, and all AI-generated content. The system applies proper italicization to genus and species names while preserving author citations in regular text.

## Features

### Automatic Detection
The system recognizes various scientific name formats:

1. **Basic Binomial**: *Homo sapiens*, *Pterocarpus indicus*
2. **Trinomial with Subspecies**: *Homo sapiens sapiens*, *Pinus kesiya var. langbianensis*
3. **With Author Citations**: *Escherichia coli* (Migula 1895), *Swietenia macrophylla* King
4. **Abbreviated Forms**: *E. coli*, *Pinus sp.*

### Formatting Rules
- **Genus and species names** are italicized using `<em>` tags
- **Author citations and years** remain in regular text
- **Proper capitalization** is maintained (genus capitalized, species lowercase)
- **Subspecies and varieties** are included in italicization when present

### Philippine Species Focus
The system includes an extensive database of Philippine endemic and economically important species commonly referenced in ERDB research:

- **Forest Species**: *Pterocarpus indicus*, *Shorea contorta*, *Dipterocarpus grandiflorus*
- **Timber Species**: *Swietenia macrophylla*, *Gmelina arborea*, *Eucalyptus camaldulensis*
- **Bamboo Species**: *Bambusa blumeana*, *Dendrocalamus asper*, *Gigantochloa levis*
- **Mangrove Species**: *Rhizophora apiculata*, *Avicennia marina*, *Sonneratia alba*
- **Pine Species**: *Pinus kesiya*, *Pinus merkusii*

## Implementation Scope

### Client-Side Processing
- **Main Chat Interface**: AI responses are formatted in real-time
- **Follow-up Questions**: Scientific names in generated questions are automatically formatted
- **Chat History**: Restored conversations maintain scientific name formatting
- **Theme Compatibility**: Formatting works in both light and dark themes

### Templates Affected
- `templates/index.html` - Main chat interface
- `templates/chat_history.html` - Chat history display
- Any future templates displaying AI-generated content

## Technical Implementation

### Core Components

1. **LLM Prompt Templates** (`query.py` and `default_models.json`)
   - Integrated scientific name formatting instructions in all prompt templates
   - Context-aware natural language understanding
   - Consistent formatting across all anti-hallucination modes

2. **System Prompts**
   - Enhanced with scientific naming guidelines
   - Philippine species focus for ERDB research context
   - Proper markdown italics formatting instructions

3. **Server-Side Processing**
   - All formatting handled by the LLM during response generation
   - Consistent across all response channels
   - No client-side processing required

### LLM Instructions

The system includes comprehensive instructions in all prompt templates:

```
SCIENTIFIC NAME FORMATTING:
- AUTOMATICALLY identify and format all scientific names using markdown italics
- Scientific names include genus and species (e.g., *Pterocarpus indicus*, *Homo sapiens*)
- Include subspecies in italics when present (e.g., *Homo sapiens sapiens*)
- Keep author citations and years in regular text (e.g., *Escherichia coli* (Migula 1895))
- Common Philippine species to watch for: *Pterocarpus indicus*, *Shorea contorta*, etc.
- Format abbreviated scientific names (e.g., *E. coli*, *P. indicus*, *Pinus sp.*)
- Only italicize the genus and species parts, not common names or author citations
```

### Advantages of LLM-Based Approach

The LLM-based system provides superior accuracy by:
- Understanding scientific context and terminology
- Avoiding false positives from regex pattern matching
- Handling complex nomenclature variations naturally
- Maintaining consistency across different response types
- Adapting to new species names without manual updates

## Usage Examples

### Input Text
```
The study examined Homo sapiens and Escherichia coli in various environments.
We also looked at Pterocarpus indicus, which is native to the Philippines.
The species Swietenia macrophylla King is an important timber species.
```

### Formatted Output
```html
The study examined <em>Homo sapiens</em> and <em>Escherichia coli</em> in various environments.
We also looked at <em>Pterocarpus indicus</em>, which is native to the Philippines.
The species <em>Swietenia macrophylla</em> King is an important timber species.
```

### Visual Result
The study examined *Homo sapiens* and *Escherichia coli* in various environments.
We also looked at *Pterocarpus indicus*, which is native to the Philippines.
The species *Swietenia macrophylla* King is an important timber species.

## Configuration

### Adding New Species
To add new scientific names to the LLM's recognition, update the prompt templates in `query.py` and `default_models.json`:

```python
# In the SCIENTIFIC NAME FORMATTING section of prompt templates
"Common Philippine species to watch for: *Pterocarpus indicus*, *Shorea contorta*, *New species name*, *Another species*"
```

### Customizing Instructions
The scientific name formatting instructions can be modified in:
- `query.py` - Dynamic prompt templates (lines 553-576, 608-633, 652-675)
- `default_models.json` - Default prompt templates and system prompts

### Model Compatibility
The scientific name formatting works with all supported LLM models:
- llama3.1:8b-instruct-q4_K_M (recommended)
- llama3.2:3b-instruct-q4_K_M
- gemma3:4b-it-q4_K_M
- Any other Ollama-compatible models

## Performance Considerations

- **Server-side processing** ensures consistent formatting across all channels
- **LLM natural language understanding** provides superior accuracy
- **No client-side overhead** - all processing handled by the AI model
- **Consistent across all response types** - main responses, follow-up questions, etc.

## Future Enhancements

Potential improvements include:
- Integration with taxonomic databases for validation
- Support for additional nomenclature systems (e.g., chemical compounds)
- User preferences for formatting styles
- Enhanced context awareness for domain-specific terminology
- Multi-language scientific name support

## Testing

### Automated Testing
A comprehensive test script is available at `/test_llm_scientific_names.py` for verifying the LLM-based formatting functionality:

```bash
python test_llm_scientific_names.py
```

The test script validates:
- Scientific name detection and formatting across different categories
- Proper italicization of genus and species names
- Preservation of author citations in regular text
- Consistency across all anti-hallucination modes
- Follow-up question formatting

### Manual Testing
Test the functionality by asking questions containing scientific names:
- "What information do you have about *Pterocarpus indicus*?"
- "Tell me about bamboo species like *Bambusa blumeana*"
- "Research on *Escherichia coli* (Migula 1895)"

### Verification Points
- ✅ Scientific names appear in italics (*genus species*)
- ✅ Author citations remain in regular text
- ✅ Subspecies are included in italics
- ✅ Abbreviated forms are properly formatted
- ✅ Follow-up questions maintain formatting
- ✅ Consistent across all anti-hallucination modes
