import os
import logging
import shutil
import hashlib
import time
import random
from get_vector_db import get_vector_db  # Assuming this is needed for delete_file
from werkzeug.utils import secure_filename

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

TEMP_FOLDER = os.getenv("TEMP_FOLDER", "./_temp")
CHROMA_PATH = os.getenv("CHROMA_PATH", "./chroma")

def safe_remove_file(file_path, max_retries=5, initial_delay=0.1):
    """
    Safely remove a file with retry logic for handling locked files.

    Args:
        file_path: Path to the file to remove
        max_retries: Maximum number of retry attempts
        initial_delay: Initial delay between retries in seconds

    Returns:
        bool: True if successful, False otherwise
    """
    if not os.path.exists(file_path):
        return True

    retry_count = 0
    retry_delay = initial_delay

    while retry_count <= max_retries:
        try:
            os.remove(file_path)
            logger.info(f"Successfully removed file: {file_path}")
            return True
        except PermissionError as e:
            # File is locked by another process
            retry_count += 1
            if retry_count <= max_retries:
                logger.warning(f"File {file_path} is locked. Retrying in {retry_delay:.2f}s ({retry_count}/{max_retries})")
                time.sleep(retry_delay)
                # Exponential backoff with jitter
                retry_delay = retry_delay * 2 * (0.5 + random.random())
            else:
                logger.error(f"Failed to remove file {file_path} after {max_retries} attempts: {str(e)}")
                return False
        except Exception as e:
            logger.error(f"Error removing file {file_path}: {str(e)}")
            return False

    return False

def safe_remove_directory(dir_path, max_retries=5, initial_delay=0.1):
    """
    Safely remove a directory with retry logic for handling locked files.

    Args:
        dir_path: Path to the directory to remove
        max_retries: Maximum number of retry attempts
        initial_delay: Initial delay between retries in seconds

    Returns:
        bool: True if successful, False otherwise
    """
    if not os.path.exists(dir_path) or not os.path.isdir(dir_path):
        return True

    retry_count = 0
    retry_delay = initial_delay

    while retry_count <= max_retries:
        try:
            shutil.rmtree(dir_path)
            logger.info(f"Successfully removed directory: {dir_path}")
            return True
        except (PermissionError, OSError) as e:
            # Directory or a file within it is locked by another process
            retry_count += 1
            if retry_count <= max_retries:
                logger.warning(f"Directory {dir_path} has locked files. Retrying in {retry_delay:.2f}s ({retry_count}/{max_retries})")
                time.sleep(retry_delay)
                # Exponential backoff with jitter
                retry_delay = retry_delay * 2 * (0.5 + random.random())
            else:
                logger.error(f"Failed to remove directory {dir_path} after {max_retries} attempts: {str(e)}")
                return False
        except Exception as e:
            logger.error(f"Error removing directory {dir_path}: {str(e)}")
            return False

    return False

def allowed_file(filename: str) -> bool:
    return filename.lower().endswith(".pdf")

def delete_file(category: str, filename: str):
    """
    Delete a file and all its associated resources (images, tables, vector embeddings).
    This function handles both the hierarchical directory structure and any legacy paths.

    Args:
        category: The category the file belongs to
        filename: The filename to delete

    Returns:
        Tuple of (success, message)
    """
    try:
        resources_deleted = []
        pdf_name_base = os.path.splitext(filename)[0]  # Remove extension

        # Check for the new directory structure first
        pdf_dir = os.path.join(TEMP_FOLDER, category, pdf_name_base)

        # If the PDF directory exists in the new structure, delete the entire directory
        if os.path.exists(pdf_dir) and os.path.isdir(pdf_dir):
            # Delete the entire PDF directory (including the PDF file, images, tables, and thumbnails)
            if safe_remove_directory(pdf_dir):
                resources_deleted.append("PDF directory with all resources (including thumbnails)")
                logger.info(f"Deleted PDF directory with all resources: {pdf_dir}")
            else:
                # If directory removal fails, try to delete individual files
                logger.warning(f"Failed to remove entire directory {pdf_dir}, attempting to delete individual files")

                # Try to delete the PDF file first
                pdf_file_path = os.path.join(pdf_dir, filename)
                if os.path.exists(pdf_file_path) and safe_remove_file(pdf_file_path):
                    resources_deleted.append("PDF file")

                # Try to delete images directory
                images_dir = os.path.join(pdf_dir, "pdf_images")
                if os.path.exists(images_dir) and safe_remove_directory(images_dir):
                    resources_deleted.append("PDF images")

                # Try to delete tables directory
                tables_dir = os.path.join(pdf_dir, "pdf_tables")
                if os.path.exists(tables_dir) and safe_remove_directory(tables_dir):
                    resources_deleted.append("PDF tables")
        else:
            # Fall back to the old structure if the new one doesn't exist
            # Delete the PDF file
            file_path = os.path.join(TEMP_FOLDER, category, filename)
            if os.path.exists(file_path):
                if safe_remove_file(file_path):
                    resources_deleted.append("file")
                    logger.info(f"Deleted file from filesystem: {file_path}")
                else:
                    logger.warning(f"Failed to delete file: {file_path}")
            else:
                logger.warning(f"File not found on filesystem: {file_path}")

            # Check for old structure images and tables
            old_images_dir = os.path.join(TEMP_FOLDER, "pdf_images", category)
            if os.path.exists(old_images_dir):
                images_deleted = False
                for img_file in os.listdir(old_images_dir):
                    if img_file.startswith(pdf_name_base):
                        img_path = os.path.join(old_images_dir, img_file)
                        os.remove(img_path)
                        images_deleted = True
                        logger.info(f"Deleted associated image: {img_path}")

                if images_deleted:
                    resources_deleted.append("extracted images (old structure)")

            old_tables_dir = os.path.join(TEMP_FOLDER, "pdf_tables", category)
            if os.path.exists(old_tables_dir):
                tables_deleted = False
                for table_file in os.listdir(old_tables_dir):
                    if table_file.startswith(pdf_name_base):
                        table_path = os.path.join(old_tables_dir, table_file)
                        os.remove(table_path)
                        tables_deleted = True
                        logger.info(f"Deleted associated table: {table_path}")

                if tables_deleted:
                    resources_deleted.append("extracted tables (old structure)")

        # Check for any images in the temp_images directory (used as fallback in pdf_processor.py)
        temp_images_dir = os.path.join(TEMP_FOLDER, "temp_images")
        if os.path.exists(temp_images_dir) and os.path.isdir(temp_images_dir):
            temp_images_deleted = False
            for img_file in os.listdir(temp_images_dir):
                if img_file.startswith(pdf_name_base):
                    img_path = os.path.join(temp_images_dir, img_file)
                    os.remove(img_path)
                    temp_images_deleted = True
                    logger.info(f"Deleted temporary image: {img_path}")

            if temp_images_deleted:
                resources_deleted.append("temporary images")

        # Check for any tables in the temp_tables directory (used as fallback in pdf_processor.py)
        temp_tables_dir = os.path.join(TEMP_FOLDER, "temp_tables")
        if os.path.exists(temp_tables_dir) and os.path.isdir(temp_tables_dir):
            temp_tables_deleted = False
            for table_file in os.listdir(temp_tables_dir):
                if table_file.startswith(pdf_name_base):
                    table_path = os.path.join(temp_tables_dir, table_file)
                    os.remove(table_path)
                    temp_tables_deleted = True
                    logger.info(f"Deleted temporary table: {table_path}")

            if temp_tables_deleted:
                resources_deleted.append("temporary tables")

        # Delete from vector database (regardless of directory structure)
        db = get_vector_db(category)
        db.delete(where={"source": filename})
        resources_deleted.append("vector data")
        logger.info(f"Deleted entries from vector DB for source: {filename}")

        # Delete associated location data
        try:
            from db_utils import delete_pdf_locations
            if delete_pdf_locations(filename, category):
                resources_deleted.append("location data")
                logger.info(f"Deleted location data for PDF: {filename}")
        except Exception as e:
            logger.warning(f"Failed to delete location data for {filename}: {str(e)}")

        # Prepare success message
        if resources_deleted:
            return True, f"File {filename} deleted successfully with all {', '.join(resources_deleted)}"
        else:
            return True, f"File {filename} deleted successfully"
    except Exception as e:
        logger.error(f"Failed to delete file {filename} in category {category}: {str(e)}")
        return False, f"Failed to delete file: {str(e)}"

def list_categories():
    """Return a list of category names from CHROMA_PATH."""
    if os.path.exists(CHROMA_PATH):
        return [d for d in os.listdir(CHROMA_PATH) if os.path.isdir(os.path.join(CHROMA_PATH, d))]
    else:
        return []

def calculate_file_hash(file_obj):
    """
    Calculate SHA-256 hash of a file object.

    Args:
        file_obj: A file-like object (must support seek, read)

    Returns:
        str: Hexadecimal digest of the file hash
    """
    try:
        # Save the current position
        current_position = file_obj.tell()

        # Reset to beginning of file
        file_obj.seek(0)

        # Calculate hash
        file_hash = hashlib.sha256()
        chunk_size = 65536  # 64KB chunks

        while True:
            data = file_obj.read(chunk_size)
            if not data:
                break
            file_hash.update(data)

        # Restore the original position
        file_obj.seek(current_position)

        return file_hash.hexdigest()
    except Exception as e:
        logger.error(f"Error calculating file hash: {str(e)}")
        return None

def check_duplicate_pdf(file_obj, category):
    """
    Check if a PDF file already exists in the system based on filename or content hash.

    Args:
        file_obj: The uploaded file object
        category: The category to check in

    Returns:
        tuple: (is_duplicate, duplicate_info)
            - is_duplicate (bool): True if a duplicate is found
            - duplicate_info (dict): Information about the duplicate if found, None otherwise
    """
    try:
        # Get the original filename
        original_filename = secure_filename(file_obj.filename)

        # Calculate file hash
        file_hash = calculate_file_hash(file_obj)
        if not file_hash:
            logger.warning(f"Could not calculate hash for {original_filename}")
            return False, None

        # Check for duplicates in the category directory
        category_path = os.path.join(TEMP_FOLDER, category)
        if not os.path.exists(category_path):
            return False, None

        # First check for filename duplicates in the new directory structure
        for item in os.listdir(category_path):
            item_path = os.path.join(category_path, item)

            # Check if it's a directory (new structure)
            if os.path.isdir(item_path):
                # Check all PDF files in this directory
                pdf_dir_files = [f for f in os.listdir(item_path) if f.lower().endswith('.pdf')]

                for pdf_file in pdf_dir_files:
                    # Extract the original filename (remove timestamp prefix if present)
                    stored_filename = pdf_file.split('_', 1)[1] if '_' in pdf_file else pdf_file

                    # Check if filenames match (case insensitive)
                    if stored_filename.lower() == original_filename.lower():
                        return True, {
                            'type': 'filename_match',
                            'category': category,
                            'filename': pdf_file,
                            'path': os.path.join(item, pdf_file)
                        }

                    # Check content hash
                    pdf_path = os.path.join(item_path, pdf_file)
                    with open(pdf_path, 'rb') as stored_file:
                        stored_hash = calculate_file_hash(stored_file)
                        if stored_hash and stored_hash == file_hash:
                            return True, {
                                'type': 'content_match',
                                'category': category,
                                'filename': pdf_file,
                                'path': os.path.join(item, pdf_file)
                            }

            # Check if it's a PDF file (old structure)
            elif item.lower().endswith('.pdf'):
                # Extract the original filename (remove timestamp prefix if present)
                stored_filename = item.split('_', 1)[1] if '_' in item else item

                # Check if filenames match (case insensitive)
                if stored_filename.lower() == original_filename.lower():
                    return True, {
                        'type': 'filename_match',
                        'category': category,
                        'filename': item,
                        'path': item
                    }

                # Check content hash
                pdf_path = os.path.join(category_path, item)
                with open(pdf_path, 'rb') as stored_file:
                    stored_hash = calculate_file_hash(stored_file)
                    if stored_hash and stored_hash == file_hash:
                        return True, {
                            'type': 'content_match',
                            'category': category,
                            'filename': item,
                            'path': item
                        }

        # No duplicates found
        return False, None

    except Exception as e:
        logger.error(f"Error checking for duplicate PDF: {str(e)}")
        return False, None