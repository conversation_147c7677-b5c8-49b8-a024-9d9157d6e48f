<!-- Text Chunking Section -->
<div class="config-section">
    <h2 class="config-section-header">Text Chunking Parameters</h2>
    <p class="config-section-description">Configure how documents are split into chunks for embedding.</p>

    <style>
        /* Text contrast fixes for embedding config partial */
        .config-section-header { color: #1a202c !important; }
        .config-section-description { color: #4a5568 !important; }

        /* Form element text colors */
        textarea, input, select { color: #1a202c !important; }

        /* Label text colors */
        label.text-sm.font-medium.text-gray-700,
        .text-gray-700 { color: #2d3748 !important; }

        /* Helper text colors */
        .text-xs.text-gray-500 { color: #6b7280 !important; }

        /* Checkbox label text */
        .font-medium.text-gray-700 { color: #2d3748 !important; }

        /* Dependency indicator */
        .dependency-indicator { color: #2563eb !important; }
        .text-blue-600 { color: #2563eb !important; }

        /* Select options */
        option { color: #1a202c !important; }
    </style>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
            <label for="chunk_size" class="block text-sm font-medium text-gray-700 mb-1">Chunk Size (characters)</label>
            <div class="flex items-center">
                <input type="number" id="chunk_size" name="chunk_size" min="100" max="2000" step="50" value="{{ embedding_params.chunk_size }}"
                    class="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:shadow-outline">
            </div>
            <p class="mt-1 text-xs text-gray-500">Larger chunks provide more context but may reduce retrieval precision.</p>
            <div class="dependency-indicator">
                <span class="text-blue-600">↑</span> Affects embedding quality and retrieval precision
            </div>
        </div>

        <div>
            <label for="chunk_overlap" class="block text-sm font-medium text-gray-700 mb-1">Chunk Overlap (characters)</label>
            <div class="flex items-center">
                <input type="number" id="chunk_overlap" name="chunk_overlap" min="0" max="500" step="10" value="{{ embedding_params.chunk_overlap }}"
                    class="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:shadow-outline">
            </div>
            <p class="mt-1 text-xs text-gray-500">Overlap between chunks helps maintain context across chunk boundaries.</p>
            <div class="dependency-indicator">
                <span class="text-blue-600">↑</span> Must be smaller than chunk size
            </div>
        </div>
    </div>
</div>

<!-- Document Processing Section -->
<div class="config-section">
    <h2 class="config-section-header">Document Processing</h2>
    <p class="config-section-description">Configure how different document elements are handled during embedding.</p>

    <div class="space-y-4">
        <div class="flex items-start">
            <div class="flex items-center h-5">
                <input id="extract_tables" name="extract_tables" type="checkbox"
                    class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    {% if embedding_params.extract_tables %}checked{% endif %}>
            </div>
            <div class="ml-3 text-sm">
                <label for="extract_tables" class="font-medium text-gray-700">Extract Tables</label>
                <p class="text-gray-500">Extract and process tables from PDF documents as separate chunks.</p>
            </div>
        </div>

        <div class="flex items-start">
            <div class="flex items-center h-5">
                <input id="extract_images" name="extract_images" type="checkbox"
                    class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    {% if embedding_params.extract_images %}checked{% endif %}>
            </div>
            <div class="ml-3 text-sm">
                <label for="extract_images" class="font-medium text-gray-700">Extract Images</label>
                <p class="text-gray-500">Extract and save images from PDF documents.</p>
            </div>
        </div>

        <!-- Vision Model Settings for Document Processing -->
        <div class="bg-blue-50 p-4 rounded-lg border border-blue-200 mb-4">
            <h3 class="text-lg font-medium text-blue-800 mb-2">Vision Model Settings</h3>
            <p class="text-sm text-blue-600 mb-4">Configure how the vision model analyzes images during document embedding. Both Llama 3.2 Vision and Gemma 3 multimodal models (4B-IT, 12B-IT) are supported.</p>

            <div class="flex items-start">
                <div class="flex items-center h-5">
                    <input id="use_vision_model" name="use_vision_during_embedding" type="checkbox"
                        class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                        {% if embedding_params.use_vision_model %}checked{% endif %}>
                </div>
                <div class="ml-3 text-sm">
                    <label for="use_vision_model" class="font-medium text-gray-700">Enable Vision Model During Embedding</label>
                    <p class="text-gray-500">Use vision model to analyze and describe images during document embedding.</p>
                    <div class="dependency-indicator">
                        <span class="text-blue-600">↑</span> Requires a vision model to be selected in AI Models tab
                    </div>
                </div>
            </div>

            <div class="ml-7 {% if not embedding_params.use_vision_model %}opacity-50{% endif %}" id="visionModelOptions">
                <div class="mb-3 mt-4">
                    <label for="filter_sensitivity" class="block text-sm font-medium text-gray-700 mb-1">Image Filter Sensitivity</label>
                    <select id="filter_sensitivity" name="filter_sensitivity"
                        class="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:shadow-outline"
                        {% if not embedding_params.use_vision_model %}disabled{% endif %}>
                        <option value="low" {% if embedding_params.filter_sensitivity == 'low' %}selected{% endif %}>Low - Keep most images</option>
                        <option value="medium" {% if embedding_params.filter_sensitivity == 'medium' %}selected{% endif %}>Medium - Balanced filtering</option>
                        <option value="high" {% if embedding_params.filter_sensitivity == 'high' %}selected{% endif %}>High - Only keep very relevant images</option>
                    </select>
                </div>

                <div class="mb-3">
                    <label for="max_images" class="block text-sm font-medium text-gray-700 mb-1">Maximum Images to Process</label>
                    <input type="number" id="max_images" name="max_pdf_images" min="1" max="100"
                        value="{{ embedding_params.max_images }}"
                        class="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:shadow-outline"
                        {% if not embedding_params.use_vision_model %}disabled{% endif %}>
                    <p class="mt-1 text-xs text-gray-500">Limit the number of images processed per document to improve performance.</p>
                </div>

                <div class="mt-3">
                    <label class="inline-flex items-center">
                        <input type="checkbox" name="show_filtered_images" class="form-checkbox h-5 w-5 text-blue-600"
                               {% if show_filtered_images %}checked{% endif %}
                               {% if not embedding_params.use_vision_model %}disabled{% endif %}>
                        <span class="ml-2 text-gray-700">Show Filtered Images</span>
                    </label>
                    <p class="mt-1 text-xs text-gray-500 ml-7">Display thumbnails of filtered images with filtering reasons.</p>
                </div>
            </div>
        </div>

        <div class="flex items-start">
            <div class="flex items-center h-5">
                <input id="extract_images" name="extract_images" type="checkbox"
                    class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    {% if embedding_params.extract_images %}checked{% endif %}>
            </div>
            <div class="ml-3 text-sm">
                <label for="extract_images" class="font-medium text-gray-700">Extract Images</label>
                <p class="text-gray-500">Extract and save images from PDF documents.</p>
            </div>
        </div>

        <div class="flex items-start">
            <div class="flex items-center h-5">
                <input id="extract_locations" name="extract_locations" type="checkbox"
                    class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    {% if embedding_params.extract_locations %}checked{% endif %}>
            </div>
            <div class="ml-3 text-sm">
                <label for="extract_locations" class="font-medium text-gray-700">Extract Locations</label>
                <p class="text-gray-500">Extract geographical locations (Municipality, City, Barangay) from PDF documents using NER.</p>
                <div class="dependency-indicator">
                    <span class="text-blue-600">↑</span> Focuses on Philippine administrative divisions
                </div>
            </div>
        </div>

        <div class="ml-7 {% if not embedding_params.extract_locations %}opacity-50{% endif %}" id="locationExtractionOptions">
            <div class="mb-3 mt-4">
                <label for="location_confidence_threshold" class="block text-sm font-medium text-gray-700 mb-1">Location Confidence Threshold</label>
                <select id="location_confidence_threshold" name="location_confidence_threshold"
                    class="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:shadow-outline"
                    {% if not embedding_params.extract_locations %}disabled{% endif %}>
                    <option value="0.3" {% if embedding_params.location_confidence_threshold == 0.3 %}selected{% endif %}>Low (0.3) - Extract more locations</option>
                    <option value="0.5" {% if embedding_params.location_confidence_threshold == 0.5 or not embedding_params.location_confidence_threshold %}selected{% endif %}>Medium (0.5) - Balanced extraction</option>
                    <option value="0.7" {% if embedding_params.location_confidence_threshold == 0.7 %}selected{% endif %}>High (0.7) - Extract only confident locations</option>
                </select>
                <p class="mt-1 text-xs text-gray-500">Minimum confidence score for location extraction. Higher values reduce false positives.</p>
            </div>

            <div class="mb-3">
                <label for="max_locations_per_document" class="block text-sm font-medium text-gray-700 mb-1">Max Locations per Document</label>
                <input type="number" id="max_locations_per_document" name="max_locations_per_document"
                    min="5" max="100" value="{{ embedding_params.max_locations_per_document or 50 }}"
                    class="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:shadow-outline"
                    {% if not embedding_params.extract_locations %}disabled{% endif %}>
                <p class="mt-1 text-xs text-gray-500">Maximum number of locations to extract per PDF document.</p>
            </div>

            <div class="mt-3">
                <label class="inline-flex items-center">
                    <input type="checkbox" name="enable_geocoding" class="form-checkbox h-5 w-5 text-blue-600"
                           {% if embedding_params.enable_geocoding %}checked{% endif %}
                           {% if not embedding_params.extract_locations %}disabled{% endif %}>
                    <span class="ml-2 text-gray-700">Enable Geocoding</span>
                </label>
                <p class="mt-1 text-xs text-gray-500 ml-7">Automatically geocode extracted locations to get coordinates for mapping.</p>
            </div>
        </div>
    </div>
</div>

<!-- Performance Optimization Section -->
<div class="config-section">
    <h2 class="config-section-header">Performance Optimization</h2>
    <p class="config-section-description">Configure parameters that affect embedding performance and resource usage.</p>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
            <label for="batch_size" class="block text-sm font-medium text-gray-700 mb-1">Batch Size</label>
            <input type="number" id="batch_size" name="batch_size" min="1" max="100" value="{{ embedding_params.batch_size }}"
                class="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:shadow-outline">
            <p class="mt-1 text-xs text-gray-500">Number of chunks to process in a single batch. Higher values use more memory but may be faster.</p>
            <div class="dependency-indicator">
                <span class="text-blue-600">↑</span> Affects memory usage and processing speed
            </div>
        </div>

        <div>
            <label for="processing_threads" class="block text-sm font-medium text-gray-700 mb-1">Processing Threads</label>
            <input type="number" id="processing_threads" name="processing_threads" min="1" max="16" value="{{ embedding_params.processing_threads }}"
                class="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:shadow-outline">
            <p class="mt-1 text-xs text-gray-500">Number of parallel threads for document processing. Higher values may improve speed on multi-core systems.</p>
            <div class="dependency-indicator">
                <span class="text-blue-600">↑</span> Affects CPU usage and processing speed
            </div>
        </div>
    </div>
</div>
