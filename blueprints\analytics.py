"""
Analytics Blueprint

Handles analytics and reporting routes including:
- Analytics dashboard
- Chat history viewing
- Session management
- Device analytics
- Location mapping
- Performance monitoring

This blueprint manages all analytics-related functionality
that was previously in the main app.py file.
"""

import logging
from flask import Blueprint, render_template, request, redirect, url_for, jsonify
import db_utils
import geo_utils
import geoip_analytics
from blueprints.auth import admin_required, function_permission_required
from greeting_manager import GreetingManager
import user_management as um

# Configure logging
logger = logging.getLogger(__name__)

# Create blueprint
analytics_bp = Blueprint('analytics', __name__)

@analytics_bp.route('/admin/analytics')
@admin_required
def analytics_dashboard():
    """
    View AI analytics dashboard. Enhanced for Phase 2.
    
    Displays comprehensive analytics including usage statistics,
    performance metrics, geographical data, and greeting analytics.
    
    Returns:
        Rendered analytics dashboard template
    """
    try:
        # Get date range filters from request
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        
        # Get analytics data
        analytics = db_utils.get_analytics(start_date=start_date, end_date=end_date)
        
        # Get summary statistics
        summary = db_utils.get_analytics_summary()
        
        # Get greeting analytics for Phase 2
        greeting_manager = GreetingManager()
        greeting_analytics = greeting_manager.get_greeting_analytics(start_date=start_date, end_date=end_date)
        engagement_patterns = greeting_manager.get_time_based_engagement_patterns()
        
        # Get processing time data for charts
        processing_time_data = db_utils.get_processing_time_data(start_date=start_date, end_date=end_date)
        
        # Get query volume data
        query_volume_data = db_utils.get_query_volume_data(start_date=start_date, end_date=end_date)
        
        # Get location data for geographic visualization
        location_data = geo_utils.get_location_analytics_data(start_date=start_date, end_date=end_date)
        
        # Get model performance data
        model_performance_data = db_utils.get_model_performance_data(start_date=start_date, end_date=end_date)
        
        # Get development location for filtering
        DEV_LOCATION = geoip_analytics.get_dev_location()
        
        return render_template('analytics.html',
                              analytics=analytics,
                              summary=summary,
                              greeting_analytics=greeting_analytics,
                              engagement_patterns=engagement_patterns,
                              processing_time_data=processing_time_data,
                              query_volume_data=query_volume_data,
                              location_data=location_data,
                              start_date=start_date,
                              end_date=end_date,
                              dev_location=DEV_LOCATION,
                              model_performance_data=model_performance_data)
                              
    except Exception as e:
        logger.error(f"Error loading analytics dashboard: {str(e)}")
        return render_template('analytics.html',
                              analytics=[],
                              summary={},
                              greeting_analytics={},
                              engagement_patterns={},
                              processing_time_data=[],
                              query_volume_data=[],
                              location_data=[],
                              start_date=start_date,
                              end_date=end_date,
                              dev_location={},
                              model_performance_data=[])

@analytics_bp.route('/admin/chat_history')
@admin_required
@function_permission_required('chat_history')
def chat_history():
    """
    Display chat history interface.
    
    Shows comprehensive chat history with filtering and search capabilities.
    
    Returns:
        Rendered chat history template
    """
    try:
        history = db_utils.get_chat_history()
        return render_template('chat_history.html', history=history)
    except Exception as e:
        logger.error(f"Error loading chat history: {str(e)}")
        return render_template('chat_history.html', history=[])

@analytics_bp.route('/admin/sessions')
@admin_required
@function_permission_required('chat_sessions')
def view_sessions():
    """
    Display session management interface.
    
    Shows active and historical user sessions with management options.
    
    Returns:
        Rendered sessions template
    """
    try:
        sessions = db_utils.get_all_sessions()
        return render_template('sessions.html', sessions=sessions)
    except Exception as e:
        logger.error(f"Error loading sessions: {str(e)}")
        return render_template('sessions.html', sessions=[])

@analytics_bp.route('/admin/client-analytics')
def client_analytics():
    """
    View analytics for a specific client (legacy route, redirects to device analytics).
    
    Maintains backward compatibility for existing client analytics links.
    
    Returns:
        Redirect to device analytics
    """
    # Get client name from request
    client_name = request.args.get('client_name')
    
    # Redirect to device analytics with client name parameter
    return redirect(url_for('analytics.device_analytics', client_name=client_name))

@analytics_bp.route('/admin/device-analytics')
def device_analytics():
    """
    View analytics for a specific device.
    
    Displays detailed analytics for a specific device including
    usage patterns, session history, and performance metrics.
    
    Returns:
        Rendered device analytics template
    """
    try:
        # Get device fingerprint from request
        device_fingerprint = request.args.get('device_fingerprint')
        client_name = request.args.get('client_name')
        
        if not device_fingerprint and not client_name:
            return render_template('device_analytics.html', 
                                 device_data=None, 
                                 error="No device fingerprint or client name provided")
        
        # Get device analytics data
        device_data = db_utils.get_device_analytics(
            device_fingerprint=device_fingerprint,
            client_name=client_name
        )
        
        # Get session history for this device
        session_history = db_utils.get_device_session_history(
            device_fingerprint=device_fingerprint,
            client_name=client_name
        )
        
        # Get usage patterns
        usage_patterns = db_utils.get_device_usage_patterns(
            device_fingerprint=device_fingerprint,
            client_name=client_name
        )
        
        return render_template('device_analytics.html',
                              device_data=device_data,
                              session_history=session_history,
                              usage_patterns=usage_patterns,
                              device_fingerprint=device_fingerprint,
                              client_name=client_name)
                              
    except Exception as e:
        logger.error(f"Error loading device analytics: {str(e)}")
        return render_template('device_analytics.html',
                              device_data=None,
                              session_history=[],
                              usage_patterns={},
                              error=f"Error loading analytics: {str(e)}")

@analytics_bp.route('/admin/session/<session_id>')
@admin_required
def view_session(session_id):
    """
    View chat history for a specific session.
    
    Displays detailed session information including all messages,
    metadata, and session statistics.
    
    Args:
        session_id: The session ID to view
        
    Returns:
        Rendered session view template
    """
    try:
        history = db_utils.get_chat_history(session_id)
        
        # Get the first entry to get session details
        session_info = None
        if history:
            session_info = {
                "session_id": session_id,
                "client_name": history[0].get("client_name", "Anonymous"),
                "device_fingerprint": history[0].get("device_fingerprint", "Unknown"),
                "start_time": history[0].get("session_start"),
                "end_time": history[0].get("session_end"),
                "message_count": len(history),
                "category": history[0].get("category")
            }
            
        return render_template('session_view.html', 
                              history=history, 
                              session=session_info)
                              
    except Exception as e:
        logger.error(f"Error loading session {session_id}: {str(e)}")
        return render_template('session_view.html', 
                              history=[], 
                              session=None,
                              error=f"Error loading session: {str(e)}")

@analytics_bp.route('/location_map')
@um.function_permission_required('ai_analytics')
def location_map():
    """
    Display the location map interface.
    
    Shows an interactive map with extracted geographical locations
    from documents and their associated metadata.
    
    Returns:
        Rendered location map template
    """
    try:
        # Get location data for the map
        locations = db_utils.get_all_extracted_locations(include_sources=True)
        
        # Get location statistics
        statistics = db_utils.get_location_statistics()
        
        return render_template('location_map.html',
                              locations=locations,
                              statistics=statistics)
                              
    except Exception as e:
        logger.error(f"Error loading location map: {str(e)}")
        return render_template('location_map.html',
                              locations=[],
                              statistics={},
                              error=f"Error loading map: {str(e)}")
