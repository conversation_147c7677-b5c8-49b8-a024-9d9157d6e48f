{% extends "admin_base.html" %}

{% block title %}Location Map{% endblock %}

{% block head %}
    <!-- Leaflet CSS and JS for maps -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
          crossorigin=""/>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
            integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
            crossorigin=""></script>

    <!-- Leaflet MarkerCluster plugin for clustering markers -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.css" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.Default.css" />
    <script src="https://unpkg.com/leaflet.markercluster@1.4.1/dist/leaflet.markercluster.js"></script>

    <style>
        #locationMap {
            height: 600px;
            width: 100%;
            border-radius: 0.5rem;
            border: 1px solid #dee2e6;
        }

        .location-sidebar {
            height: 600px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            background-color: var(--bs-body-bg);
        }

        .location-item {
            border-bottom: 1px solid #dee2e6;
            padding: 1rem;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .location-item:hover {
            background-color: var(--bs-secondary-bg);
        }

        .location-item.active {
            background-color: var(--bs-primary-bg-subtle);
            border-left: 4px solid var(--bs-primary);
        }

        .location-type-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }

        .confidence-bar {
            height: 4px;
            background-color: #e9ecef;
            border-radius: 2px;
            overflow: hidden;
        }

        .confidence-fill {
            height: 100%;
            transition: width 0.3s ease;
        }

        /* ERDB Brand Colors for markers */
        .marker-pdf { background-color: #378C47; }
        .marker-chat { background-color: #0267B6; }
        .marker-url { background-color: #3CA6D6; }

        /* Custom marker styles */
        .custom-marker {
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 12px;
            width: 30px;
            height: 30px;
        }

        /* Leaflet attribution styling */
        .leaflet-control-attribution {
            background-color: rgba(255, 255, 255, 0.8) !important;
            padding: 5px 8px !important;
            font-size: 11px !important;
            line-height: 1.4 !important;
            border-radius: 4px !important;
            box-shadow: 0 1px 5px rgba(0,0,0,0.15) !important;
        }

        .leaflet-control-attribution a {
            color: #0078A8 !important;
            text-decoration: none !important;
        }

        .leaflet-control-attribution a:hover {
            text-decoration: underline !important;
        }

        /* Dark mode styles */
        .dark .location-sidebar {
            background-color: #1f2937;
            border-color: #4b5563;
        }

        .dark .location-item {
            border-color: #4b5563;
            color: #f3f4f6;
        }

        .dark .location-item:hover {
            background-color: #374151;
        }

        .dark .location-item.active {
            background-color: #1e40af;
            border-left-color: #3b82f6;
        }

        .dark .confidence-bar {
            background-color: #4b5563;
        }

        .dark .leaflet-control-attribution {
            background-color: rgba(31, 41, 55, 0.8) !important;
            color: #e5e7eb !important;
        }

        .dark .leaflet-control-attribution a {
            color: #60a5fa !important;
        }

        /* Statistics cards */
        .stat-card {
            background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-primary-dark, #0056b3) 100%);
            color: white;
            border-radius: 0.5rem;
            padding: 1.5rem;
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
    </style>
{% endblock %}

{% block content %}
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0 text-gray-800">Location Map</h1>
            <div class="d-flex gap-2">
                <select id="categoryFilter" class="form-select form-select-sm" style="width: auto;">
                    <option value="">All Categories</option>
                    {% for category in categories %}
                        <option value="{{ category }}">{{ category }}</option>
                    {% endfor %}
                </select>
                <button id="refreshMap" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-sync-alt"></i> Refresh
                </button>
            </div>
        </div>

        <!-- Statistics Row -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stat-card bg-primary">
                    <div class="stat-number">{{ statistics.total_locations }}</div>
                    <div class="stat-label">Total Locations</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card bg-success">
                    <div class="stat-number">{{ statistics.geocoded_locations }}</div>
                    <div class="stat-label">Geocoded Locations</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card bg-info">
                    <div class="stat-number">{{ "%.1f"|format(statistics.geocoding_rate) }}%</div>
                    <div class="stat-label">Geocoding Success Rate</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card bg-warning">
                    <div class="stat-number">{{ statistics.locations_by_source|length }}</div>
                    <div class="stat-label">Source Types</div>
                </div>
            </div>
        </div>

        <!-- Main Content Row -->
        <div class="row">
            <!-- Map Column -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-map-marked-alt me-2"></i>
                            Extracted Locations Map
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div id="locationMap"></div>
                    </div>
                    <div class="card-footer">
                        <small class="text-muted">
                            <strong>Data Sources:</strong> Locations extracted from PDF documents and chat conversations.
                            Geocoding provided by <a href="https://nominatim.openstreetmap.org/" target="_blank">Nominatim</a>.
                            Map data &copy; <a href="https://www.openstreetmap.org/copyright" target="_blank">OpenStreetMap</a> contributors.
                        </small>
                    </div>
                </div>
            </div>

            <!-- Sidebar Column -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-list me-2"></i>
                                Location Details
                                <span id="locationCount" class="badge bg-primary ms-2">{{ locations|length }}</span>
                            </h5>
                            <div class="btn-group">
                                <button id="bulkSelectBtn" class="btn btn-sm btn-outline-secondary" onclick="LocationMap.toggleBulkSelection()">
                                    <i class="fas fa-check-square"></i> Bulk Select
                                </button>
                                <button id="refreshMap" class="btn btn-sm btn-outline-primary" onclick="LocationMap.refresh()">
                                    <i class="fas fa-sync-alt"></i> Refresh
                                </button>
                            </div>
                        </div>

                        <!-- Bulk Actions Panel -->
                        <div id="bulkActions" class="mt-3 p-2 bg-light rounded" style="display: none;">
                            <div class="d-flex justify-content-between align-items-center">
                                <button id="selectAllBtn" class="btn btn-sm btn-outline-primary" onclick="LocationMap.toggleSelectAll()">
                                    Select All
                                </button>
                                <button id="deleteSelectedBtn" class="btn btn-sm btn-danger" onclick="LocationMap.deleteSelected()" disabled>
                                    Delete Selected (0)
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="location-sidebar" id="locationSidebar">
                            {% for location in locations %}
                                <div class="location-item" data-location-id="{{ location.id }}"
                                     data-lat="{{ location.latitude }}" data-lng="{{ location.longitude }}">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6 class="mb-1 fw-bold">{{ location.location_text }}</h6>
                                        <span class="badge location-type-badge
                                            {% if location.location_type == 'place_name' %}bg-primary
                                            {% elif location.location_type == 'address' %}bg-success
                                            {% elif location.location_type == 'coordinates' %}bg-info
                                            {% elif location.location_type == 'landmark' %}bg-warning
                                            {% else %}bg-secondary{% endif %}">
                                            {{ location.location_type.replace('_', ' ').title() }}
                                        </span>
                                    </div>

                                    {% if location.geocoded_address %}
                                        <p class="mb-2 text-muted small">{{ location.geocoded_address }}</p>
                                    {% endif %}

                                    <div class="mb-2">
                                        <small class="text-muted">Confidence:</small>
                                        <div class="confidence-bar">
                                            <div class="confidence-fill bg-success"
                                                 style="width: {{ (location.confidence_score * 100)|round }}%"></div>
                                        </div>
                                        <small class="text-muted">{{ (location.confidence_score * 100)|round }}%</small>
                                    </div>

                                    {% if location.source_filename %}
                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted">
                                                <i class="fas fa-file-pdf me-1"></i>
                                                {{ location.source_filename }}
                                                {% if location.page_number %}(p. {{ location.page_number }}){% endif %}
                                            </small>
                                            {% if location.source_category %}
                                                <span class="badge bg-light text-dark">{{ location.source_category }}</span>
                                            {% endif %}
                                        </div>
                                    {% endif %}

                                    {% if location.context_snippet %}
                                        <details class="mt-2">
                                            <summary class="text-muted small" style="cursor: pointer;">Context</summary>
                                            <p class="small mt-1 mb-0 text-muted">{{ location.context_snippet }}</p>
                                        </details>
                                    {% endif %}
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Location Map JavaScript -->
    <script src="{{ url_for('static', filename='js/location-map.js') }}"></script>
    <script>
        // Initialize the location map when the page loads
        document.addEventListener('DOMContentLoaded', function() {
            const locations = {{ locations|tojson }};
            const statistics = {{ statistics|tojson }};

            // Initialize the map
            LocationMap.init('locationMap', locations, {
                enableClustering: true,
                showStatistics: true,
                enableFiltering: true
            });

            // Set up event listeners
            document.getElementById('categoryFilter').addEventListener('change', function() {
                LocationMap.filterByCategory(this.value);
            });

            document.getElementById('refreshMap').addEventListener('click', function() {
                LocationMap.refresh();
            });
        });
    </script>
{% endblock %}
