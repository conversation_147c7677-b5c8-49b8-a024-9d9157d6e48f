#!/usr/bin/env python3
"""
Database Schema Migration for Geographical Location Extraction System

This module provides migration functions to fix the geocoding_cache table schema
inconsistency and enhance location type filtering for geographical entities only.

Migration Version: 2.0
Target: Add missing address component columns to geocoding_cache table
"""

import sqlite3
import logging
import os
from datetime import datetime
from typing import List, Dict, Any, Optional

# Import database path
from db_schema import DB_PATH

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Migration version
MIGRATION_VERSION = 2
MIGRATION_DESCRIPTION = "Add address component columns to geocoding_cache table and enhance location filtering"

def check_column_exists(cursor: sqlite3.Cursor, table_name: str, column_name: str) -> bool:
    """Check if a column exists in a table."""
    cursor.execute(f"PRAGMA table_info({table_name})")
    columns = cursor.fetchall()
    return any(col[1] == column_name for col in columns)

def get_current_database_version(cursor: sqlite3.Cursor) -> int:
    """Get the current database version."""
    try:
        cursor.execute("SELECT MAX(version) FROM database_version")
        result = cursor.fetchone()
        return result[0] if result and result[0] is not None else 0
    except sqlite3.Error:
        # database_version table doesn't exist
        return 0

def backup_table(cursor: sqlite3.Cursor, table_name: str) -> str:
    """Create a backup of a table before migration."""
    backup_name = f"{table_name}_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    cursor.execute(f"CREATE TABLE {backup_name} AS SELECT * FROM {table_name}")
    logger.info(f"Created backup table: {backup_name}")
    return backup_name

def migrate_geocoding_cache_table(cursor: sqlite3.Cursor) -> bool:
    """
    Migrate the geocoding_cache table to add missing address component columns.
    
    Returns:
        bool: True if migration successful, False otherwise
    """
    try:
        logger.info("Starting geocoding_cache table migration...")
        
        # Check if table exists
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='geocoding_cache'
        """)
        if not cursor.fetchone():
            logger.error("geocoding_cache table does not exist")
            return False
        
        # Check which columns are missing
        missing_columns = []
        required_columns = {
            'country': 'TEXT',
            'region': 'TEXT', 
            'city': 'TEXT'
        }
        
        for column_name, column_type in required_columns.items():
            if not check_column_exists(cursor, 'geocoding_cache', column_name):
                missing_columns.append((column_name, column_type))
        
        if not missing_columns:
            logger.info("All required columns already exist in geocoding_cache table")
            return True
        
        logger.info(f"Adding missing columns: {[col[0] for col in missing_columns]}")
        
        # Create backup before migration
        backup_name = backup_table(cursor, 'geocoding_cache')
        
        # Add missing columns
        for column_name, column_type in missing_columns:
            try:
                alter_sql = f"ALTER TABLE geocoding_cache ADD COLUMN {column_name} {column_type}"
                cursor.execute(alter_sql)
                logger.info(f"Added column: {column_name} {column_type}")
            except sqlite3.Error as e:
                logger.error(f"Failed to add column {column_name}: {e}")
                # Rollback by dropping the backup (it will be recreated if needed)
                try:
                    cursor.execute(f"DROP TABLE IF EXISTS {backup_name}")
                except:
                    pass
                return False
        
        # Verify all columns were added successfully
        for column_name, _ in missing_columns:
            if not check_column_exists(cursor, 'geocoding_cache', column_name):
                logger.error(f"Column {column_name} was not added successfully")
                return False
        
        logger.info("geocoding_cache table migration completed successfully")
        return True
        
    except sqlite3.Error as e:
        logger.error(f"Database error during geocoding_cache migration: {e}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error during geocoding_cache migration: {e}")
        return False

def update_database_version(cursor: sqlite3.Cursor, version: int, description: str) -> bool:
    """Update the database version after successful migration."""
    try:
        cursor.execute("""
            INSERT INTO database_version (version, description)
            VALUES (?, ?)
        """, (version, description))
        logger.info(f"Updated database version to {version}")
        return True
    except sqlite3.Error as e:
        logger.error(f"Failed to update database version: {e}")
        return False

def verify_migration(cursor: sqlite3.Cursor) -> bool:
    """Verify that the migration was successful."""
    try:
        # Check that all required columns exist
        required_columns = ['country', 'region', 'city']
        
        for column in required_columns:
            if not check_column_exists(cursor, 'geocoding_cache', column):
                logger.error(f"Verification failed: column {column} missing")
                return False
        
        # Test that we can insert and retrieve data with new columns
        test_data = {
            'location_query': 'test_migration_query',
            'latitude': 14.1648,
            'longitude': 121.2413,
            'formatted_address': 'Test Address',
            'geocoding_service': 'nominatim',
            'confidence_score': 0.9,
            'status': 'success',
            'country': 'Philippines',
            'region': 'Laguna',
            'city': 'Los Baños'
        }
        
        # Insert test data
        cursor.execute("""
            INSERT OR REPLACE INTO geocoding_cache 
            (location_query, latitude, longitude, formatted_address, geocoding_service,
             confidence_score, status, country, region, city)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            test_data['location_query'],
            test_data['latitude'],
            test_data['longitude'],
            test_data['formatted_address'],
            test_data['geocoding_service'],
            test_data['confidence_score'],
            test_data['status'],
            test_data['country'],
            test_data['region'],
            test_data['city']
        ))
        
        # Retrieve test data
        cursor.execute("""
            SELECT latitude, longitude, formatted_address, confidence_score,
                   country, region, city, status
            FROM geocoding_cache 
            WHERE location_query = ?
        """, (test_data['location_query'],))
        
        result = cursor.fetchone()
        if not result:
            logger.error("Verification failed: could not retrieve test data")
            return False
        
        # Clean up test data
        cursor.execute("DELETE FROM geocoding_cache WHERE location_query = ?", 
                      (test_data['location_query'],))
        
        logger.info("Migration verification successful")
        return True
        
    except sqlite3.Error as e:
        logger.error(f"Verification failed with database error: {e}")
        return False
    except Exception as e:
        logger.error(f"Verification failed with error: {e}")
        return False

def run_migration() -> bool:
    """
    Run the complete database migration for location extraction system.
    
    Returns:
        bool: True if migration successful, False otherwise
    """
    try:
        logger.info("Starting database migration for location extraction system")
        logger.info(f"Database path: {DB_PATH}")
        
        if not os.path.exists(DB_PATH):
            logger.error(f"Database file does not exist: {DB_PATH}")
            return False
        
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Enable foreign keys
        cursor.execute("PRAGMA foreign_keys = ON")
        
        # Check current database version
        current_version = get_current_database_version(cursor)
        logger.info(f"Current database version: {current_version}")
        
        if current_version >= MIGRATION_VERSION:
            logger.info(f"Database is already at version {current_version}, no migration needed")
            conn.close()
            return True
        
        # Start transaction
        cursor.execute("BEGIN TRANSACTION")
        
        try:
            # Run geocoding_cache table migration
            if not migrate_geocoding_cache_table(cursor):
                logger.error("geocoding_cache table migration failed")
                cursor.execute("ROLLBACK")
                conn.close()
                return False
            
            # Verify migration
            if not verify_migration(cursor):
                logger.error("Migration verification failed")
                cursor.execute("ROLLBACK")
                conn.close()
                return False
            
            # Update database version
            if not update_database_version(cursor, MIGRATION_VERSION, MIGRATION_DESCRIPTION):
                logger.error("Failed to update database version")
                cursor.execute("ROLLBACK")
                conn.close()
                return False
            
            # Commit transaction
            cursor.execute("COMMIT")
            logger.info("Database migration completed successfully")
            
        except Exception as e:
            logger.error(f"Migration failed: {e}")
            cursor.execute("ROLLBACK")
            conn.close()
            return False
        
        conn.close()
        return True
        
    except sqlite3.Error as e:
        logger.error(f"Database connection error: {e}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error during migration: {e}")
        return False

def check_migration_needed() -> bool:
    """Check if migration is needed."""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        current_version = get_current_database_version(cursor)
        needed = current_version < MIGRATION_VERSION
        
        conn.close()
        return needed
        
    except Exception:
        return True  # Assume migration is needed if we can't check

if __name__ == "__main__":
    print("🔄 Database Migration for Geographical Location Extraction System")
    print("="*70)
    
    if check_migration_needed():
        print("Migration needed - starting migration process...")
        success = run_migration()
        
        if success:
            print("✅ Migration completed successfully!")
            print("The geocoding_cache table now includes address component columns.")
        else:
            print("❌ Migration failed!")
            print("Please check the logs for details.")
    else:
        print("✅ Database is already up to date - no migration needed.")
