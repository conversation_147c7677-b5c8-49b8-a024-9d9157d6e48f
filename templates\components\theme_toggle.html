<!--
Reusable Theme Toggle Component

This component provides a standardized theme toggle button that can be used
across different templates. It maintains consistent styling and functionality
while supporting different display modes.

Usage:
{% include 'components/theme_toggle.html' %}

Parameters:
- toggle_style: 'admin', 'user', or 'chat' (default: 'admin')
- show_label: boolean (default: false)
- button_class: additional CSS classes (optional)
- icon_class: additional icon CSS classes (optional)
-->

{% set toggle_style = toggle_style or 'admin' %}
{% set show_label = show_label if show_label is defined else false %}
{% set button_class = button_class or '' %}
{% set icon_class = icon_class or '' %}

{% if toggle_style == 'admin' %}
<!-- Admin Theme Toggle -->
<div class="nav-item me-3">
    <button id="theme-toggle" class="theme-toggle {{ button_class }}" type="button" aria-label="Toggle dark mode">
        <i id="theme-icon" class="fas fa-moon theme-icon {{ icon_class }}"></i>
        {% if show_label %}
        <span class="theme-label d-none d-lg-inline-flex ms-2">Theme</span>
        {% endif %}
    </button>
</div>

{% elif toggle_style == 'user' %}
<!-- User Interface Theme Toggle -->
<button id="theme-toggle" onclick="toggleTheme()" class="theme-toggle {{ button_class }}">
    <span id="theme-icon" class="text-xl {{ icon_class }}">☀️</span>
    {% if show_label %}
    <span class="theme-label">Theme</span>
    {% endif %}
</button>

{% elif toggle_style == 'chat' %}
<!-- Chat Interface Theme Toggle -->
<button id="theme-toggle" class="btn btn-sm btn-outline-secondary theme-toggle {{ button_class }}" type="button" aria-label="Toggle theme">
    <i id="theme-icon" class="fas fa-moon {{ icon_class }}"></i>
    {% if show_label %}
    <span class="theme-label d-none d-md-inline ms-1">Theme</span>
    {% endif %}
</button>

{% elif toggle_style == 'minimal' %}
<!-- Minimal Theme Toggle -->
<button id="theme-toggle" class="theme-toggle-minimal {{ button_class }}" type="button" aria-label="Toggle theme">
    <i id="theme-icon" class="fas fa-moon {{ icon_class }}"></i>
</button>

{% endif %}

<!-- Theme Toggle Styles (included once per page) -->
{% if not theme_toggle_styles_included %}
<style>
/* Admin Theme Toggle Styles */
.theme-toggle {
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    padding: 8px 12px;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    height: 40px;
}

.theme-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.theme-toggle:active {
    transform: translateY(0);
}

.theme-icon {
    font-size: 16px;
    transition: all 0.3s ease;
}

.theme-toggle:hover .theme-icon {
    transform: rotate(15deg);
}

/* User Interface Theme Toggle Styles */
.theme-toggle {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 10px;
    color: inherit;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 44px;
    height: 44px;
}

.theme-toggle:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
}

/* Chat Interface Theme Toggle Styles */
.btn.theme-toggle {
    border-radius: 6px;
    transition: all 0.3s ease;
    min-width: 44px;
}

.btn.theme-toggle:hover {
    transform: translateY(-1px);
}

/* Minimal Theme Toggle Styles */
.theme-toggle-minimal {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-toggle-minimal:hover {
    background: rgba(0, 0, 0, 0.1);
}

/* Dark mode adjustments */
.dark-mode .theme-toggle-minimal:hover,
.dark .theme-toggle-minimal:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* Theme label styles */
.theme-label {
    font-size: 14px;
    font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .theme-toggle {
        min-width: 36px;
        height: 36px;
        padding: 6px 8px;
    }
    
    .theme-icon {
        font-size: 14px;
    }
    
    .theme-label {
        display: none !important;
    }
}

/* Animation for theme transitions */
.theme-transition {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Icon rotation animation */
@keyframes iconRotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.theme-icon.rotating {
    animation: iconRotate 0.5s ease-in-out;
}
</style>

<script>
// Theme Toggle Functionality (included once per page)
if (typeof themeToggleInitialized === 'undefined') {
    window.themeToggleInitialized = true;
    
    // Initialize theme toggle functionality
    document.addEventListener('DOMContentLoaded', function() {
        const themeToggle = document.getElementById('theme-toggle');
        const themeIcon = document.getElementById('theme-icon');
        
        if (themeToggle && themeIcon) {
            // Set initial icon based on current theme
            updateThemeIcon();
            
            // Add click event listener
            themeToggle.addEventListener('click', function() {
                toggleTheme();
            });
        }
    });
    
    function updateThemeIcon() {
        const themeIcon = document.getElementById('theme-icon');
        if (!themeIcon) return;
        
        const isDarkMode = document.documentElement.classList.contains('dark-mode') ||
                          document.documentElement.classList.contains('dark') ||
                          localStorage.getItem('theme') === 'dark';
        
        // Update icon based on current theme
        if (isDarkMode) {
            // Dark mode - show sun icon
            themeIcon.className = themeIcon.className.replace(/fa-moon|☀️/, '');
            if (themeIcon.tagName === 'I') {
                themeIcon.classList.add('fa-sun');
            } else {
                themeIcon.textContent = '☀️';
            }
        } else {
            // Light mode - show moon icon
            themeIcon.className = themeIcon.className.replace(/fa-sun/, '');
            if (themeIcon.tagName === 'I') {
                themeIcon.classList.add('fa-moon');
            } else {
                themeIcon.textContent = '🌙';
            }
        }
    }
    
    function toggleTheme() {
        const themeIcon = document.getElementById('theme-icon');
        
        // Add rotation animation
        if (themeIcon) {
            themeIcon.classList.add('rotating');
            setTimeout(() => {
                themeIcon.classList.remove('rotating');
            }, 500);
        }
        
        // Toggle theme using utilities.js if available
        if (typeof DMSUtils !== 'undefined' && DMSUtils.toggleTheme) {
            DMSUtils.toggleTheme();
        } else {
            // Fallback theme toggle
            const currentTheme = localStorage.getItem('theme') || 'light';
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            
            localStorage.setItem('theme', newTheme);
            document.documentElement.classList.toggle('dark-mode', newTheme === 'dark');
            document.documentElement.classList.toggle('dark', newTheme === 'dark');
        }
        
        // Update icon after theme change
        setTimeout(updateThemeIcon, 100);
    }
    
    // Make functions globally available
    window.updateThemeIcon = updateThemeIcon;
    window.toggleTheme = toggleTheme;
}
</script>

{% set theme_toggle_styles_included = true %}
{% endif %}
