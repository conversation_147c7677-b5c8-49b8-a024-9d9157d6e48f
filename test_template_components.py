#!/usr/bin/env python3
"""
Template Components Implementation Validation Script

This script validates the template components implementation and measures
the template duplication reduction for Phase 2, Step 2.3.

Tests:
1. Template component file creation
2. Base layout template structure
3. Component reusability validation
4. Template duplication measurement
5. Jinja2 macro functionality
"""

import os
import sys
import re
import logging
from datetime import datetime

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_component_files():
    """Test that all template component files exist."""
    print("🧪 Testing Template Component Files")
    print("=" * 45)
    
    required_components = [
        'templates/components/navbar.html',
        'templates/components/theme_toggle.html',
        'templates/components/sidebar.html',
        'templates/components/form_components.html'
    ]
    
    required_layouts = [
        'templates/base/admin_layout.html',
        'templates/base/user_layout.html'
    ]
    
    success_count = 0
    total_count = len(required_components) + len(required_layouts)
    
    print("📋 Component Files:")
    for component_file in required_components:
        if os.path.exists(component_file):
            file_size = os.path.getsize(component_file)
            print(f"✅ {component_file} ({file_size} bytes)")
            success_count += 1
        else:
            print(f"❌ {component_file} - MISSING")
    
    print("\n📋 Layout Files:")
    for layout_file in required_layouts:
        if os.path.exists(layout_file):
            file_size = os.path.getsize(layout_file)
            print(f"✅ {layout_file} ({file_size} bytes)")
            success_count += 1
        else:
            print(f"❌ {layout_file} - MISSING")
    
    print(f"\n📊 File Results: {success_count}/{total_count} files present")
    return success_count == total_count

def analyze_template_structure():
    """Analyze the structure of template components."""
    print("\n🧪 Analyzing Template Structure")
    print("=" * 40)
    
    try:
        # Analyze navbar component
        navbar_file = 'templates/components/navbar.html'
        if os.path.exists(navbar_file):
            with open(navbar_file, 'r', encoding='utf-8') as f:
                navbar_content = f.read()
            
            # Count different navbar types
            admin_navbar = 'navbar_type == \'admin\'' in navbar_content
            user_navbar = 'navbar_type == \'user\'' in navbar_content
            chat_navbar = 'navbar_type == \'chat\'' in navbar_content
            
            print(f"📋 Navbar Component Analysis:")
            print(f"   ✅ Admin navbar: {'Yes' if admin_navbar else 'No'}")
            print(f"   ✅ User navbar: {'Yes' if user_navbar else 'No'}")
            print(f"   ✅ Chat navbar: {'Yes' if chat_navbar else 'No'}")
        
        # Analyze theme toggle component
        theme_file = 'templates/components/theme_toggle.html'
        if os.path.exists(theme_file):
            with open(theme_file, 'r', encoding='utf-8') as f:
                theme_content = f.read()
            
            # Count theme toggle styles
            admin_style = 'toggle_style == \'admin\'' in theme_content
            user_style = 'toggle_style == \'user\'' in theme_content
            chat_style = 'toggle_style == \'chat\'' in theme_content
            
            print(f"\n📋 Theme Toggle Component Analysis:")
            print(f"   ✅ Admin style: {'Yes' if admin_style else 'No'}")
            print(f"   ✅ User style: {'Yes' if user_style else 'No'}")
            print(f"   ✅ Chat style: {'Yes' if chat_style else 'No'}")
        
        # Analyze form components
        form_file = 'templates/components/form_components.html'
        if os.path.exists(form_file):
            with open(form_file, 'r', encoding='utf-8') as f:
                form_content = f.read()
            
            # Count macros
            macro_count = len(re.findall(r'{% macro \w+', form_content))
            input_field = 'macro input_field' in form_content
            select_field = 'macro select_field' in form_content
            textarea_field = 'macro textarea_field' in form_content
            
            print(f"\n📋 Form Components Analysis:")
            print(f"   📊 Total macros: {macro_count}")
            print(f"   ✅ Input field macro: {'Yes' if input_field else 'No'}")
            print(f"   ✅ Select field macro: {'Yes' if select_field else 'No'}")
            print(f"   ✅ Textarea field macro: {'Yes' if textarea_field else 'No'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error analyzing template structure: {str(e)}")
        return False

def measure_template_duplication_reduction():
    """Measure the reduction in template duplication."""
    print("\n📊 Measuring Template Duplication Reduction")
    print("=" * 50)
    
    try:
        # Count lines in component files
        component_files = [
            'templates/components/navbar.html',
            'templates/components/theme_toggle.html',
            'templates/components/sidebar.html',
            'templates/components/form_components.html'
        ]
        
        component_lines = 0
        for component_file in component_files:
            if os.path.exists(component_file):
                with open(component_file, 'r', encoding='utf-8') as f:
                    lines = len(f.readlines())
                    component_lines += lines
                    print(f"   📄 {component_file}: {lines} lines")
        
        # Count lines in base layout files
        layout_files = [
            'templates/base/admin_layout.html',
            'templates/base/user_layout.html'
        ]
        
        layout_lines = 0
        for layout_file in layout_files:
            if os.path.exists(layout_file):
                with open(layout_file, 'r', encoding='utf-8') as f:
                    lines = len(f.readlines())
                    layout_lines += lines
                    print(f"   📄 {layout_file}: {lines} lines")
        
        total_component_lines = component_lines + layout_lines
        
        print(f"\n📊 Template Component Analysis:")
        print(f"   Component files: {component_lines} lines")
        print(f"   Layout files: {layout_lines} lines")
        print(f"   Total component structure: {total_component_lines} lines")
        
        # Estimate duplication reduction
        # Based on analysis, we expect ~150 lines of navbar duplication eliminated
        estimated_duplication_before = 150  # Conservative estimate
        
        if component_lines > 0:
            print(f"\n📈 Duplication Reduction Analysis:")
            print(f"   Estimated duplication before: ~{estimated_duplication_before} lines")
            print(f"   Centralized in components: {component_lines} lines")
            
            if component_lines >= estimated_duplication_before:
                print("   ✅ Target duplication reduction achieved")
                reduction_achieved = True
            else:
                print("   ⚠️  Target duplication reduction partially achieved")
                reduction_achieved = False
        else:
            reduction_achieved = False
        
        return reduction_achieved
        
    except Exception as e:
        print(f"❌ Error measuring duplication reduction: {str(e)}")
        return False

def test_jinja2_macro_functionality():
    """Test Jinja2 macro functionality in form components."""
    print("\n🧪 Testing Jinja2 Macro Functionality")
    print("=" * 40)
    
    try:
        form_file = 'templates/components/form_components.html'
        if not os.path.exists(form_file):
            print("❌ Form components file not found")
            return False
        
        with open(form_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Test for essential macros
        essential_macros = [
            'input_field',
            'select_field',
            'textarea_field',
            'checkbox_field',
            'submit_button',
            'flash_messages'
        ]
        
        macro_count = 0
        for macro_name in essential_macros:
            if f'macro {macro_name}' in content:
                print(f"   ✅ {macro_name} macro found")
                macro_count += 1
            else:
                print(f"   ❌ {macro_name} macro missing")
        
        # Test for macro parameters
        parameter_tests = [
            ('input_field', ['name', 'label', 'type', 'required']),
            ('select_field', ['name', 'label', 'options', 'selected']),
            ('submit_button', ['text', 'class', 'disabled'])
        ]
        
        print(f"\n📋 Macro Parameter Analysis:")
        for macro_name, expected_params in parameter_tests:
            macro_pattern = rf'macro {macro_name}\((.*?)\)'
            match = re.search(macro_pattern, content)
            if match:
                params = match.group(1)
                param_count = len([p for p in expected_params if p in params])
                print(f"   📊 {macro_name}: {param_count}/{len(expected_params)} expected parameters")
        
        success_rate = macro_count / len(essential_macros)
        print(f"\n📊 Macro Results: {macro_count}/{len(essential_macros)} essential macros ({success_rate:.1%})")
        
        return success_rate >= 0.8  # 80% success rate
        
    except Exception as e:
        print(f"❌ Error testing Jinja2 macros: {str(e)}")
        return False

def test_component_integration():
    """Test component integration in base layouts."""
    print("\n🧪 Testing Component Integration")
    print("=" * 35)
    
    try:
        layout_files = [
            'templates/base/admin_layout.html',
            'templates/base/user_layout.html'
        ]
        
        integration_score = 0
        total_tests = 0
        
        for layout_file in layout_files:
            if os.path.exists(layout_file):
                with open(layout_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                print(f"\n📋 {layout_file}:")
                
                # Test for component includes
                component_includes = [
                    ('navbar.html', 'components/navbar.html'),
                    ('sidebar.html', 'components/sidebar.html'),
                    ('theme_toggle.html', 'components/theme_toggle.html'),
                    ('form_components.html', 'components/form_components.html')
                ]
                
                for component_name, component_path in component_includes:
                    total_tests += 1
                    if component_path in content:
                        print(f"   ✅ {component_name} included")
                        integration_score += 1
                    else:
                        print(f"   ⚠️  {component_name} not included")
        
        success_rate = integration_score / total_tests if total_tests > 0 else 0
        print(f"\n📊 Integration Results: {integration_score}/{total_tests} components integrated ({success_rate:.1%})")
        
        return success_rate >= 0.6  # 60% integration rate
        
    except Exception as e:
        print(f"❌ Error testing component integration: {str(e)}")
        return False

def calculate_step_2_3_progress():
    """Calculate Step 2.3 completion progress."""
    print("\n📈 Step 2.3 Progress Analysis")
    print("=" * 35)
    
    try:
        # Check completion criteria
        criteria = {
            'Component Files Created': test_component_files(),
            'Template Structure Valid': analyze_template_structure(),
            'Duplication Reduction': measure_template_duplication_reduction(),
            'Jinja2 Macros Functional': test_jinja2_macro_functionality(),
            'Component Integration': test_component_integration()
        }
        
        completed_criteria = sum(criteria.values())
        total_criteria = len(criteria)
        completion_percentage = (completed_criteria / total_criteria) * 100
        
        print(f"\n📊 Step 2.3 Completion Criteria:")
        for criterion, status in criteria.items():
            status_icon = "✅" if status else "❌"
            print(f"   {status_icon} {criterion}")
        
        print(f"\n📈 Step 2.3 Progress: {completion_percentage:.1f}% ({completed_criteria}/{total_criteria} criteria)")
        
        if completion_percentage >= 80:
            print("🎉 Step 2.3: Template Components - COMPLETED")
            return True
        else:
            print("⚠️  Step 2.3: Template Components - IN PROGRESS")
            return False
        
    except Exception as e:
        print(f"❌ Error calculating progress: {str(e)}")
        return False

def main():
    """Run all template component validation tests."""
    print("🚀 Template Components Implementation Validation")
    print("=" * 60)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    tests = [
        ("Component Files", test_component_files),
        ("Template Structure", analyze_template_structure),
        ("Duplication Reduction", measure_template_duplication_reduction),
        ("Jinja2 Macros", test_jinja2_macro_functionality),
        ("Component Integration", test_component_integration),
        ("Step 2.3 Progress", calculate_step_2_3_progress)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
        except Exception as e:
            print(f"❌ Test '{test_name}' failed with exception: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests >= (total_tests * 0.8):  # 80% pass rate
        print("🎉 Template components implementation successful!")
        print("\n✅ Phase 2 Step 2.3 (Template Components) - COMPLETED")
    else:
        print("⚠️  Some tests failed. Template components need refinement.")
    
    print(f"\nTest completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return passed_tests >= (total_tests * 0.8)

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
