/* Voice Interface Styling
 * Comprehensive styling for voice interaction capabilities
 * Compatible with Bootstrap 5 and ERDB brand colors
 * Maintains WCAG AA accessibility compliance
 */

/* Voice Settings Button */
.voice-settings-button {
    cursor: pointer;
    width: 40px;
    height: 40px;
    border: none;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-light);
    color: var(--text-primary);
    transition: all var(--transition-fast);
    margin-right: 8px;
}

.voice-settings-button:hover {
    background-color: var(--primary-500);
    color: #FFFFFF;
    transform: scale(1.05);
}

.voice-settings-icon {
    font-size: 18px;
    line-height: 1;
}

/* Voice Settings Panel */
.voice-settings-panel {
    position: absolute;
    top: 60px;
    right: 20px;
    width: 320px;
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    z-index: var(--z-index-dropdown);
    transition: all var(--transition-normal);
}

.voice-settings-panel.hidden {
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
}

.voice-settings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-3);
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.voice-settings-title {
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
}

.voice-settings-close {
    background: none;
    border: none;
    font-size: 24px;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius);
    transition: all var(--transition-fast);
}

.voice-settings-close:hover {
    background: var(--danger-500);
    color: #FFFFFF;
}

.voice-settings-content {
    padding: var(--spacing-4);
}

/* Voice Setting Groups */
.voice-setting-group {
    margin-bottom: var(--spacing-4);
}

.voice-setting-group:last-child {
    margin-bottom: 0;
}

.voice-setting-label {
    display: block;
    margin-bottom: var(--spacing-2);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

.voice-setting-subtitle {
    margin: 0 0 var(--spacing-2) 0;
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--text-secondary);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: var(--spacing-1);
}

/* Form Controls */
.voice-setting-select {
    width: 100%;
    padding: var(--spacing-2);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--input-bg);
    color: var(--input-text);
    font-size: var(--font-size-sm);
    transition: all var(--transition-fast);
}

.voice-setting-select:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 2px rgba(55, 140, 71, 0.2);
}

/* Slider Controls */
.voice-slider-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
}

.voice-setting-slider {
    flex: 1;
    height: 6px;
    border-radius: 3px;
    background: var(--bg-secondary);
    outline: none;
    -webkit-appearance: none;
    appearance: none;
}

.voice-setting-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-500);
    cursor: pointer;
    border: 2px solid #FFFFFF;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all var(--transition-fast);
}

.voice-setting-slider::-webkit-slider-thumb:hover {
    background: var(--primary-600);
    transform: scale(1.1);
}

.voice-setting-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-500);
    cursor: pointer;
    border: 2px solid #FFFFFF;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all var(--transition-fast);
}

.voice-setting-slider::-moz-range-thumb:hover {
    background: var(--primary-600);
    transform: scale(1.1);
}

.voice-slider-value {
    min-width: 50px;
    text-align: center;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-secondary);
    background: var(--bg-secondary);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--border-radius);
}

/* Checkbox Controls */
.voice-setting-checkbox {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: var(--font-size-sm);
}

.voice-setting-checkbox input[type="checkbox"] {
    margin-right: var(--spacing-2);
    width: 18px;
    height: 18px;
    accent-color: var(--primary-500);
}

.voice-setting-checkbox-text {
    color: var(--text-primary);
    font-weight: var(--font-weight-normal);
}

/* Speech Control Buttons */
.speech-control-btn {
    background: none;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-1) var(--spacing-2);
    margin-left: var(--spacing-2);
    cursor: pointer;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    transition: all var(--transition-fast);
    min-width: 44px;
    min-height: 44px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.speech-control-btn:hover {
    background: var(--primary-500);
    color: #FFFFFF;
    border-color: var(--primary-500);
}

.speech-control-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.speech-control-btn.speaking {
    background: var(--warning-500);
    color: #FFFFFF;
    border-color: var(--warning-500);
    animation: pulse 1.5s infinite;
}

.speech-control-btn.paused {
    background: var(--info-500);
    color: #FFFFFF;
    border-color: var(--info-500);
}

/* Speech Progress Indicator */
.speech-progress {
    height: 3px;
    background: var(--bg-secondary);
    border-radius: 2px;
    margin-top: var(--spacing-2);
    overflow: hidden;
    position: relative;
}

.speech-progress-bar {
    height: 100%;
    background: var(--primary-500);
    border-radius: 2px;
    transition: width var(--transition-fast);
    width: 0%;
}

.speech-progress.active .speech-progress-bar {
    background: linear-gradient(90deg, var(--primary-500), var(--light-500), var(--primary-500));
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
}

/* Animations */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .voice-settings-panel {
        width: calc(100vw - 40px);
        right: 20px;
        left: 20px;
    }
    
    .voice-slider-container {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-2);
    }
    
    .voice-slider-value {
        text-align: left;
        min-width: auto;
    }
}

/* Dark Mode Specific Adjustments */
.dark-mode .voice-settings-panel,
.dark .voice-settings-panel {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .voice-setting-slider::-webkit-slider-thumb {
        border: 3px solid #000000;
    }
    
    .voice-setting-slider::-moz-range-thumb {
        border: 3px solid #000000;
    }
    
    .speech-control-btn {
        border-width: 2px;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .voice-settings-panel,
    .speech-control-btn,
    .voice-setting-slider::-webkit-slider-thumb,
    .voice-setting-slider::-moz-range-thumb {
        transition: none;
    }
    
    .speech-control-btn.speaking {
        animation: none;
        background: var(--warning-600);
    }
    
    .speech-progress.active .speech-progress-bar {
        animation: none;
        background: var(--primary-500);
    }
}
