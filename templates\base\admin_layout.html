<!DOCTYPE html>
<html lang="en" class="h-100">
<head>
    <meta charset="utf-8">
    <title>{% block title %}ERDB Document Management System{% endblock %}</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="" name="keywords">
    <meta content="" name="description">

    <!-- Favicon -->
    <link href="/static/img/favicon.ico" rel="icon">

    <!-- Google Web Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Heebo:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icon Font Stylesheet -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Libraries Stylesheet -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Toastify CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">

    <!-- Template Stylesheet -->
    <link href="/static/css/style.css" rel="stylesheet">
    
    <!-- Custom Admin Styles -->
    <link href="/static/css/admin.css" rel="stylesheet">

    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- Additional Head Content -->
    {% block head %}{% endblock %}
</head>

<body class="h-100">
    <div class="container-xxl position-relative bg-white d-flex p-0">
        <!-- Spinner Start -->
        <div id="spinner" class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
            <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
                <span class="sr-only">Loading...</span>
            </div>
        </div>
        <!-- Spinner End -->

        <!-- Sidebar -->
        {% include 'components/sidebar.html' with context %}

        <!-- Content Start -->
        <div class="content">
            <!-- Navbar -->
            {% include 'components/navbar.html' with context %}

            <!-- Content Wrapper -->
            <div class="container-fluid pt-4 px-4 content-wrapper">
                <!-- Page Header -->
                {% block page_header %}
                {% if self.page_title() %}
                <div class="bg-dark rounded p-4 mb-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <h3 class="text-light mb-0">{% block page_title %}{% endblock %}</h3>
                        <div class="btn-toolbar">
                            {% block page_actions %}{% endblock %}
                        </div>
                    </div>
                </div>
                {% endif %}
                {% endblock %}

                <!-- Flash Messages -->
                {% from 'components/form_components.html' import flash_messages %}
                {{ flash_messages() }}

                <!-- Main Content -->
                {% block content %}{% endblock %}

                <!-- Footer -->
                {% block footer %}
                <div class="container-fluid pt-4 px-4">
                    <div class="bg-dark rounded-top p-4 mt-4">
                        <div class="row">
                            <div class="col-12 col-sm-6 text-center text-sm-start">
                                <small class="text-light">&copy; {{ now.year }} ERDB Document Management System</small>
                            </div>
                            <div class="col-12 col-sm-6 text-center text-sm-end">
                                <small class="text-light">Version 2.0.0</small>
                            </div>
                        </div>
                    </div>
                </div>
                {% endblock %}
            </div>
        </div>
        <!-- Content End -->

        <!-- Back to Top -->
        <a href="#" class="btn btn-lg btn-danger btn-lg-square back-to-top"><i class="bi bi-arrow-up"></i></a>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Toastify JS -->
    <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
    
    <!-- Utilities Script -->
    <script src="/static/js/utilities.js"></script>

    <!-- Admin JavaScript -->
    <script>
        (function ($) {
            "use strict";

            // Spinner
            var spinner = function () {
                setTimeout(function () {
                    if ($('#spinner').length > 0) {
                        $('#spinner').removeClass('show');
                    }
                }, 500);
            };
            spinner();

            // Back to top button
            $(window).scroll(function () {
                if ($(this).scrollTop() > 300) {
                    $('.back-to-top').fadeIn('slow');
                } else {
                    $('.back-to-top').fadeOut('slow');
                }
            });
            $('.back-to-top').click(function () {
                $('html, body').animate({scrollTop: 0}, 1500, 'easeInOutExpo');
                return false;
            });

            // Sidebar Toggler
            $('.sidebar-toggler').click(function (e) {
                e.preventDefault();
                $('.sidebar').toggleClass('open');
                if ($(window).width() >= 992) {
                    $('.content').toggleClass('open');
                }
                return false;
            });

            // Enhanced Sidebar Dropdown Management
            function initializeSidebarDropdowns() {
                $('.sidebar .nav-item.dropdown .dropdown-toggle').off('click.sidebar');
                $('.sidebar .nav-item.dropdown .dropdown-toggle').on('click.sidebar', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    const $this = $(this);
                    const $parent = $this.parent();
                    const $dropdownMenu = $parent.find('.dropdown-menu');
                    const isExpanded = $this.attr('aria-expanded') === 'true';

                    // Close all other dropdowns
                    $('.sidebar .nav-item.dropdown').not($parent).each(function() {
                        $(this).find('.dropdown-toggle').attr('aria-expanded', 'false');
                        $(this).find('.dropdown-menu').removeClass('show').hide();
                    });

                    // Toggle current dropdown
                    if (isExpanded) {
                        $this.attr('aria-expanded', 'false');
                        $dropdownMenu.removeClass('show').slideUp(200);
                    } else {
                        $this.attr('aria-expanded', 'true');
                        $dropdownMenu.addClass('show').slideDown(200);
                    }

                    return false;
                });
            }

            // Initialize dropdowns
            $(document).ready(function() {
                initializeSidebarDropdowns();
                
                // Responsive sidebar management
                function adjustSidebar() {
                    if ($(window).width() < 992) {
                        $('.sidebar').removeClass('open');
                        $('.content').removeClass('open');
                    } else {
                        $('.sidebar').addClass('open');
                        $('.content').addClass('open');
                    }
                }

                adjustSidebar();
                $(window).resize(adjustSidebar);

                // Expand active dropdown
                setTimeout(function() {
                    $('.sidebar .dropdown-item.active').each(function() {
                        const $dropdown = $(this).closest('.dropdown');
                        if ($dropdown.length) {
                            $dropdown.find('.dropdown-toggle').attr('aria-expanded', 'true');
                            $dropdown.find('.dropdown-menu').addClass('show');
                        }
                    });
                }, 100);
            });

        })(jQuery);
    </script>

    <!-- Page-specific JavaScript -->
    {% block scripts %}{% endblock %}
</body>
</html>
