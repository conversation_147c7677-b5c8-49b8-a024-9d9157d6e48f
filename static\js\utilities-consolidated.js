/**
 * Document Management System - Consolidated Utilities
 * 
 * This file consolidates and extends the DMS Core utilities with application-specific functions.
 * It eliminates duplication from script.js, admin.js, and utilities.js while maintaining
 * backward compatibility.
 * 
 * Dependencies: dms-core.js, Bootstrap 5, Toastify.js
 */

(function(window) {
    'use strict';

    // Ensure DMSCore is available
    if (typeof window.DMSCore === 'undefined') {
        console.error('DMSCore not found. Please include dms-core.js before utilities.js');
        return;
    }

    // Extended utilities object that builds on DMSCore
    window.DMSUtils = {
        // Delegate core functions to DMSCore for consistency
        showToast: window.DMSCore.showToast.bind(window.DMSCore),
        showSuccess: window.DMSCore.showSuccess.bind(window.DMSCore),
        showError: window.DMSCore.showError.bind(window.DMSCore),
        showWarning: window.DMSCore.showWarning.bind(window.DMSCore),
        showInfo: window.DMSCore.showInfo.bind(window.DMSCore),
        api: window.DMSCore.api.bind(window.DMSCore),
        refreshCSRFToken: window.DMSCore.refreshCSRFToken.bind(window.DMSCore),

        // Theme management (enhanced from DMSCore)
        toggleTheme: window.DMSCore.theme.toggle.bind(window.DMSCore.theme),
        initializeTheme: window.DMSCore.initializeTheme.bind(window.DMSCore),

        /**
         * Enhanced dark mode toggle with application-specific fixes
         * @param {boolean} isDark - Whether to enable dark mode
         */
        toggleDarkMode: function(isDark) {
            // Use DMSCore theme management
            window.DMSCore.theme.set(isDark ? 'dark' : 'light');

            // Apply application-specific theme fixes
            this.applyTailwindThemeFixes(isDark);
            this.applyFormThemeFixes(isDark);
            this.applyTableThemeFixes(isDark);

            // Dispatch custom event for application-specific handling
            document.dispatchEvent(new CustomEvent('themeChanged', { detail: { isDark } }));
        },

        /**
         * Initialize dark mode based on user preference
         */
        initDarkMode: function() {
            // Use DMSCore theme initialization
            window.DMSCore.initializeTheme();

            // Add application-specific theme toggle handling
            const themeToggle = document.getElementById('theme-toggle');
            if (themeToggle) {
                themeToggle.addEventListener('click', () => {
                    const currentTheme = window.DMSCore.theme.getCurrent();
                    this.toggleDarkMode(currentTheme === 'light');
                });
            }

            // Listen for system theme changes
            if (window.matchMedia) {
                window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
                    if (!localStorage.getItem('theme')) {
                        this.toggleDarkMode(e.matches);
                    }
                });
            }

            // Apply fixes to dynamically loaded content
            this.observeContentChanges();
        },

        /**
         * Confirm an action with the user using Bootstrap modal
         * @param {string} message - Confirmation message
         * @param {Object} options - Additional options
         * @returns {Promise<boolean>} - Resolves to boolean (true if confirmed)
         */
        confirm: function(message, options = {}) {
            const defaultOptions = {
                title: 'Confirm Action',
                confirmText: 'Confirm',
                cancelText: 'Cancel',
                dangerous: false
            };

            const settings = { ...defaultOptions, ...options };

            // Use Bootstrap modal if available
            if (typeof bootstrap !== 'undefined') {
                return new Promise((resolve) => {
                    this.createConfirmationModal(message, settings, resolve);
                });
            } else {
                // Fallback to browser confirm
                return Promise.resolve(confirm(message));
            }
        },

        /**
         * Create or update confirmation modal
         * @private
         */
        createConfirmationModal: function(message, settings, resolve) {
            let modal = document.getElementById('confirmationModal');
            
            if (!modal) {
                // Create modal
                const modalHtml = `
                    <div class="modal fade" id="confirmationModal" tabindex="-1" aria-labelledby="confirmationModalLabel" aria-hidden="true">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="confirmationModalLabel">${settings.title}</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    ${message}
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">${settings.cancelText}</button>
                                    <button type="button" class="btn ${settings.dangerous ? 'btn-danger' : 'btn-primary'}" id="confirmBtn">${settings.confirmText}</button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                document.body.insertAdjacentHTML('beforeend', modalHtml);
                modal = document.getElementById('confirmationModal');
            } else {
                // Update existing modal
                modal.querySelector('#confirmationModalLabel').textContent = settings.title;
                modal.querySelector('.modal-body').innerHTML = message;
                modal.querySelector('.btn-secondary').textContent = settings.cancelText;
                const confirmBtn = modal.querySelector('#confirmBtn');
                confirmBtn.textContent = settings.confirmText;
                confirmBtn.className = `btn ${settings.dangerous ? 'btn-danger' : 'btn-primary'}`;
            }

            // Set up event handlers
            const confirmBtn = modal.querySelector('#confirmBtn');
            const newConfirmBtn = confirmBtn.cloneNode(true);
            confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);
            
            newConfirmBtn.addEventListener('click', () => {
                const modalInstance = bootstrap.Modal.getInstance(modal);
                modalInstance.hide();
                resolve(true);
            });

            // Handle modal close
            modal.addEventListener('hidden.bs.modal', () => resolve(false), { once: true });

            // Show modal
            const modalInstance = new bootstrap.Modal(modal);
            modalInstance.show();
        },

        /**
         * Apply theme fixes for Tailwind CSS classes
         * @param {boolean} isDark - Whether dark mode is enabled
         */
        applyTailwindThemeFixes: function(isDark) {
            const elements = {
                backgrounds: {
                    '.bg-white': isDark ? '#1f2937' : '',
                    '.bg-gray-50, .bg-gray-100': isDark ? '#374151' : ''
                },
                text: {
                    '.text-gray-800, .text-gray-900': isDark ? '#f9fafb' : '',
                    '.text-gray-700': isDark ? '#e5e7eb' : '',
                    '.text-gray-600, .text-gray-500': isDark ? '#d1d5db' : ''
                },
                borders: {
                    '.border-gray-200, .border-gray-300': isDark ? '#4b5563' : ''
                }
            };

            Object.entries(elements).forEach(([type, selectors]) => {
                Object.entries(selectors).forEach(([selector, color]) => {
                    document.querySelectorAll(selector).forEach(el => {
                        if (type === 'backgrounds') {
                            el.style.backgroundColor = color;
                        } else if (type === 'text') {
                            el.style.color = color;
                        } else if (type === 'borders') {
                            el.style.borderColor = color;
                        }
                    });
                });
            });
        },

        /**
         * Apply theme fixes for form elements
         * @param {boolean} isDark - Whether dark mode is enabled
         */
        applyFormThemeFixes: function(isDark) {
            const formElements = document.querySelectorAll(
                'input:not([type="radio"]):not([type="checkbox"]), select, textarea, .form-control, .form-select'
            );

            formElements.forEach(el => {
                if (isDark) {
                    el.style.backgroundColor = '#374151';
                    el.style.color = '#f9fafb';
                    el.style.borderColor = '#6b7280';
                } else {
                    el.style.backgroundColor = '#ffffff';
                    el.style.color = '#1a202c';
                    el.style.borderColor = '#d1d5db';
                }
            });
        },

        /**
         * Apply theme fixes for tables
         * @param {boolean} isDark - Whether dark mode is enabled
         */
        applyTableThemeFixes: function(isDark) {
            const tables = document.querySelectorAll('table, .table');

            tables.forEach(table => {
                table.style.color = isDark ? '#f9fafb' : '#1a202c';

                table.querySelectorAll('th').forEach(th => {
                    th.style.backgroundColor = isDark ? '#374151' : '#f9fafb';
                    th.style.color = isDark ? '#f9fafb' : '#1a202c';
                    th.style.borderColor = isDark ? '#4b5563' : '#e5e7eb';
                });

                table.querySelectorAll('td').forEach(td => {
                    td.style.color = isDark ? '#f9fafb' : '#1a202c';
                    td.style.borderColor = isDark ? '#4b5563' : '#e5e7eb';
                });
            });
        },

        /**
         * Observe content changes for dynamic theme application
         * @private
         */
        observeContentChanges: function() {
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        const isDark = window.DMSCore.theme.getCurrent() === 'dark';
                        this.applyTailwindThemeFixes(isDark);
                        this.applyFormThemeFixes(isDark);
                        this.applyTableThemeFixes(isDark);
                    }
                });
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        },

        /**
         * Utility functions (delegated to DMSCore)
         */
        utils: window.DMSCore.utils
    };

    // Backward compatibility aliases
    window.showToast = window.DMSUtils.showToast;
    window.api = window.DMSUtils.api;
    window.refreshCSRFToken = window.DMSUtils.refreshCSRFToken;
    window.toggleTheme = window.DMSUtils.toggleTheme;

    // Auto-initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            window.DMSUtils.initDarkMode();
        });
    } else {
        window.DMSUtils.initDarkMode();
    }

})(window);
