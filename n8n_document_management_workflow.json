{
  "name": "Document Management System Workflow",
  "active": true,
  "createdAt": "2024-01-01T00:00:00.000Z",
  "updatedAt": "2024-01-01T00:00:00.000Z",
  "versionId": "1.0.0",
  "meta": {
    "templateCredsSetupCompleted": true
  },
  "settings": {
    "executionOrder": "v1",
    "saveManualExecutions": true,
    "callerPolicy": "workflowsFromSameOwner",
    "errorWorkflow": "error-handler-workflow"
  },
  "staticData": {},
  "tags": [
    {
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z",
      "id": "1",
      "name": "Document Management"
    },
    {
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z",
      "id": "2",
      "name": "AI Processing"
    },
    {
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z",
      "id": "3",
      "name": "ERDB"
    }
  ],
  "nodes": [
    {
      "parameters": {
        "httpMethod": "POST",
        "path": "webhook-document-upload",
        "responseMode": "responseNode",
        "options": {}
      },
      "id": "webhook-upload",
      "name": "Document Upload Webhook",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 1,
      "position": [
        240,
        300
      ]
    },
    {
      "parameters": {
        "conditions": {
          "options": {
            "caseSensitive": true,
            "leftValue": "",
            "typeValidation": "strict"
          },
          "conditions": [
            {
              "id": "file-type-check",
              "leftValue": "={{ $json.file_type }}",
              "rightValue": "application/pdf",
              "operator": {
                "type": "string",
                "operation": "equals"
              }
            }
          ],
          "combinator": "and"
        },
        "options": {}
      },
      "id": "file-validation",
      "name": "File Type Validation",
      "type": "n8n-nodes-base.if",
      "typeVersion": 2,
      "position": [
        460,
        300
      ]
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "SELECT id, filename FROM pdf_documents WHERE filename = ? AND category = ?",
        "additionalFields": {
          "queryParameters": "={{ [$json.filename, $json.category] }}"
        }
      },
      "id": "duplicate-check",
      "name": "Check Duplicate PDF",
      "type": "n8n-nodes-base.sqlite",
      "typeVersion": 1,
      "position": [
        680,
        300
      ]
    },
    {
      "parameters": {
        "conditions": {
          "options": {
            "caseSensitive": true,
            "leftValue": "",
            "typeValidation": "strict"
          },
          "conditions": [
            {
              "id": "duplicate-exists",
              "leftValue": "={{ $json.length }}",
              "rightValue": 0,
              "operator": {
                "type": "number",
                "operation": "equals"
              }
            }
          ],
          "combinator": "and"
        },
        "options": {}
      },
      "id": "duplicate-decision",
      "name": "Duplicate Decision",
      "type": "n8n-nodes-base.if",
      "typeVersion": 2,
      "position": [
        900,
        300
      ]
    },
    {
      "parameters": {
        "url": "http://localhost:11434/api/tags",
        "options": {}
      },
      "id": "check-ollama-models",
      "name": "Check Available Models",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.1,
      "position": [
        1120,
        300
      ]
    },
    {
      "parameters": {
        "jsCode": "// Create temporary directory structure\nconst category = $input.first().json.category;\nconst filename = $input.first().json.filename;\nconst baseDir = './_temp';\nconst categoryDir = `${baseDir}/${category}`;\nconst fileDir = `${categoryDir}/${filename.replace('.pdf', '')}`;\n\n// Directory structure\nconst directories = {\n  base: baseDir,\n  category: categoryDir,\n  file: fileDir,\n  pdf_images: `${fileDir}/pdf_images`,\n  pdf_tables: `${fileDir}/pdf_tables`,\n  pdf_path: `${fileDir}/${filename}`\n};\n\nreturn {\n  json: {\n    ...directories,\n    category,\n    filename\n  }\n};"
      },
      "id": "create-temp-dirs",
      "name": "Create Temp Directories",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        1340,
        300
      ]
    },
    {
      "parameters": {
        "url": "http://localhost:5000/api/pdf/process",
        "sendBody": true,
        "specifyBody": "json",
        "jsonBody": "={{ {\n  \"pdf_path\": $json.pdf_path,\n  \"category\": $json.category,\n  \"source_url\": $json.source_url || null,\n  \"extract_tables\": true,\n  \"save_images\": true,\n  \"save_tables\": true,\n  \"use_vision\": $json.use_vision || false,\n  \"filter_sensitivity\": $json.filter_sensitivity || 0.7,\n  \"max_images\": $json.max_images || 10,\n  \"extract_locations\": true\n} }}",
        "options": {
          "timeout": 300000
        }
      },
      "id": "pdf-processing",
      "name": "PDF Text & Image Extraction",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.1,
      "position": [
        1560,
        300
      ]
    },
    {
      "parameters": {
        "conditions": {
          "options": {
            "caseSensitive": true,
            "leftValue": "",
            "typeValidation": "strict"
          },
          "conditions": [
            {
              "id": "vision-enabled",
              "leftValue": "={{ $json.use_vision }}",
              "rightValue": true,
              "operator": {
                "type": "boolean",
                "operation": "equals"
              }
            }
          ],
          "combinator": "and"
        },
        "options": {}
      },
      "id": "vision-check",
      "name": "Vision Analysis Check",
      "type": "n8n-nodes-base.if",
      "typeVersion": 2,
      "position": [
        1780,
        300
      ]
    },
    {
      "parameters": {
        "url": "http://localhost:11434/api/generate",
        "sendBody": true,
        "specifyBody": "json",
        "jsonBody": "={{ {\n  \"model\": \"llama3.2-vision:11b-instruct-q4_K_M\",\n  \"prompt\": \"Analyze this image and determine if it contains relevant information for a document management system. Rate relevance from 0-1 and provide a brief description.\",\n  \"images\": [$json.base64_image],\n  \"stream\": false\n} }}",
        "options": {
          "timeout": 120000
        }
      },
      "id": "vision-analysis",
      "name": "Llama Vision Analysis",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.1,
      "position": [
        2000,
        200
      ]
    },
    {
      "parameters": {
        "url": "http://localhost:11434/api/embeddings",
        "sendBody": true,
        "specifyBody": "json",
        "jsonBody": "={{ {\n  \"model\": \"mxbai-embed-large:latest\",\n  \"prompt\": $json.text_chunk\n} }}",
        "options": {
          "timeout": 60000
        }
      },
      "id": "generate-embeddings",
      "name": "Generate Text Embeddings",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.1,
      "position": [
        2000,
        400
      ]
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "INSERT INTO pdf_documents (filename, original_filename, category, upload_date, source_url_id) VALUES (?, ?, ?, datetime('now'), ?)",
        "additionalFields": {
          "queryParameters": "={{ [$json.filename, $json.original_filename, $json.category, $json.source_url_id] }}"
        }
      },
      "id": "save-pdf-record",
      "name": "Save PDF to Database",
      "type": "n8n-nodes-base.sqlite",
      "typeVersion": 1,
      "position": [
        2220,
        300
      ]
    },
    {
      "parameters": {
        "url": "http://localhost:8000/api/chroma/add",
        "sendBody": true,
        "specifyBody": "json",
        "jsonBody": "={{ {\n  \"collection_name\": $json.category,\n  \"documents\": $json.text_chunks,\n  \"embeddings\": $json.embeddings,\n  \"metadatas\": $json.metadata,\n  \"ids\": $json.chunk_ids\n} }}",
        "options": {}
      },
      "id": "store-vectors",
      "name": "Store in ChromaDB",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.1,
      "position": [
        2440,
        300
      ]
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "INSERT INTO cover_images (pdf_document_id, image_path, image_url, source, description) VALUES (?, ?, ?, ?, ?)",
        "additionalFields": {
          "queryParameters": "={{ [$json.pdf_id, $json.image_path, $json.image_url, $json.source, $json.description] }}"
        }
      },
      "id": "save-cover-image",
      "name": "Save Cover Image",
      "type": "n8n-nodes-base.sqlite",
      "typeVersion": 1,
      "position": [
        2660,
        300
      ]
    },
    {
      "parameters": {
        "jsCode": "// Extract geographical locations using NER\nconst text = $input.first().json.extracted_text;\nconst category = $input.first().json.category;\n\n// Philippine administrative levels to extract\nconst locationPatterns = {\n  municipality: /\\b([A-Z][a-z]+(?:\\s+[A-Z][a-z]+)*),?\\s+(?:Municipality|Mun\\.)/gi,\n  city: /\\b([A-Z][a-z]+(?:\\s+[A-Z][a-z]+)*),?\\s+City/gi,\n  barangay: /\\bBarangay\\s+([A-Z][a-z]+(?:\\s+[A-Z][a-z]+)*)/gi\n};\n\nconst extractedLocations = [];\n\n// Extract each type of location\nfor (const [type, pattern] of Object.entries(locationPatterns)) {\n  let match;\n  while ((match = pattern.exec(text)) !== null) {\n    extractedLocations.push({\n      location_name: match[1].trim(),\n      location_type: type,\n      administrative_level: type === 'barangay' ? 3 : (type === 'municipality' ? 2 : 1),\n      extraction_method: 'regex',\n      source_text: match[0],\n      category: category\n    });\n  }\n}\n\nreturn {\n  json: {\n    locations: extractedLocations,\n    total_found: extractedLocations.length\n  }\n};"
      },
      "id": "extract-locations",
      "name": "Extract Philippine Locations",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        2880,
        300
      ]
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "INSERT INTO extracted_locations (location_name, location_type, administrative_level, latitude, longitude, extraction_method, confidence_score) VALUES (?, ?, ?, ?, ?, ?, ?)",
        "additionalFields": {
          "queryParameters": "={{ [$json.location_name, $json.location_type, $json.administrative_level, $json.latitude, $json.longitude, $json.extraction_method, $json.confidence_score] }}"
        }
      },
      "id": "save-locations",
      "name": "Save Extracted Locations",
      "type": "n8n-nodes-base.sqlite",
      "typeVersion": 1,
      "position": [
        3100,
        300
      ]
    },
    {
      "parameters": {
        "respondWith": "json",
        "responseBody": "={{ {\n  \"success\": true,\n  \"message\": \"Document processed successfully\",\n  \"pdf_id\": $json.pdf_id,\n  \"chunks_created\": $json.chunk_count,\n  \"images_extracted\": $json.image_count,\n  \"tables_extracted\": $json.table_count,\n  \"locations_found\": $json.locations_count,\n  \"processing_time\": $json.processing_time\n} }}"
      },
      "id": "upload-response",
      "name": "Upload Success Response",
      "type": "n8n-nodes-base.respondToWebhook",
      "typeVersion": 1,
      "position": [
        3320,
        300
      ]
    },
    {
      "parameters": {
        "httpMethod": "POST",
        "path": "webhook-chat-query",
        "responseMode": "responseNode",
        "options": {}
      },
      "id": "webhook-chat",
      "name": "Chat Query Webhook",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 1,
      "position": [
        240,
        800
      ]
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "SELECT username, role, account_status FROM users WHERE session_token = ? AND account_status = 'active'",
        "additionalFields": {
          "queryParameters": "={{ [$json.session_token] }}"
        }
      },
      "id": "authenticate-user",
      "name": "Authenticate User Session",
      "type": "n8n-nodes-base.sqlite",
      "typeVersion": 1,
      "position": [
        460,
        800
      ]
    },
    {
      "parameters": {
        "conditions": {
          "options": {
            "caseSensitive": true,
            "leftValue": "",
            "typeValidation": "strict"
          },
          "conditions": [
            {
              "id": "user-authenticated",
              "leftValue": "={{ $json.length }}",
              "rightValue": 0,
              "operator": {
                "type": "number",
                "operation": "gt"
              }
            }
          ],
          "combinator": "and"
        },
        "options": {}
      },
      "id": "auth-check",
      "name": "Authentication Check",
      "type": "n8n-nodes-base.if",
      "typeVersion": 2,
      "position": [
        680,
        800
      ]
    },
    {
      "parameters": {
        "jsCode": "// Generate device fingerprint for session tracking\nconst userAgent = $input.first().json.user_agent || '';\nconst ipAddress = $input.first().json.ip_address || '';\nconst timestamp = new Date().toISOString();\n\n// Simple fingerprint generation\nconst fingerprint = Buffer.from(`${userAgent}-${ipAddress}-${timestamp}`).toString('base64').substring(0, 32);\n\n// Generate session ID\nconst sessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;\n\nreturn {\n  json: {\n    ...($input.first().json),\n    device_fingerprint: fingerprint,\n    session_id: sessionId,\n    session_start: timestamp\n  }\n};"
      },
      "id": "session-management",
      "name": "Session Management",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        900,
        800
      ]
    },
    {
      "parameters": {
        "url": "http://localhost:8000/api/chroma/query",
        "sendBody": true,
        "specifyBody": "json",
        "jsonBody": "={{ {\n  \"collection_name\": $json.category,\n  \"query_texts\": [$json.question],\n  \"n_results\": $json.max_results || 10,\n  \"where\": $json.metadata_filter || {}\n} }}",
        "options": {}
      },
      "id": "vector-search",
      "name": "Vector Similarity Search",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.1,
      "position": [
        1120,
        800
      ]
    },
    {
      "parameters": {
        "conditions": {
          "options": {
            "caseSensitive": true,
            "leftValue": "",
            "typeValidation": "strict"
          },
          "conditions": [
            {
              "id": "anti-hallucination-enabled",
              "leftValue": "={{ $json.anti_hallucination_mode }}",
              "rightValue": "strict",
              "operator": {
                "type": "string",
                "operation": "equals"
              }
            }
          ],
          "combinator": "and"
        },
        "options": {}
      },
      "id": "hallucination-check",
      "name": "Anti-Hallucination Check",
      "type": "n8n-nodes-base.if",
      "typeVersion": 2,
      "position": [
        1340,
        800
      ]
    },
    {
      "parameters": {
        "url": "http://localhost:11434/api/generate",
        "sendBody": true,
        "specifyBody": "json",
        "jsonBody": "={{ {\n  \"model\": \"llama3.1:8b-instruct-q4_K_M\",\n  \"prompt\": `You are an AI assistant for ERDB (Ecosystems Research and Development Bureau). Based ONLY on the following context, answer the user's question. If the information is not in the context, say so clearly.\\n\\nContext: ${$json.retrieved_context}\\n\\nQuestion: ${$json.question}\\n\\nAnswer:\`,\n  \"stream\": false,\n  \"options\": {\n    \"temperature\": $json.temperature || 0.7,\n    \"top_p\": $json.top_p || 0.9,\n    \"repeat_penalty\": $json.repeat_penalty || 1.1\n  }\n} }}",
        "options": {
          "timeout": 120000
        }
      },
      "id": "llm-generation",
      "name": "LLM Response Generation",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.1,
      "position": [
        1560,
        800
      ]
    },
    {
      "parameters": {
        "jsCode": "// Generate follow-up questions based on document categories\nconst category = $input.first().json.category;\nconst availableCategories = $input.first().json.available_categories || [];\n\n// Template follow-up questions by category\nconst followUpTemplates = {\n  'research': [\n    'What research methodologies were used in this study?',\n    'What are the key findings and recommendations?',\n    'How does this research relate to ecosystem management?'\n  ],\n  'policy': [\n    'What are the implementation guidelines for this policy?',\n    'Who are the stakeholders involved in this policy?',\n    'What are the compliance requirements?'\n  ],\n  'technical': [\n    'What are the technical specifications mentioned?',\n    'What equipment or tools are recommended?',\n    'Are there any safety considerations?'\n  ],\n  'default': [\n    'Can you provide more details about this topic?',\n    'What are the main recommendations?',\n    'How does this relate to ERDB\\'s mission?'\n  ]\n};\n\nconst questions = followUpTemplates[category] || followUpTemplates['default'];\n\nreturn {\n  json: {\n    follow_up_questions: questions.slice(0, 3),\n    category: category\n  }\n};"
      },
      "id": "generate-followups",
      "name": "Generate Follow-up Questions",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        1780,
        800
      ]
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "INSERT INTO chat_history (category, question, answer, sources, images, pdf_links, metadata, client_name, session_id, session_start, device_fingerprint, anti_hallucination_mode, model_name, embedding_model, vision_model) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
        "additionalFields": {
          "queryParameters": "={{ [$json.category, $json.question, $json.answer, JSON.stringify($json.sources), JSON.stringify($json.images), JSON.stringify($json.pdf_links), JSON.stringify($json.metadata), $json.client_name, $json.session_id, $json.session_start, $json.device_fingerprint, $json.anti_hallucination_mode, $json.model_name, $json.embedding_model, $json.vision_model] }}"
        }
      },
      "id": "save-chat-history",
      "name": "Save Chat History",
      "type": "n8n-nodes-base.sqlite",
      "typeVersion": 1,
      "position": [
        2000,
        800
      ]
    },
    {
      "parameters": {
        "url": "https://ipapi.co/json/",
        "options": {}
      },
      "id": "get-geolocation",
      "name": "Get User Geolocation",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.1,
      "position": [
        2220,
        800
      ]
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "INSERT INTO analytics (chat_id, session_id, category, client_name, question_length, answer_length, processing_time, source_count, image_count, model_name, embedding_model, vision_model, anti_hallucination_mode, device_fingerprint, ip_address, city, region, country, latitude, longitude) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
        "additionalFields": {
          "queryParameters": "={{ [$json.chat_id, $json.session_id, $json.category, $json.client_name, $json.question_length, $json.answer_length, $json.processing_time, $json.source_count, $json.image_count, $json.model_name, $json.embedding_model, $json.vision_model, $json.anti_hallucination_mode, $json.device_fingerprint, $json.ip_address, $json.city, $json.region, $json.country, $json.latitude, $json.longitude] }}"
        }
      },
      "id": "save-analytics",
      "name": "Save Analytics Data",
      "type": "n8n-nodes-base.sqlite",
      "typeVersion": 1,
      "position": [
        2440,
        800
      ]
    },
    {
      "parameters": {
        "respondWith": "json",
        "responseBody": "={{ {\n  \"answer\": $json.answer,\n  \"sources\": $json.sources,\n  \"images\": $json.images,\n  \"pdf_links\": $json.pdf_links,\n  \"follow_up_questions\": $json.follow_up_questions,\n  \"metadata\": {\n    \"processing_time\": $json.processing_time,\n    \"model_used\": $json.model_name,\n    \"anti_hallucination_mode\": $json.anti_hallucination_mode,\n    \"source_count\": $json.source_count\n  },\n  \"session_id\": $json.session_id,\n  \"session_start\": $json.session_start\n} }}"
      },
      "id": "chat-response",
      "name": "Chat Response",
      "type": "n8n-nodes-base.respondToWebhook",
      "typeVersion": 1,
      "position": [
        2660,
        800
      ]
    },
    {
      "parameters": {
        "httpMethod": "POST",
        "path": "webhook-user-management",
        "responseMode": "responseNode",
        "options": {}
      },
      "id": "webhook-user-mgmt",
      "name": "User Management Webhook",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 1,
      "position": [
        240,
        1300
      ]
    },
    {
      "parameters": {
        "conditions": {
          "options": {
            "caseSensitive": true,
            "leftValue": "",
            "typeValidation": "strict"
          },
          "conditions": [
            {
              "id": "action-type",
              "leftValue": "={{ $json.action }}",
              "rightValue": "register",
              "operator": {
                "type": "string",
                "operation": "equals"
              }
            }
          ],
          "combinator": "and"
        },
        "options": {}
      },
      "id": "user-action-switch",
      "name": "User Action Switch",
      "type": "n8n-nodes-base.if",
      "typeVersion": 2,
      "position": [
        460,
        1300
      ]
    },
    {
      "parameters": {
        "jsCode": "// Validate user registration data\nconst { username, email, password, full_name } = $input.first().json;\n\n// Username validation (3-20 alphanumeric characters)\nconst usernameValid = /^[a-zA-Z0-9_]{3,20}$/.test(username);\n\n// Email validation\nconst emailValid = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(email);\n\n// Password complexity validation\nconst passwordValid = password && password.length >= 8 && \n  /[A-Z]/.test(password) && \n  /[a-z]/.test(password) && \n  /[0-9]/.test(password) && \n  /[^A-Za-z0-9]/.test(password);\n\nconst validationErrors = [];\nif (!usernameValid) validationErrors.push('Username must be 3-20 alphanumeric characters or underscores');\nif (!emailValid) validationErrors.push('Invalid email address');\nif (!passwordValid) validationErrors.push('Password must be at least 8 characters with uppercase, lowercase, number, and special character');\n\nreturn {\n  json: {\n    ...($input.first().json),\n    validation_passed: validationErrors.length === 0,\n    validation_errors: validationErrors\n  }\n};"
      },
      "id": "validate-user-data",
      "name": "Validate User Registration",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        680,
        1200
      ]
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "SELECT user_id FROM users WHERE username = ? OR email = ?",
        "additionalFields": {
          "queryParameters": "={{ [$json.username, $json.email] }}"
        }
      },
      "id": "check-user-exists",
      "name": "Check User Exists",
      "type": "n8n-nodes-base.sqlite",
      "typeVersion": 1,
      "position": [
        900,
        1200
      ]
    },
    {
      "parameters": {
        "jsCode": "// Hash password using bcrypt-like approach (simplified for demo)\nconst crypto = require('crypto');\nconst password = $input.first().json.password;\nconst salt = crypto.randomBytes(16).toString('hex');\nconst hash = crypto.pbkdf2Sync(password, salt, 10000, 64, 'sha512').toString('hex');\nconst hashedPassword = `${salt}:${hash}`;\n\n// Generate verification token\nconst verificationToken = crypto.randomBytes(32).toString('hex');\n\nreturn {\n  json: {\n    ...($input.first().json),\n    hashed_password: hashedPassword,\n    verification_token: verificationToken,\n    created_at: new Date().toISOString()\n  }\n};"
      },
      "id": "hash-password",
      "name": "Hash Password & Generate Token",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        1120,
        1200
      ]
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "INSERT INTO users (username, email, password_hash, role, account_status, full_name, email_verification_token, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
        "additionalFields": {
          "queryParameters": "={{ [$json.username, $json.email, $json.hashed_password, 'viewer', 'pending_verification', $json.full_name, $json.verification_token, $json.created_at] }}"
        }
      },
      "id": "create-user",
      "name": "Create User Record",
      "type": "n8n-nodes-base.sqlite",
      "typeVersion": 1,
      "position": [
        1340,
        1200
      ]
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "INSERT INTO activity_logs (user_id, action, details, ip_address, user_agent, created_at) VALUES (?, ?, ?, ?, ?, ?)",
        "additionalFields": {
          "queryParameters": "={{ [$json.user_id, 'user_registration', JSON.stringify({username: $json.username, email: $json.email}), $json.ip_address, $json.user_agent, $json.created_at] }}"
        }
      },
      "id": "log-user-activity",
      "name": "Log User Activity",
      "type": "n8n-nodes-base.sqlite",
      "typeVersion": 1,
      "position": [
        1560,
        1200
      ]
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "SELECT username, email, role, account_status, created_at FROM users WHERE username = ? AND password_hash = ?",
        "additionalFields": {
          "queryParameters": "={{ [$json.username, $json.hashed_password] }}"
        }
      },
      "id": "authenticate-login",
      "name": "Authenticate Login",
      "type": "n8n-nodes-base.sqlite",
      "typeVersion": 1,
      "position": [
        680,
        1400
      ]
    },
    {
      "parameters": {
        "jsCode": "// Generate session token for authenticated user\nconst crypto = require('crypto');\nconst sessionToken = crypto.randomBytes(32).toString('hex');\nconst expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(); // 24 hours\n\nreturn {\n  json: {\n    ...($input.first().json),\n    session_token: sessionToken,\n    expires_at: expiresAt,\n    last_login: new Date().toISOString()\n  }\n};"
      },
      "id": "generate-session",
      "name": "Generate Session Token",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        900,
        1400
      ]
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "UPDATE users SET session_token = ?, session_expires = ?, last_login = ? WHERE username = ?",
        "additionalFields": {
          "queryParameters": "={{ [$json.session_token, $json.expires_at, $json.last_login, $json.username] }}"
        }
      },
      "id": "update-session",
      "name": "Update User Session",
      "type": "n8n-nodes-base.sqlite",
      "typeVersion": 1,
      "position": [
        1120,
        1400
      ]
    },
    {
      "parameters": {
        "httpMethod": "DELETE",
        "path": "webhook-delete-document",
        "responseMode": "responseNode",
        "options": {}
      },
      "id": "webhook-delete",
      "name": "Document Deletion Webhook",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 1,
      "position": [
        240,
        1800
      ]
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "SELECT id, filename, category FROM pdf_documents WHERE id = ?",
        "additionalFields": {
          "queryParameters": "={{ [$json.document_id] }}"
        }
      },
      "id": "get-document-info",
      "name": "Get Document Info",
      "type": "n8n-nodes-base.sqlite",
      "typeVersion": 1,
      "position": [
        460,
        1800
      ]
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "DELETE FROM extracted_locations WHERE id IN (SELECT location_id FROM location_sources WHERE source_type = 'pdf_document' AND source_id = ?)",
        "additionalFields": {
          "queryParameters": "={{ [$json.document_id] }}"
        }
      },
      "id": "delete-locations",
      "name": "Delete Associated Locations",
      "type": "n8n-nodes-base.sqlite",
      "typeVersion": 1,
      "position": [
        680,
        1800
      ]
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "DELETE FROM cover_images WHERE pdf_document_id = ?",
        "additionalFields": {
          "queryParameters": "={{ [$json.document_id] }}"
        }
      },
      "id": "delete-cover-images",
      "name": "Delete Cover Images",
      "type": "n8n-nodes-base.sqlite",
      "typeVersion": 1,
      "position": [
        900,
        1800
      ]
    },
    {
      "parameters": {
        "url": "http://localhost:8000/api/chroma/delete",
        "sendBody": true,
        "specifyBody": "json",
        "jsonBody": "={{ {\n  \"collection_name\": $json.category,\n  \"where\": {\"filename\": $json.filename}\n} }}",
        "options": {}
      },
      "id": "delete-vectors",
      "name": "Delete Vector Embeddings",
      "type": "n8n-nodes-base.httpRequest",
      "typeVersion": 4.1,
      "position": [
        1120,
        1800
      ]
    },
    {
      "parameters": {
        "jsCode": "// Clean up temporary files and directories\nconst fs = require('fs');\nconst path = require('path');\nconst category = $input.first().json.category;\nconst filename = $input.first().json.filename;\nconst baseDir = './_temp';\n\n// Remove PDF-specific directory\nconst pdfDir = path.join(baseDir, category, filename.replace('.pdf', ''));\n\ntry {\n  if (fs.existsSync(pdfDir)) {\n    fs.rmSync(pdfDir, { recursive: true, force: true });\n  }\n  \n  return {\n    json: {\n      ...($input.first().json),\n      cleanup_success: true,\n      cleaned_directory: pdfDir\n    }\n  };\n} catch (error) {\n  return {\n    json: {\n      ...($input.first().json),\n      cleanup_success: false,\n      cleanup_error: error.message\n    }\n  };\n}"
      },
      "id": "cleanup-files",
      "name": "Cleanup Temporary Files",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [
        1340,
        1800
      ]
    },
    {
      "parameters": {
        "operation": "executeQuery",
        "query": "DELETE FROM pdf_documents WHERE id = ?",
        "additionalFields": {
          "queryParameters": "={{ [$json.document_id] }}"
        }
      },
      "id": "delete-document-record",
      "name": "Delete Document Record",
      "type": "n8n-nodes-base.sqlite",
      "typeVersion": 1,
      "position": [
        1560,
        1800
      ]
    },
    {
      "parameters": {
        "respondWith": "json",
        "responseBody": "={{ {\n  \"success\": true,\n  \"message\": \"Document and all associated resources deleted successfully\",\n  \"document_id\": $json.document_id,\n  \"filename\": $json.filename,\n  \"cleanup_performed\": {\n    \"locations\": true,\n    \"cover_images\": true,\n    \"vector_embeddings\": true,\n    \"temporary_files\": $json.cleanup_success,\n    \"database_record\": true\n  }\n} }}"
      },
      "id": "delete-response",
      "name": "Deletion Success Response",
      "type": "n8n-nodes-base.respondToWebhook",
      "typeVersion": 1,
      "position": [
        1780,
        1800
      ]
    },
    {
      "parameters": {
        "respondWith": "json",
        "responseBody": "={{ {\n  \"error\": true,\n  \"message\": \"File validation failed\",\n  \"details\": \"Only PDF files are allowed\",\n  \"file_type\": $json.file_type\n} }}",
        "options": {
          "responseCode": 400
        }
      },
      "id": "file-error-response",
      "name": "File Validation Error",
      "type": "n8n-nodes-base.respondToWebhook",
      "typeVersion": 1,
      "position": [
        460,
        500
      ]
    },
    {
      "parameters": {
        "respondWith": "json",
        "responseBody": "={{ {\n  \"error\": true,\n  \"message\": \"Duplicate file detected\",\n  \"existing_file\": $json[0],\n  \"action_required\": \"Choose to replace or rename\"\n} }}",
        "options": {
          "responseCode": 409
        }
      },
      "id": "duplicate-error-response",
      "name": "Duplicate File Error",
      "type": "n8n-nodes-base.respondToWebhook",
      "typeVersion": 1,
      "position": [
        900,
        500
      ]
    },
    {
      "parameters": {
        "respondWith": "json",
        "responseBody": "={{ {\n  \"error\": true,\n  \"message\": \"Authentication failed\",\n  \"details\": \"Invalid or expired session token\"\n} }}",
        "options": {
          "responseCode": 401
        }
      },
      "id": "auth-error-response",
      "name": "Authentication Error",
      "type": "n8n-nodes-base.respondToWebhook",
      "typeVersion": 1,
      "position": [
        680,
        1000
      ]
    },
    {
      "parameters": {
        "respondWith": "json",
        "responseBody": "={{ {\n  \"error\": true,\n  \"message\": \"User registration failed\",\n  \"validation_errors\": $json.validation_errors\n} }}",
        "options": {
          "responseCode": 400
        }
      },
      "id": "user-validation-error",
      "name": "User Validation Error",
      "type": "n8n-nodes-base.respondToWebhook",
      "typeVersion": 1,
      "position": [
        680,
        1500
      ]
    },
    {
      "parameters": {
        "respondWith": "json",
        "responseBody": "={{ {\n  \"success\": true,\n  \"message\": \"User registered successfully\",\n  \"user_id\": $json.user_id,\n  \"verification_required\": true\n} }}"
      },
      "id": "user-success-response",
      "name": "User Registration Success",
      "type": "n8n-nodes-base.respondToWebhook",
      "typeVersion": 1,
      "position": [
        1780,
        1200
      ]
    },
    {
      "parameters": {
        "respondWith": "json",
        "responseBody": "={{ {\n  \"success\": true,\n  \"message\": \"Login successful\",\n  \"session_token\": $json.session_token,\n  \"user\": {\n    \"username\": $json.username,\n    \"email\": $json.email,\n    \"role\": $json.role\n  },\n  \"expires_at\": $json.expires_at\n} }}"
      },
      "id": "login-success-response",
      "name": "Login Success Response",
      "type": "n8n-nodes-base.respondToWebhook",
      "typeVersion": 1,
      "position": [
        1340,
        1400
      ]
    }
  ],
  "connections": {
    "Document Upload Webhook": {
      "main": [
        [
          {
            "node": "File Type Validation",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "File Type Validation": {
      "main": [
        [
          {
            "node": "Check Duplicate PDF",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "File Validation Error",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Check Duplicate PDF": {
      "main": [
        [
          {
            "node": "Duplicate Decision",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Duplicate Decision": {
      "main": [
        [
          {
            "node": "Check Available Models",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Duplicate File Error",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Check Available Models": {
      "main": [
        [
          {
            "node": "Create Temp Directories",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Create Temp Directories": {
      "main": [
        [
          {
            "node": "PDF Text & Image Extraction",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "PDF Text & Image Extraction": {
      "main": [
        [
          {
            "node": "Vision Analysis Check",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Vision Analysis Check": {
      "main": [
        [
          {
            "node": "Llama Vision Analysis",
            "type": "main",
            "index": 0
          },
          {
            "node": "Generate Text Embeddings",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Generate Text Embeddings",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Llama Vision Analysis": {
      "main": [
        [
          {
            "node": "Save PDF to Database",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Generate Text Embeddings": {
      "main": [
        [
          {
            "node": "Save PDF to Database",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Save PDF to Database": {
      "main": [
        [
          {
            "node": "Store in ChromaDB",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Store in ChromaDB": {
      "main": [
        [
          {
            "node": "Save Cover Image",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Save Cover Image": {
      "main": [
        [
          {
            "node": "Extract Philippine Locations",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Extract Philippine Locations": {
      "main": [
        [
          {
            "node": "Save Extracted Locations",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Save Extracted Locations": {
      "main": [
        [
          {
            "node": "Upload Success Response",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Chat Query Webhook": {
      "main": [
        [
          {
            "node": "Authenticate User Session",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Authenticate User Session": {
      "main": [
        [
          {
            "node": "Authentication Check",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Authentication Check": {
      "main": [
        [
          {
            "node": "Session Management",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Authentication Error",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Session Management": {
      "main": [
        [
          {
            "node": "Vector Similarity Search",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Vector Similarity Search": {
      "main": [
        [
          {
            "node": "Anti-Hallucination Check",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Anti-Hallucination Check": {
      "main": [
        [
          {
            "node": "LLM Response Generation",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "LLM Response Generation",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "LLM Response Generation": {
      "main": [
        [
          {
            "node": "Generate Follow-up Questions",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Generate Follow-up Questions": {
      "main": [
        [
          {
            "node": "Save Chat History",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Save Chat History": {
      "main": [
        [
          {
            "node": "Get User Geolocation",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Get User Geolocation": {
      "main": [
        [
          {
            "node": "Save Analytics Data",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Save Analytics Data": {
      "main": [
        [
          {
            "node": "Chat Response",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "User Management Webhook": {
      "main": [
        [
          {
            "node": "User Action Switch",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "User Action Switch": {
      "main": [
        [
          {
            "node": "Validate User Registration",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Authenticate Login",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Validate User Registration": {
      "main": [
        [
          {
            "node": "Check User Exists",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Check User Exists": {
      "main": [
        [
          {
            "node": "Hash Password & Generate Token",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Hash Password & Generate Token": {
      "main": [
        [
          {
            "node": "Create User Record",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Create User Record": {
      "main": [
        [
          {
            "node": "Log User Activity",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Log User Activity": {
      "main": [
        [
          {
            "node": "User Registration Success",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Authenticate Login": {
      "main": [
        [
          {
            "node": "Generate Session Token",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Generate Session Token": {
      "main": [
        [
          {
            "node": "Update User Session",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Update User Session": {
      "main": [
        [
          {
            "node": "Login Success Response",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Document Deletion Webhook": {
      "main": [
        [
          {
            "node": "Get Document Info",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Get Document Info": {
      "main": [
        [
          {
            "node": "Delete Associated Locations",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Delete Associated Locations": {
      "main": [
        [
          {
            "node": "Delete Cover Images",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Delete Cover Images": {
      "main": [
        [
          {
            "node": "Delete Vector Embeddings",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Delete Vector Embeddings": {
      "main": [
        [
          {
            "node": "Cleanup Temporary Files",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Cleanup Temporary Files": {
      "main": [
        [
          {
            "node": "Delete Document Record",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Delete Document Record": {
      "main": [
        [
          {
            "node": "Deletion Success Response",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "pinData": {},
  "versionId": "1.0.0"
}
