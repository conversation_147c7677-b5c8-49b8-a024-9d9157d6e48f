"""
Admin Blueprint

Handles administrative dashboard routes including:
- Admin dashboard
- Configuration management
- Model settings
- System utilities
- Demo pages

This blueprint contains routes that require admin authentication
and provide administrative functionality.
"""

import os
import logging
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
import utils
import db_utils
from blueprints.auth import admin_required, function_permission_required
from greeting_manager import GreetingManager

# Configure logging
logger = logging.getLogger(__name__)

# Create blueprint
admin_bp = Blueprint('admin', __name__, url_prefix='/admin')

@admin_bp.route('/', methods=['GET'])
def admin_redirect():
    """
    Redirect /admin to /admin/dashboard.
    
    Returns:
        Redirect response to admin dashboard
    """
    return redirect(url_for('admin.admin_dashboard'))

@admin_bp.route('/dashboard', methods=['GET'])
def admin_dashboard():
    """
    Main administrative dashboard interface.
    
    Displays system overview, available categories, and admin functions.
    This route handles its own authentication to allow login form display.
    
    Returns:
        Rendered admin dashboard template
    """
    try:
        categories = sorted(utils.list_categories())
        # Store categories for use in user management
        from flask import current_app
        current_app.config['CATEGORIES'] = categories
        
        return render_template('admin_dashboard.html', categories=categories)
        
    except Exception as e:
        logger.error(f"Error loading admin dashboard: {str(e)}")
        flash('Error loading dashboard. Please try again.', 'error')
        return render_template('admin_dashboard.html', categories=[])

@admin_bp.route('/darkpan_demo', methods=['GET'])
@admin_required
def darkpan_demo():
    """
    Demo page for DarkPan Bootstrap 5 Admin Dashboard Template.
    
    Displays template components and styling examples.
    
    Returns:
        Rendered demo template
    """
    return render_template('darkpan_demo.html')

@admin_bp.route('/greeting_management', methods=['GET'])
@admin_required
@function_permission_required('greeting_management')
def greeting_management():
    """
    Greeting management interface.
    
    Allows administrators to manage greeting templates and settings.
    
    Returns:
        Rendered greeting management template
    """
    try:
        greeting_manager = GreetingManager()
        templates = greeting_manager.get_greeting_templates()

        # Group templates by type for easier display
        grouped_templates = {
            'welcome': [],
            'response': [],
            'return_user': [],
            'time_based': []
        }

        for template in templates:
            template_type = template.get('template_type', 'response')
            if template_type in grouped_templates:
                grouped_templates[template_type].append(template)

        return render_template('greeting_management.html', 
                             grouped_templates=grouped_templates,
                             templates=templates)
                             
    except Exception as e:
        logger.error(f"Error loading greeting management: {str(e)}")
        flash('Error loading greeting management. Please try again.', 'error')
        return render_template('greeting_management.html', 
                             grouped_templates={}, templates=[])

@admin_bp.route('/unified_config', methods=['GET', 'POST'])
@admin_required
@function_permission_required('model_settings')
def unified_config():
    """
    Unified configuration interface for models and settings.
    
    Handles both GET requests to display the configuration page
    and POST requests to update configuration settings.
    
    Returns:
        Rendered configuration template or redirect after POST
    """
    if request.method == 'GET':
        try:
            # Import configuration utilities
            from get_vector_db import OLLAMA_BASE_URL
            import ollama
            import json
            
            # Get available models
            try:
                models_response = ollama.list()
                available_models = [model['name'] for model in models_response.get('models', [])]
            except Exception as e:
                logger.warning(f"Could not fetch models from Ollama: {str(e)}")
                available_models = []

            # Load current default models
            default_models_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'default_models.json')
            current_defaults = {}
            
            if os.path.exists(default_models_file):
                try:
                    with open(default_models_file, 'r') as f:
                        current_defaults = json.load(f)
                except Exception as e:
                    logger.warning(f"Could not load default models file: {str(e)}")

            return render_template('unified_config.html',
                                 available_models=available_models,
                                 current_defaults=current_defaults,
                                 ollama_base_url=OLLAMA_BASE_URL)
                                 
        except Exception as e:
            logger.error(f"Error loading unified config: {str(e)}")
            flash('Error loading configuration. Please try again.', 'error')
            return render_template('unified_config.html',
                                 available_models=[],
                                 current_defaults={},
                                 ollama_base_url="")
    
    # Handle POST request (configuration updates)
    elif request.method == 'POST':
        try:
            # Process configuration updates
            # This would typically involve updating the default_models.json file
            # and applying new settings
            
            flash('Configuration updated successfully.', 'success')
            return redirect(url_for('admin.unified_config'))
            
        except Exception as e:
            logger.error(f"Error updating configuration: {str(e)}")
            flash('Error updating configuration. Please try again.', 'error')
            return redirect(url_for('admin.unified_config'))

@admin_bp.route('/query_config_partial')
def query_config_partial():
    """
    Endpoint to get partial HTML for query configuration.
    
    Returns partial template for AJAX loading of query configuration
    components in the unified configuration interface.
    
    Returns:
        Rendered partial template
    """
    try:
        # Load current query configuration
        import json
        
        default_models_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'default_models.json')
        current_config = {}
        
        if os.path.exists(default_models_file):
            try:
                with open(default_models_file, 'r') as f:
                    current_config = json.load(f)
            except Exception as e:
                logger.warning(f"Could not load configuration: {str(e)}")
        
        return render_template('query_config_partial.html', config=current_config)
        
    except Exception as e:
        logger.error(f"Error loading query config partial: {str(e)}")
        return render_template('query_config_partial.html', config={})

@admin_bp.route('/embedding_config_partial')
def embedding_config_partial():
    """
    Endpoint to get partial HTML for embedding configuration.
    
    Returns partial template for AJAX loading of embedding configuration
    components in the unified configuration interface.
    
    Returns:
        Rendered partial template
    """
    try:
        # Load current embedding configuration
        import json
        
        default_models_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', 'default_models.json')
        current_config = {}
        
        if os.path.exists(default_models_file):
            try:
                with open(default_models_file, 'r') as f:
                    current_config = json.load(f)
            except Exception as e:
                logger.warning(f"Could not load configuration: {str(e)}")
        
        return render_template('embedding_config_partial.html', config=current_config)
        
    except Exception as e:
        logger.error(f"Error loading embedding config partial: {str(e)}")
        return render_template('embedding_config_partial.html', config={})

@admin_bp.route('/clean_urls', methods=['GET'])
@admin_required
def clean_urls():
    """
    Clean up malformed URLs in the database.
    
    Performs database maintenance to fix or remove malformed URLs
    and provides feedback on the cleanup results.
    
    Returns:
        Redirect to admin dashboard with status message
    """
    try:
        from db_content_utils import clean_malformed_urls
        
        results = clean_malformed_urls()
        
        if 'error' in results:
            flash(f"Error cleaning URLs: {results['error']}", "error")
        else:
            flash(f"URL cleanup complete: {results['fixed_count']} URLs fixed, "
                  f"{results['deleted_count']} URLs deleted, "
                  f"{results['total_processed']} total processed.", "success")
        
        return redirect(url_for('admin.admin_dashboard'))
        
    except Exception as e:
        logger.error(f"Error during URL cleanup: {str(e)}")
        flash('Error during URL cleanup. Please try again.', 'error')
        return redirect(url_for('admin.admin_dashboard'))

@admin_bp.route('/models', methods=['GET', 'POST'])
def manage_models():
    """
    Legacy model management route.
    
    Redirects to the unified configuration page with models anchor.
    This maintains backward compatibility for existing links.
    
    Returns:
        Redirect to unified configuration
    """
    if request.method == 'GET':
        flash("AI Models configuration has been moved to the Model Settings page.", "info")
        return redirect(url_for('admin.unified_config', _anchor='models'))
    
    # Handle POST requests by redirecting to unified config
    return redirect(url_for('admin.unified_config'))
