/**
 * Location Map JavaScript Module
 *
 * Provides interactive mapping functionality for displaying extracted geographical locations
 * from PDF documents and chat conversations using Leaflet.js with OpenStreetMap tiles.
 *
 * Features:
 * - Interactive map with clustered markers
 * - Category-based filtering
 * - Location details sidebar
 * - ERDB brand color scheme
 * - Dark mode support
 * - Responsive design
 */

const LocationMap = {
    map: null,
    markerClusterGroup: null,
    allLocations: [],
    filteredLocations: [],
    currentCategory: '',
    selectedLocations: new Set(),
    bulkSelectionMode: false,

    // ERDB Brand Colors
    colors: {
        pdf: '#378C47',      // Dark green for PDF sources
        chat: '#0267B6',     // Dark blue for chat sources
        url: '#3CA6D6',      // Light blue for URL sources
        default: '#5BA85B'   // Light green default
    },

    /**
     * Initialize the location map
     * @param {string} containerId - ID of the map container element
     * @param {Array} locations - Array of location objects
     * @param {Object} options - Configuration options
     */
    init: function(containerId, locations, options = {}) {
        this.allLocations = locations || [];
        this.filteredLocations = [...this.allLocations];

        // Initialize the map
        this.map = L.map(containerId, {
            center: [14.1648, 121.2413], // Los Baños coordinates as default
            zoom: 2,
            zoomControl: true,
            attributionControl: true
        });

        // Add OpenStreetMap tiles
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
            maxZoom: 18
        }).addTo(this.map);

        // Initialize marker cluster group if clustering is enabled
        if (options.enableClustering !== false) {
            this.markerClusterGroup = L.markerClusterGroup({
                chunkedLoading: true,
                spiderfyOnMaxZoom: true,
                showCoverageOnHover: false,
                zoomToBoundsOnClick: true,
                maxClusterRadius: 50
            });
            this.map.addLayer(this.markerClusterGroup);
        }

        // Add locations to map
        this.addLocationsToMap();

        // Set up sidebar interactions
        this.setupSidebarInteractions();

        // Fit map to show all markers if locations exist
        if (this.filteredLocations.length > 0) {
            this.fitMapToLocations();
        }
    },

    /**
     * Add location markers to the map
     */
    addLocationsToMap: function() {
        if (this.markerClusterGroup) {
            this.markerClusterGroup.clearLayers();
        }

        this.filteredLocations.forEach(location => {
            if (location.latitude && location.longitude) {
                const marker = this.createLocationMarker(location);

                if (this.markerClusterGroup) {
                    this.markerClusterGroup.addLayer(marker);
                } else {
                    marker.addTo(this.map);
                }
            }
        });
    },

    /**
     * Create a marker for a location
     * @param {Object} location - Location object
     * @returns {L.Marker} Leaflet marker
     */
    createLocationMarker: function(location) {
        // Determine marker color based on source type
        let color = this.colors.default;
        if (location.source_type === 'pdf_document') {
            color = this.colors.pdf;
        } else if (location.source_type === 'chat_message') {
            color = this.colors.chat;
        } else if (location.source_type === 'url_content') {
            color = this.colors.url;
        }

        // Create custom icon
        const icon = L.divIcon({
            className: 'custom-marker',
            html: `<div class="custom-marker" style="background-color: ${color};">
                     <i class="fas fa-map-pin" style="font-size: 14px;"></i>
                   </div>`,
            iconSize: [30, 30],
            iconAnchor: [15, 15],
            popupAnchor: [0, -15]
        });

        // Create marker
        const marker = L.marker([location.latitude, location.longitude], { icon: icon });

        // Create popup content
        const popupContent = this.createPopupContent(location);
        marker.bindPopup(popupContent, {
            maxWidth: 300,
            className: 'location-popup'
        });

        // Add click event to highlight in sidebar
        marker.on('click', () => {
            this.highlightLocationInSidebar(location.id);
        });

        return marker;
    },

    /**
     * Create popup content for a location marker
     * @param {Object} location - Location object
     * @returns {string} HTML content for popup
     */
    createPopupContent: function(location) {
        let content = `
            <div class="location-popup-content">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <h6 class="fw-bold mb-0">${location.location_text}</h6>
                    <button class="btn btn-sm btn-outline-danger ms-2" onclick="LocationMap.deleteLocation(${location.id})" title="Delete Location">
                        <i class="fas fa-trash-alt"></i>
                    </button>
                </div>
                <div class="mb-2">
                    <span class="badge bg-primary">${location.location_type.replace('_', ' ')}</span>
                    <span class="badge bg-success ms-1">${Math.round(location.confidence_score * 100)}% confidence</span>
                </div>
        `;

        if (location.geocoded_address) {
            content += `<p class="mb-2 text-muted small"><i class="fas fa-map-marker-alt me-1"></i>${location.geocoded_address}</p>`;
        }

        if (location.source_filename) {
            content += `
                <div class="mb-2">
                    <small class="text-muted">
                        <i class="fas fa-file-pdf me-1"></i>
                        ${location.source_filename}
                        ${location.page_number ? `(Page ${location.page_number})` : ''}
                    </small>
                </div>
            `;
        }

        if (location.source_category) {
            content += `<div class="mb-2"><span class="badge bg-light text-dark">${location.source_category}</span></div>`;
        }

        if (location.context_snippet) {
            content += `
                <details class="mt-2">
                    <summary class="text-muted small" style="cursor: pointer;">Context</summary>
                    <p class="small mt-1 mb-0 text-muted">${location.context_snippet}</p>
                </details>
            `;
        }

        content += '</div>';
        return content;
    },

    /**
     * Set up sidebar interactions
     */
    setupSidebarInteractions: function() {
        const sidebar = document.getElementById('locationSidebar');
        if (!sidebar) return;

        const locationItems = sidebar.querySelectorAll('.location-item');
        locationItems.forEach(item => {
            item.addEventListener('click', () => {
                const lat = parseFloat(item.dataset.lat);
                const lng = parseFloat(item.dataset.lng);
                const locationId = item.dataset.locationId;

                if (lat && lng) {
                    // Pan to location on map
                    this.map.setView([lat, lng], 12);

                    // Find and open the corresponding marker popup
                    this.openMarkerPopup(lat, lng);

                    // Highlight this item in sidebar
                    this.highlightLocationInSidebar(locationId);
                }
            });
        });
    },

    /**
     * Highlight a location item in the sidebar
     * @param {string} locationId - ID of the location to highlight
     */
    highlightLocationInSidebar: function(locationId) {
        const sidebar = document.getElementById('locationSidebar');
        if (!sidebar) return;

        // Remove existing highlights
        sidebar.querySelectorAll('.location-item.active').forEach(item => {
            item.classList.remove('active');
        });

        // Add highlight to selected item
        const targetItem = sidebar.querySelector(`[data-location-id="${locationId}"]`);
        if (targetItem) {
            targetItem.classList.add('active');
            targetItem.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
    },

    /**
     * Open marker popup at specific coordinates
     * @param {number} lat - Latitude
     * @param {number} lng - Longitude
     */
    openMarkerPopup: function(lat, lng) {
        if (!this.markerClusterGroup) return;

        this.markerClusterGroup.eachLayer(marker => {
            const markerLatLng = marker.getLatLng();
            if (Math.abs(markerLatLng.lat - lat) < 0.0001 && Math.abs(markerLatLng.lng - lng) < 0.0001) {
                marker.openPopup();
                return false; // Break the loop
            }
        });
    },

    /**
     * Filter locations by category
     * @param {string} category - Category to filter by (empty string for all)
     */
    filterByCategory: function(category) {
        console.log('Filtering by category:', category);
        this.currentCategory = category;

        if (category && category.trim() !== '') {
            this.filteredLocations = this.allLocations.filter(location => {
                // Check multiple possible category field names for compatibility
                const locationCategory = location.source_category || location.category || location.document_category;
                console.log('Location category:', locationCategory, 'Filter category:', category);
                return locationCategory === category;
            });
        } else {
            this.filteredLocations = [...this.allLocations];
        }

        console.log('Filtered locations count:', this.filteredLocations.length);

        // Clear existing markers
        if (this.markerClusterGroup) {
            this.markerClusterGroup.clearLayers();
        }

        // Update map markers
        this.addLocationsToMap();

        // Update sidebar
        this.updateSidebar();

        // Update location count
        this.updateLocationCount();

        // Fit map to filtered locations
        if (this.filteredLocations.length > 0) {
            this.fitMapToLocations();
        } else {
            // If no locations, show default view
            this.map.setView([14.1648, 121.2413], 6);
        }
    },

    /**
     * Update the sidebar to show filtered locations
     */
    updateSidebar: function() {
        const sidebar = document.getElementById('locationSidebar');
        if (!sidebar) return;

        // Clear existing content
        sidebar.innerHTML = '';

        // Create header
        const header = document.createElement('div');
        header.className = 'sidebar-header p-3 border-bottom';
        header.innerHTML = `
            <h5 class="mb-0">Locations</h5>
            <small class="text-muted">${this.filteredLocations.length} location(s)</small>
        `;
        sidebar.appendChild(header);

        // Create scrollable content area
        const content = document.createElement('div');
        content.className = 'sidebar-content';
        content.style.maxHeight = '400px';
        content.style.overflowY = 'auto';

        if (this.filteredLocations.length === 0) {
            content.innerHTML = '<div class="p-3 text-muted">No locations found for the selected category.</div>';
        } else {
            this.filteredLocations.forEach(location => {
                const item = document.createElement('div');
                item.className = 'location-item p-3 border-bottom cursor-pointer';
                item.dataset.lat = location.latitude;
                item.dataset.lng = location.longitude;
                item.dataset.locationId = location.id;

                item.innerHTML = `
                    <div class="fw-bold">${location.location_text}</div>
                    <div class="small text-muted">${location.location_type}</div>
                    ${location.source_filename ? `<div class="small text-primary">${location.source_filename}</div>` : ''}
                `;

                // Add click event
                item.addEventListener('click', () => {
                    const lat = parseFloat(location.latitude);
                    const lng = parseFloat(location.longitude);

                    if (lat && lng) {
                        this.map.setView([lat, lng], 12);
                        this.openMarkerPopup(lat, lng);
                        this.highlightLocationInSidebar(location.id);
                    }
                });

                content.appendChild(item);
            });
        }

        sidebar.appendChild(content);
    },

    /**
     * Update the location count display
     */
    updateLocationCount: function() {
        const countElement = document.getElementById('locationCount');
        if (countElement) {
            countElement.textContent = this.filteredLocations.length;
        }

        // Update any other count displays
        const totalElement = document.getElementById('totalLocations');
        if (totalElement) {
            totalElement.textContent = this.allLocations.length;
        }
    },

    /**
     * Fit the map view to show all filtered locations
     */
    fitMapToLocations: function() {
        if (this.filteredLocations.length === 0) return;

        const bounds = L.latLngBounds();
        this.filteredLocations.forEach(location => {
            if (location.latitude && location.longitude) {
                bounds.extend([location.latitude, location.longitude]);
            }
        });

        if (bounds.isValid()) {
            this.map.fitBounds(bounds, { padding: [20, 20] });
        }
    },

    /**
     * Refresh the map data
     */
    refresh: function() {
        // Show loading indicator
        const refreshBtn = document.getElementById('refreshMap');
        if (refreshBtn) {
            refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
            refreshBtn.disabled = true;
        }

        // Fetch updated location data
        fetch('/api/locations' + (this.currentCategory ? `?category=${this.currentCategory}` : ''))
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.allLocations = data.locations;
                    this.filterByCategory(this.currentCategory);

                    // Show success message
                    this.showNotification('Map data refreshed successfully', 'success');
                } else {
                    this.showNotification('Failed to refresh map data', 'error');
                }
            })
            .catch(error => {
                console.error('Error refreshing map data:', error);
                this.showNotification('Error refreshing map data', 'error');
            })
            .finally(() => {
                // Reset refresh button
                if (refreshBtn) {
                    refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i> Refresh';
                    refreshBtn.disabled = false;
                }
            });
    },

    /**
     * Show a notification message
     * @param {string} message - Message to show
     * @param {string} type - Type of notification (success, error, info)
     */
    showNotification: function(message, type = 'info') {
        // Use Toastify if available, otherwise fall back to alert
        if (typeof Toastify !== 'undefined') {
            Toastify({
                text: message,
                duration: 3000,
                gravity: 'top',
                position: 'right',
                backgroundColor: type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'
            }).showToast();
        } else {
            alert(message);
        }
    },

    /**
     * Get CSRF token from meta tag
     * @returns {string} CSRF token
     */
    getCSRFToken: function() {
        const token = document.querySelector('meta[name="csrf-token"]');
        return token ? token.getAttribute('content') : '';
    },

    /**
     * Delete a single location
     * @param {number} locationId - ID of the location to delete
     */
    deleteLocation: function(locationId) {
        const location = this.allLocations.find(loc => loc.id == locationId);
        if (!location) {
            this.showNotification('Location not found', 'error');
            return;
        }

        // Show confirmation dialog
        const confirmed = confirm(`Are you sure you want to delete the location "${location.location_text}"?\n\nThis action cannot be undone.`);
        if (!confirmed) return;

        // Get CSRF token
        const csrfToken = this.getCSRFToken();

        // Send delete request
        fetch(`/api/locations/${locationId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Remove from local arrays
                this.allLocations = this.allLocations.filter(loc => loc.id != locationId);
                this.filteredLocations = this.filteredLocations.filter(loc => loc.id != locationId);

                // Update map and sidebar
                this.addLocationsToMap();
                this.updateSidebar();
                this.updateLocationCount();

                // Remove from sidebar DOM
                const sidebarItem = document.querySelector(`[data-location-id="${locationId}"]`);
                if (sidebarItem) {
                    sidebarItem.remove();
                }

                this.showNotification('Location deleted successfully', 'success');
            } else {
                this.showNotification(data.message || 'Failed to delete location', 'error');
            }
        })
        .catch(error => {
            console.error('Error deleting location:', error);
            this.showNotification('Error deleting location', 'error');
        });
    },

    /**
     * Toggle bulk selection mode
     */
    toggleBulkSelection: function() {
        this.bulkSelectionMode = !this.bulkSelectionMode;
        this.selectedLocations.clear();

        const bulkBtn = document.getElementById('bulkSelectBtn');
        const bulkActions = document.getElementById('bulkActions');

        if (this.bulkSelectionMode) {
            bulkBtn.innerHTML = '<i class="fas fa-times"></i> Cancel Selection';
            bulkBtn.classList.remove('btn-outline-secondary');
            bulkBtn.classList.add('btn-warning');
            if (bulkActions) bulkActions.style.display = 'block';

            // Add selection checkboxes to sidebar items
            this.addSelectionCheckboxes();
        } else {
            bulkBtn.innerHTML = '<i class="fas fa-check-square"></i> Bulk Select';
            bulkBtn.classList.remove('btn-warning');
            bulkBtn.classList.add('btn-outline-secondary');
            if (bulkActions) bulkActions.style.display = 'none';

            // Remove selection checkboxes
            this.removeSelectionCheckboxes();
        }

        this.updateBulkActionButtons();
    },

    /**
     * Add selection checkboxes to sidebar items
     */
    addSelectionCheckboxes: function() {
        const locationItems = document.querySelectorAll('.location-item');
        locationItems.forEach(item => {
            if (!item.querySelector('.location-checkbox')) {
                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.className = 'form-check-input location-checkbox me-2';
                checkbox.addEventListener('change', (e) => {
                    const locationId = item.dataset.locationId;
                    if (e.target.checked) {
                        this.selectedLocations.add(locationId);
                    } else {
                        this.selectedLocations.delete(locationId);
                    }
                    this.updateBulkActionButtons();
                });

                item.insertBefore(checkbox, item.firstChild);
            }
        });
    },

    /**
     * Remove selection checkboxes from sidebar items
     */
    removeSelectionCheckboxes: function() {
        const checkboxes = document.querySelectorAll('.location-checkbox');
        checkboxes.forEach(checkbox => checkbox.remove());
    },

    /**
     * Update bulk action button states
     */
    updateBulkActionButtons: function() {
        const deleteSelectedBtn = document.getElementById('deleteSelectedBtn');
        const selectAllBtn = document.getElementById('selectAllBtn');

        if (deleteSelectedBtn) {
            deleteSelectedBtn.disabled = this.selectedLocations.size === 0;
            deleteSelectedBtn.textContent = `Delete Selected (${this.selectedLocations.size})`;
        }

        if (selectAllBtn) {
            const visibleItems = document.querySelectorAll('.location-item:not([style*="display: none"])');
            const allSelected = visibleItems.length > 0 && this.selectedLocations.size === visibleItems.length;
            selectAllBtn.textContent = allSelected ? 'Deselect All' : 'Select All';
        }
    },

    /**
     * Select or deselect all visible locations
     */
    toggleSelectAll: function() {
        const visibleItems = document.querySelectorAll('.location-item:not([style*="display: none"])');
        const allSelected = visibleItems.length > 0 && this.selectedLocations.size === visibleItems.length;

        visibleItems.forEach(item => {
            const locationId = item.dataset.locationId;
            const checkbox = item.querySelector('.location-checkbox');

            if (allSelected) {
                this.selectedLocations.delete(locationId);
                if (checkbox) checkbox.checked = false;
            } else {
                this.selectedLocations.add(locationId);
                if (checkbox) checkbox.checked = true;
            }
        });

        this.updateBulkActionButtons();
    },

    /**
     * Delete selected locations
     */
    deleteSelected: function() {
        if (this.selectedLocations.size === 0) {
            this.showNotification('No locations selected', 'error');
            return;
        }

        const confirmed = confirm(`Are you sure you want to delete ${this.selectedLocations.size} selected location(s)?\n\nThis action cannot be undone.`);
        if (!confirmed) return;

        const locationIds = Array.from(this.selectedLocations);

        // Get CSRF token
        const csrfToken = this.getCSRFToken();

        // Send bulk delete request
        fetch('/api/locations/bulk-delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken
            },
            body: JSON.stringify({ location_ids: locationIds })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Remove deleted locations from local arrays
                this.allLocations = this.allLocations.filter(loc => !locationIds.includes(loc.id.toString()));
                this.filteredLocations = this.filteredLocations.filter(loc => !locationIds.includes(loc.id.toString()));

                // Remove from sidebar DOM
                locationIds.forEach(id => {
                    const sidebarItem = document.querySelector(`[data-location-id="${id}"]`);
                    if (sidebarItem) {
                        sidebarItem.remove();
                    }
                });

                // Update map and UI
                this.addLocationsToMap();
                this.updateLocationCount();
                this.selectedLocations.clear();
                this.updateBulkActionButtons();

                this.showNotification(`${data.deleted_count} location(s) deleted successfully`, 'success');
            } else {
                this.showNotification(data.message || 'Failed to delete locations', 'error');
            }
        })
        .catch(error => {
            console.error('Error deleting locations:', error);
            this.showNotification('Error deleting locations', 'error');
        });
    }
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LocationMap;
}
