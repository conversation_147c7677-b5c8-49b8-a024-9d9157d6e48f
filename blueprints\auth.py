"""
Authentication Blueprint

Handles authentication and session management routes including:
- Admin login/logout
- Session management
- CSRF token handling
- User authentication flows

This blueprint manages all authentication-related functionality
that was previously in the main app.py file.
"""

import logging
from flask import Blueprint, request, jsonify, flash, redirect, url_for
from flask_wtf import csrf
from services import get_service

# Configure logging
logger = logging.getLogger(__name__)

# Create blueprint
auth_bp = Blueprint('auth', __name__)

# Get auth service instance
auth_service = get_service('auth')

@auth_bp.route('/admin/login', methods=['POST'])
def admin_login():
    """
    Handle admin login directly from the dashboard.

    Processes login form data and authenticates users.
    Handles password expiration and device fingerprinting.

    Returns:
        Redirect to dashboard on success or back to login on failure
    """
    try:
        username = request.form.get('username')
        password = request.form.get('password')
        remember = request.form.get('remember') == 'on'
        device_fingerprint = request.form.get('device_fingerprint')

        # Use auth service for authentication
        success, user_or_error, redirect_info = auth_service.authenticate_user(
            username=username,
            password=password,
            remember=remember,
            device_fingerprint=device_fingerprint
        )

        if success:
            # Authentication successful
            flash(redirect_info['message'], redirect_info['message_type'])
            return redirect(url_for(redirect_info['redirect']))
        else:
            # Authentication failed
            if redirect_info and 'redirect' in redirect_info:
                flash(user_or_error, redirect_info['message_type'])
                return redirect(url_for(redirect_info['redirect']))
            else:
                flash(user_or_error, 'error')
                return redirect(url_for('admin.admin_dashboard'))

    except Exception as e:
        logger.error(f"Error during admin login: {str(e)}")
        flash('An error occurred during login. Please try again.', 'error')
        return redirect(url_for('admin.admin_dashboard'))

@auth_bp.route('/clear_session', methods=['POST'])
@csrf.exempt  # Exempt from CSRF protection since this is called during page unload
def clear_session():
    """
    Clear all session data when the app is closed.

    This endpoint is called when users close the application
    to clean up session data and ensure proper logout.

    Returns:
        JSON response indicating success
    """
    try:
        # Use auth service for logout
        success, message = auth_service.logout_user()

        return jsonify({
            "success": success,
            "message": message
        }), 200 if success else 500

    except Exception as e:
        logger.error(f"Error clearing session: {str(e)}")
        return jsonify({
            "success": False,
            "error": "Failed to clear session"
        }), 500

@auth_bp.route('/api/csrf-token', methods=['GET'])
def get_csrf_token():
    """
    Get a fresh CSRF token.

    This endpoint provides CSRF tokens for AJAX requests
    and handles token refresh when tokens expire.

    Returns:
        JSON response with CSRF token
    """
    try:
        # Use auth service for CSRF token generation
        success, token_or_error = auth_service.generate_csrf_token()

        if success:
            return jsonify({
                "success": True,
                "csrf_token": token_or_error
            }), 200
        else:
            return jsonify({
                "success": False,
                "error": token_or_error
            }), 500

    except Exception as e:
        logger.error(f"Error generating CSRF token: {str(e)}")
        return jsonify({
            "success": False,
            "error": "Failed to generate CSRF token"
        }), 500

@auth_bp.route('/admin/session/<session_id>/close', methods=['POST'])
def close_session_route(session_id):
    """
    Close a session by updating its end time.

    This endpoint allows administrators to manually close
    user sessions and update session records.

    Args:
        session_id: The session ID to close

    Returns:
        JSON response indicating success or failure
    """
    try:
        # Use auth service for session closing
        success, message = auth_service.close_session(session_id)

        return jsonify({
            "success": success,
            "message": message if success else message
        }), 200 if success else 500

    except Exception as e:
        logger.error(f"Error closing session {session_id}: {str(e)}")
        return jsonify({
            "success": False,
            "error": f"Error closing session: {str(e)}"
        }), 500

# Authentication decorators (moved from app.py)
from functools import wraps

def admin_required(f):
    """
    Decorator to require admin authentication for routes.

    This decorator checks if a user is authenticated and has
    appropriate permissions to access admin functionality.

    Args:
        f: Function to wrap

    Returns:
        Wrapped function with authentication check
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Use auth service for permission checking
        has_permission, error_info = auth_service.check_admin_permission(request.endpoint)

        if not has_permission:
            # Check if this is an API request
            if request.path.startswith('/api/') or request.is_json or 'application/json' in request.headers.get('Accept', ''):
                return jsonify({
                    "error": error_info['error'],
                    "redirect": url_for(error_info['redirect'])
                }), 401
            else:
                flash(error_info['message'], error_info['message_type'])
                return redirect(url_for(error_info['redirect']))

        return f(*args, **kwargs)
    return decorated_function

def function_permission_required(function_name):
    """
    Decorator to require specific dashboard function permission.

    This decorator checks if the authenticated user has permission
    to access specific dashboard functions.

    Args:
        function_name: Name of the function to check permission for

    Returns:
        Decorator function
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Use auth service for function permission checking
            has_permission, error_info = auth_service.check_function_permission(
                function_name, request.endpoint
            )

            if not has_permission:
                # Check if this is an API request
                if request.path.startswith('/api/') or request.is_json or 'application/json' in request.headers.get('Accept', ''):
                    return jsonify({
                        "error": error_info['error'],
                        "redirect": url_for(error_info['redirect'])
                    }), 403
                else:
                    flash(error_info['message'], error_info['message_type'])
                    return redirect(url_for(error_info['redirect']))

            return f(*args, **kwargs)
        return decorated_function
    return decorator
