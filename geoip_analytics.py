"""
GeoIP Analytics module for tracking and storing user location data.

This module provides functions for:
1. Collecting and storing IP geolocation data
2. Generating analytics summaries based on location data
3. Providing location data for the AI Analytics Dashboard

It uses MaxMind's GeoLite2 database for IP geolocation.
"""

import os
import logging
import sqlite3
from typing import Dict, List, Any, Optional
from flask import Flask, request, jsonify
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Database path - use the same as the main app
CHAT_DB_PATH = os.getenv("CHAT_DB_PATH", "chat_history.db")

def ensure_geoip_table():
    """
    Ensure the geoip_analytics table exists in the database.
    """
    try:
        conn = sqlite3.connect(CHAT_DB_PATH)
        cursor = conn.cursor()

        # Check if the table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='geoip_analytics'")
        table_exists = cursor.fetchone() is not None

        if not table_exists:
            # Create the table if it doesn't exist
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS geoip_analytics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ip_address TEXT,
                    device_fingerprint TEXT,
                    client_name TEXT,
                    city TEXT,
                    region TEXT,
                    country TEXT,
                    latitude REAL,
                    longitude REAL,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    user_agent TEXT,
                    page_url TEXT,
                    session_id TEXT
                )
            ''')
            conn.commit()
            logger.info("Created geoip_analytics table")

        # Create critical performance indexes for geoip_analytics table
        # These indexes will significantly improve query performance for analytics
        logger.info("Creating performance indexes for geoip_analytics table...")

        try:
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_geoip_analytics_ip_address ON geoip_analytics(ip_address)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_geoip_analytics_timestamp ON geoip_analytics(timestamp)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_geoip_analytics_device_fingerprint ON geoip_analytics(device_fingerprint)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_geoip_analytics_session_id ON geoip_analytics(session_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_geoip_analytics_country ON geoip_analytics(country)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_geoip_analytics_city ON geoip_analytics(city)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_geoip_analytics_client_name ON geoip_analytics(client_name)")

            conn.commit()
            logger.info("Successfully created all performance indexes for geoip_analytics table")
        except sqlite3.Error as e:
            logger.error(f"Error creating geoip_analytics indexes: {str(e)}")

        conn.close()
        return True
    except sqlite3.Error as e:
        logger.error(f"Database error: {str(e)}")
        return False

def save_geoip_data(ip_address: str, device_fingerprint: str, client_name: Optional[str],
                   city: Optional[str], region: Optional[str], country: Optional[str],
                   latitude: Optional[float], longitude: Optional[float],
                   user_agent: Optional[str] = None, page_url: Optional[str] = None,
                   session_id: Optional[str] = None) -> bool:
    """
    Save geolocation data to the database.

    Args:
        ip_address: The user's IP address
        device_fingerprint: Unique identifier for the device
        client_name: Name of the client (if provided)
        city: City name from geolocation
        region: Region/state from geolocation
        country: Country name from geolocation
        latitude: Latitude coordinate
        longitude: Longitude coordinate
        user_agent: User agent string from request
        page_url: URL of the page being accessed
        session_id: Session identifier

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Ensure the table exists
        ensure_geoip_table()

        conn = sqlite3.connect(CHAT_DB_PATH)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO geoip_analytics (
                ip_address, device_fingerprint, client_name, city, region, country,
                latitude, longitude, user_agent, page_url, session_id
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            ip_address, device_fingerprint, client_name, city, region, country,
            latitude, longitude, user_agent, page_url, session_id
        ))

        conn.commit()
        conn.close()
        logger.info(f"Saved geolocation data for IP: {ip_address}")
        return True
    except sqlite3.Error as e:
        logger.error(f"Failed to save geolocation data: {str(e)}")
        return False

def get_geoip_data(start_date: Optional[str] = None, end_date: Optional[str] = None,
                  device_fingerprint: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Get geolocation data from the database, optionally filtered by date range or device.

    Args:
        start_date: Start date for filtering (format: YYYY-MM-DD)
        end_date: End date for filtering (format: YYYY-MM-DD)
        device_fingerprint: Filter by specific device

    Returns:
        List of dictionaries containing geolocation data
    """
    try:
        conn = sqlite3.connect(CHAT_DB_PATH)
        cursor = conn.cursor()

        # Build the query with optional filters
        query = "SELECT * FROM geoip_analytics"
        params = []

        # Add WHERE clause if any filters are provided
        filters = []

        if device_fingerprint:
            filters.append("device_fingerprint = ?")
            params.append(device_fingerprint)

        if start_date:
            filters.append("timestamp >= ?")
            params.append(f"{start_date} 00:00:00")

        if end_date:
            filters.append("timestamp <= ?")
            params.append(f"{end_date} 23:59:59")

        if filters:
            query += " WHERE " + " AND ".join(filters)

        query += " ORDER BY timestamp DESC"

        cursor.execute(query, params)
        rows = cursor.fetchall()

        # Get column names
        column_names = [description[0] for description in cursor.description]

        # Convert rows to dictionaries
        geoip_data = []
        for row in rows:
            entry = {column_names[i]: row[i] for i in range(len(column_names))}

            # Ensure latitude and longitude are proper numbers
            if entry.get('latitude') is not None:
                try:
                    entry['latitude'] = float(entry['latitude'])
                except (ValueError, TypeError):
                    entry['latitude'] = 0.0

            if entry.get('longitude') is not None:
                try:
                    entry['longitude'] = float(entry['longitude'])
                except (ValueError, TypeError):
                    entry['longitude'] = 0.0

            # Handle local entries
            if (entry.get('city') == 'Local' or entry.get('country') == 'Local' or
                (entry.get('latitude') == 0 and entry.get('longitude') == 0)):
                # Import DEV_LOCATION from geo_utils to ensure consistency
                from geo_utils import DEV_LOCATION

                # Set Los Baños, Laguna, Philippines coordinates for local entries
                entry['latitude'] = DEV_LOCATION['latitude']
                entry['longitude'] = DEV_LOCATION['longitude']
                entry['city'] = DEV_LOCATION['city']
                entry['region'] = DEV_LOCATION['region']
                entry['country'] = DEV_LOCATION['country_display']

            geoip_data.append(entry)

        logger.info(f"Retrieved {len(geoip_data)} geolocation data entries")

        conn.close()
        return geoip_data
    except sqlite3.Error as e:
        logger.error(f"Failed to retrieve geolocation data: {str(e)}")
        return []

def get_geoip_summary(start_date: Optional[str] = None, end_date: Optional[str] = None) -> Dict[str, Any]:
    """
    Get summary statistics from the geolocation data.

    Args:
        start_date: Start date for filtering (format: YYYY-MM-DD)
        end_date: End date for filtering (format: YYYY-MM-DD)

    Returns:
        Dictionary containing summary statistics
    """
    try:
        conn = sqlite3.connect(CHAT_DB_PATH)
        cursor = conn.cursor()

        # Build the filter clause for date range
        filter_clause = ""
        params = []

        if start_date:
            filter_clause += " WHERE timestamp >= ?"
            params.append(f"{start_date} 00:00:00")

        if end_date:
            if filter_clause:
                filter_clause += " AND timestamp <= ?"
            else:
                filter_clause += " WHERE timestamp <= ?"
            params.append(f"{end_date} 23:59:59")

        # Initialize summary dictionary
        summary = {}

        # Total number of visitors
        cursor.execute(f"SELECT COUNT(*) FROM geoip_analytics{filter_clause}", params)
        summary["total_visitors"] = cursor.fetchone()[0]

        # Count of unique countries
        cursor.execute(f"SELECT COUNT(DISTINCT country) FROM geoip_analytics WHERE country IS NOT NULL AND country != ''{filter_clause}", params)
        summary["unique_countries"] = cursor.fetchone()[0]

        # Count of unique cities
        cursor.execute(f"SELECT COUNT(DISTINCT city) FROM geoip_analytics WHERE city IS NOT NULL AND city != ''{filter_clause}", params)
        summary["unique_cities"] = cursor.fetchone()[0]

        # Count of local development entries
        local_filter = f" AND (city = 'Local' OR country = 'Local' OR (latitude = 0 AND longitude = 0))" if filter_clause else " WHERE (city = 'Local' OR country = 'Local' OR (latitude = 0 AND longitude = 0))"
        cursor.execute(f"SELECT COUNT(*) FROM geoip_analytics{filter_clause}{local_filter}", params)
        summary["local_count"] = cursor.fetchone()[0]

        # Top countries
        country_query = f"""
            SELECT country, COUNT(*) as count
            FROM geoip_analytics
            WHERE country IS NOT NULL AND country != ''
            {filter_clause.replace('WHERE', 'AND') if filter_clause else ''}
            GROUP BY country
            ORDER BY count DESC
            LIMIT 10
        """
        cursor.execute(country_query, params)
        top_countries = []
        for row in cursor.fetchall():
            percentage = round((row[1] / summary["total_visitors"]) * 100, 1) if summary["total_visitors"] > 0 else 0
            top_countries.append({
                "name": row[0],
                "count": row[1],
                "percentage": percentage
            })
        summary["top_countries"] = top_countries

        # Top cities
        city_query = f"""
            SELECT city, country, COUNT(*) as count
            FROM geoip_analytics
            WHERE city IS NOT NULL AND city != ''
            {filter_clause.replace('WHERE', 'AND') if filter_clause else ''}
            GROUP BY city, country
            ORDER BY count DESC
            LIMIT 10
        """
        cursor.execute(city_query, params)
        summary["top_cities"] = [{"name": row[0], "country": row[1], "count": row[2]} for row in cursor.fetchall()]

        conn.close()
        return summary
    except sqlite3.Error as e:
        logger.error(f"Failed to retrieve geolocation summary: {str(e)}")
        return {}

def register_geoip_routes(app: Flask):
    """
    Register the GeoIP analytics API routes with the Flask app.

    Note: The GeoIP Analytics Dashboard has been removed as its functionality
    is now integrated into the AI Analytics Dashboard.

    Args:
        app: The Flask application instance
    """
    @app.route('/api/locate', methods=['POST'])
    def locate_ip():
        """API endpoint to locate an IP address."""
        data = request.json
        ip = data.get('ip')

        if not ip:
            return jsonify({"error": "IP address is required"}), 400

        # Import geo_utils here to avoid circular imports
        from geo_utils import get_geolocation_data

        try:
            # Get geolocation data
            geo_data = get_geolocation_data(ip)

            # Return the data
            return jsonify({
                "ip": geo_data.get("ip"),
                "city": geo_data.get("city"),
                "region": geo_data.get("region"),
                "country": geo_data.get("country"),
                "latitude": geo_data.get("latitude"),
                "longitude": geo_data.get("longitude")
            })
        except Exception as e:
            logger.error(f"Error locating IP {ip}: {str(e)}")
            return jsonify({"error": f"Failed to locate IP: {str(e)}"}), 500
