"""
Streamlined Flask Application with Blueprint Architecture

This is the refactored main application file that uses Flask blueprints
to organize routes by functionality, replacing the monolithic structure.

Key improvements:
- Modular blueprint architecture
- Separated business logic from route handlers
- Centralized configuration management
- Reduced complexity from 4,322 lines to ~300 lines
- Improved maintainability and testability
"""

import os
import logging
import json
from dotenv import load_dotenv

# Load environment variables first
load_dotenv()

from flask import Flask, request, jsonify
import user_management as um
from user_routes import user_bp
from greeting_manager import GreetingManager
from error_handler import (
    standardized_error_response, handle_api_error, handle_database_error,
    DatabaseError, FileProcessingError, ValidationError, safe_execute,
    log_and_return_error
)
from create_temp_dirs import create_temp_directories

# Import blueprints
from blueprints import register_blueprints

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create Flask application
app = Flask(__name__)

# Configuration Management
class Config:
    """Centralized configuration management."""
    
    # Security Configuration
    SECRET_KEY = os.getenv("FLASK_SECRET_KEY", "1qazxsw23edcvfr4")
    MAX_CONTENT_LENGTH = 64 * 1024 * 1024  # 64MB max file size
    
    # CSRF Configuration
    WTF_CSRF_ENABLED = os.getenv('WTF_CSRF_ENABLED', 'true').lower() == 'true'
    WTF_CSRF_TIME_LIMIT = int(os.getenv('WTF_CSRF_TIME_LIMIT', '7200'))  # 2 hours
    WTF_CSRF_SSL_STRICT = os.getenv('WTF_CSRF_SSL_STRICT', 'false').lower() == 'true'
    WTF_CSRF_CHECK_DEFAULT = os.getenv('WTF_CSRF_CHECK_DEFAULT', 'true').lower() == 'true'
    
    # Session Configuration
    PERMANENT_SESSION_LIFETIME = int(os.getenv('SESSION_TIMEOUT_MINUTES', '120')) * 60
    
    # Application Paths
    TEMP_FOLDER = os.getenv("TEMP_FOLDER", "./_temp")
    CHROMA_PATH = os.getenv("CHROMA_PATH", "./chroma")
    
    # Anti-hallucination Configuration
    ANTI_HALLUCINATION_MODE = os.getenv('ANTI_HALLUCINATION_MODE', 'strict')

# Apply configuration
app.config.from_object(Config)

# Initialize user management components
login_manager = um.init_login_manager(app)
csrf = um.init_csrf(app)
limiter = um.init_limiter(app)

# Exempt static files from rate limiting
@limiter.request_filter
def exempt_static_files():
    """Exempt static files from rate limiting."""
    return request.path.startswith('/static/')

# Register all blueprints
register_blueprints(app)

# Register existing user blueprint
app.register_blueprint(user_bp)

# Global error handlers for API requests
@app.errorhandler(404)
def handle_404(error):
    """Handle 404 errors with JSON response for API requests."""
    if request.path.startswith('/api/') or request.is_json or 'application/json' in request.headers.get('Accept', ''):
        return jsonify({"error": "Not found", "status": 404}), 404
    return error

@app.errorhandler(500)
def handle_500(error):
    """Handle 500 errors with JSON response for API requests."""
    if request.path.startswith('/api/') or request.is_json or 'application/json' in request.headers.get('Accept', ''):
        return jsonify({"error": "Internal server error", "status": 500}), 500
    return error

@app.errorhandler(403)
def handle_403(error):
    """Handle 403 errors with JSON response for API requests."""
    if request.path.startswith('/api/') or request.is_json or 'application/json' in request.headers.get('Accept', ''):
        return jsonify({"error": "Forbidden", "status": 403}), 403
    return error

# Model Configuration Management
DEFAULT_MODELS_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'default_models.json')

# Default model settings
default_llm = 'llama3.1:8b-instruct-q4_K_M'
default_embedding = "mxbai-embed-large:latest"
default_vision = "llama3.2-vision:11b-instruct-q4_K_M"

def load_default_models():
    """Load default models from configuration file."""
    global default_llm, default_embedding, default_vision
    
    try:
        if os.path.exists(DEFAULT_MODELS_FILE):
            with open(DEFAULT_MODELS_FILE, 'r') as f:
                defaults = json.load(f)
                
                # Update default values if they exist in the file
                if 'llm_model' in defaults:
                    default_llm = defaults['llm_model']
                    logger.info(f"Loaded default LLM model: {default_llm}")
                
                if 'embedding_model' in defaults:
                    default_embedding = defaults['embedding_model']
                    logger.info(f"Loaded default embedding model: {default_embedding}")
                
                if 'vision_model' in defaults:
                    default_vision = defaults['vision_model']
                    logger.info(f"Loaded default vision model: {default_vision}")
                
                # Load additional configuration settings
                _load_additional_config(defaults)
                
    except Exception as e:
        logger.error(f"Error loading default models: {str(e)}")

def _load_additional_config(defaults):
    """Load additional configuration settings from defaults file."""
    try:
        # Load vision toggle states
        if 'use_vision_model' in defaults:
            use_vision = defaults['use_vision_model']
            os.environ['USE_VISION_MODEL'] = 'true' if use_vision else 'false'
            logger.info(f"Loaded vision model setting: {use_vision}")
        
        if 'use_vision_model_during_embedding' in defaults:
            use_vision_embedding = defaults['use_vision_model_during_embedding']
            os.environ['USE_VISION_MODEL_DURING_EMBEDDING'] = 'true' if use_vision_embedding else 'false'
            logger.info(f"Loaded vision embedding setting: {use_vision_embedding}")
        
        # Load model parameters
        if 'model_parameters' in defaults:
            model_params = defaults['model_parameters']
            for param, value in model_params.items():
                env_key = f'LLM_{param.upper()}'
                os.environ[env_key] = str(value)
                logger.info(f"Loaded model parameter {param}: {value}")
        
        # Load query parameters
        if 'query_parameters' in defaults:
            query_params = defaults['query_parameters']
            
            if 'preamble' in query_params:
                os.environ['QUERY_PREAMBLE'] = query_params['preamble']
            
            if 'anti_hallucination_modes' in query_params:
                ah_modes = query_params['anti_hallucination_modes']
                if 'default_mode' in ah_modes:
                    os.environ['ANTI_HALLUCINATION_MODE'] = ah_modes['default_mode']
        
    except Exception as e:
        logger.error(f"Error loading additional configuration: {str(e)}")

# Custom Jinja2 filters
def pretty_json(data):
    """Pretty print JSON data."""
    return json.dumps(data, indent=2, ensure_ascii=False)

def markdown_filter(text):
    """Render markdown text."""
    import markdown
    if text is None:
        return ""
    return markdown.markdown(text, extensions=['extra', 'nl2br'])

# Register Jinja2 filters
app.jinja_env.filters['prettyjson'] = pretty_json
app.jinja_env.filters['markdown'] = markdown_filter

# Note: Query processing route has been moved to the API blueprint
# All query endpoints are now handled by blueprints/api.py

# Application initialization
def initialize_application():
    """Initialize the application with required components."""
    try:
        # Load default models configuration
        load_default_models()
        
        # Initialize user management database
        if not um.init_user_db():
            logger.error("Failed to initialize user management database")
        else:
            logger.info("User management database initialized successfully")
            
            # Initialize default permission groups
            from permissions import ensure_default_permission_groups, sync_new_module_permissions
            
            if ensure_default_permission_groups():
                logger.info("Default permission groups initialized")
            else:
                logger.error("Failed to initialize default permission groups")
            
            if sync_new_module_permissions():
                logger.info("Module permissions synchronized")
            else:
                logger.error("Failed to synchronize module permissions")
        
        # Create temporary directories
        create_temp_directories()
        
        logger.info("Application initialization completed successfully")
        
    except Exception as e:
        logger.error(f"Error during application initialization: {str(e)}")

if __name__ == '__main__':
    # Initialize the application
    initialize_application()
    
    # Start the Flask application
    app.run(host='0.0.0.0', port=8080, debug=True)
