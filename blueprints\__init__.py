"""
Flask Blueprints Package

This package contains modular Flask blueprints that organize the application's routes
by functionality, replacing the monolithic app.py structure.

Blueprints:
- auth: Authentication and session management routes
- admin: Administrative dashboard and management routes
- api: RESTful API endpoints
- file_management: File upload, processing, and management routes
- analytics: Analytics, reporting, and monitoring routes
- core: Core application routes (index, documentation, etc.)
"""

from flask import Blueprint

# Import all blueprints
from .auth import auth_bp
from .admin import admin_bp
from .api import api_bp
from .file_management import file_management_bp
from .analytics import analytics_bp
from .core import core_bp

# List of all blueprints for easy registration
ALL_BLUEPRINTS = [
    auth_bp,
    admin_bp,
    api_bp,
    file_management_bp,
    analytics_bp,
    core_bp
]

def register_blueprints(app):
    """
    Register all blueprints with the Flask application.
    
    Args:
        app: Flask application instance
    """
    for blueprint in ALL_BLUEPRINTS:
        app.register_blueprint(blueprint)
