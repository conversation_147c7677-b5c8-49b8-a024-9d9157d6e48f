"""
Authentication Service

Handles all authentication-related business logic including:
- User authentication and validation
- Session management
- Device fingerprinting
- Password management
- CSRF token handling

This service extracts authentication business logic from route handlers
to improve testability and maintainability.
"""

import logging
import uuid
from datetime import datetime
from flask import session
import user_management as um
from services import ServiceError, AuthenticationError

# Configure logging
logger = logging.getLogger(__name__)

class AuthService:
    """Service class for authentication business logic."""
    
    def __init__(self):
        """Initialize the authentication service."""
        self.initialized = False
    
    def initialize(self):
        """Initialize the service with required dependencies."""
        try:
            # Perform any initialization tasks
            self.initialized = True
            logger.info("Authentication service initialized successfully")
            return True
        except Exception as e:
            logger.error(f"Error initializing authentication service: {str(e)}")
            return False
    
    def authenticate_user(self, username, password, remember=False, device_fingerprint=None):
        """
        Authenticate a user with username and password.
        
        Args:
            username: User's username
            password: User's password
            remember: Whether to remember the user
            device_fingerprint: Device fingerprint for tracking
            
        Returns:
            Tuple of (success, user_or_error_message, redirect_info)
        """
        try:
            # Validate input
            if not username or not password:
                return False, 'Username and password are required.', None
            
            # Authenticate user
            user, error = um.authenticate_user(username, password)
            
            if not user:
                logger.warning(f"Authentication failed for user: {username}")
                return False, error, None
            
            # Check if password is expired
            if user.password_expired:
                logger.info(f"Password expired for user: {username}")
                # Store user ID in session for password change
                session['password_expired_user_id'] = user.user_id
                return False, 'Your password has expired. Please create a new password.', {
                    'redirect': 'change_expired_password',
                    'message_type': 'warning'
                }
            
            # Log in user
            um.login_user(user, remember=remember)
            
            # Store device fingerprint if available
            if device_fingerprint:
                session['device_fingerprint'] = device_fingerprint
                logger.debug(f"Stored device fingerprint for user: {username}")
            
            logger.info(f"User authenticated successfully: {username}")
            return True, user, {
                'redirect': 'admin_dashboard',
                'message': f'Welcome, {user.username}!',
                'message_type': 'success'
            }
            
        except Exception as e:
            logger.error(f"Error during authentication: {str(e)}")
            raise AuthenticationError(f"Authentication failed: {str(e)}")
    
    def logout_user(self):
        """
        Log out the current user and clean up session.
        
        Returns:
            Tuple of (success, message)
        """
        try:
            # Get session info before clearing
            session_id = session.get('session_id')
            client_name = session.get('client_name', 'Anonymous')
            username = getattr(um.current_user, 'username', 'Unknown') if um.current_user.is_authenticated else 'Anonymous'
            
            # Log session clearing
            if session_id:
                logger.info(f"Logging out user {username}, clearing session {session_id}")
            
            # Logout user if authenticated
            if um.current_user.is_authenticated:
                um.logout_user()
            
            # Clear all session data
            session.clear()
            
            return True, "Logged out successfully"
            
        except Exception as e:
            logger.error(f"Error during logout: {str(e)}")
            return False, f"Error during logout: {str(e)}"
    
    def generate_csrf_token(self):
        """
        Generate a fresh CSRF token.
        
        Returns:
            Tuple of (success, token_or_error)
        """
        try:
            from flask_wtf import csrf
            token = csrf.generate_csrf()
            logger.debug("Generated fresh CSRF token")
            return True, token
        except Exception as e:
            logger.error(f"Error generating CSRF token: {str(e)}")
            return False, f"Failed to generate CSRF token: {str(e)}"
    
    def validate_session(self):
        """
        Validate the current session.
        
        Returns:
            Tuple of (is_valid, session_info)
        """
        try:
            session_id = session.get('session_id')
            session_start = session.get('session_start')
            device_fingerprint = session.get('device_fingerprint')
            
            if not session_id or not session_start:
                return False, None
            
            session_info = {
                'session_id': session_id,
                'session_start': session_start,
                'device_fingerprint': device_fingerprint,
                'client_name': session.get('client_name', 'Anonymous'),
                'is_authenticated': um.current_user.is_authenticated if um.current_user else False
            }
            
            return True, session_info
            
        except Exception as e:
            logger.error(f"Error validating session: {str(e)}")
            return False, None
    
    def close_session(self, session_id):
        """
        Close a specific session.
        
        Args:
            session_id: The session ID to close
            
        Returns:
            Tuple of (success, message)
        """
        try:
            import db_utils
            
            # Close the session in the database
            success = db_utils.close_session(session_id)
            
            if success:
                logger.info(f"Session {session_id} closed successfully")
                return True, "Session closed successfully"
            else:
                logger.warning(f"Failed to close session {session_id}")
                return False, "Failed to close session"
                
        except Exception as e:
            logger.error(f"Error closing session {session_id}: {str(e)}")
            return False, f"Error closing session: {str(e)}"
    
    def check_admin_permission(self, endpoint=None):
        """
        Check if current user has admin permissions.
        
        Args:
            endpoint: Optional endpoint to check specific permissions for
            
        Returns:
            Tuple of (has_permission, error_info)
        """
        try:
            # Skip authentication for the admin dashboard (which handles login)
            if endpoint == 'admin.admin_dashboard':
                return True, None
            
            # Check if user is authenticated
            if not um.current_user.is_authenticated:
                return False, {
                    'error': 'Authentication required',
                    'redirect': 'admin.admin_dashboard',
                    'message': 'Please log in to access this page.',
                    'message_type': 'error'
                }
            
            return True, None
            
        except Exception as e:
            logger.error(f"Error checking admin permission: {str(e)}")
            return False, {
                'error': 'Permission check failed',
                'message': 'Error checking permissions. Please try again.',
                'message_type': 'error'
            }
    
    def check_function_permission(self, function_name, endpoint=None):
        """
        Check if current user has permission for a specific function.
        
        Args:
            function_name: Name of the function to check permission for
            endpoint: Optional endpoint for context
            
        Returns:
            Tuple of (has_permission, error_info)
        """
        try:
            # Skip permission check for admin dashboard
            if endpoint == 'admin.admin_dashboard':
                return True, None
            
            # First check admin permission
            has_admin, admin_error = self.check_admin_permission(endpoint)
            if not has_admin:
                return False, admin_error
            
            # Check if user has permission for this function
            if not um.current_user.has_dashboard_permission(function_name) and um.current_user.role != 'admin':
                function_display = function_name.replace('_', ' ').title()
                return False, {
                    'error': f'Permission denied for {function_display}',
                    'redirect': 'admin.admin_dashboard',
                    'message': f'You do not have permission to access {function_display}.',
                    'message_type': 'error'
                }
            
            return True, None
            
        except Exception as e:
            logger.error(f"Error checking function permission: {str(e)}")
            return False, {
                'error': 'Permission check failed',
                'message': 'Error checking permissions. Please try again.',
                'message_type': 'error'
            }
    
    def get_current_user_info(self):
        """
        Get information about the current authenticated user.
        
        Returns:
            Dictionary with user information or None
        """
        try:
            if not um.current_user.is_authenticated:
                return None
            
            return {
                'user_id': um.current_user.user_id,
                'username': um.current_user.username,
                'email': um.current_user.email,
                'role': um.current_user.role,
                'full_name': getattr(um.current_user, 'full_name', ''),
                'is_authenticated': True
            }
            
        except Exception as e:
            logger.error(f"Error getting current user info: {str(e)}")
            return None
