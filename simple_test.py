#!/usr/bin/env python3
"""
Simple test to verify location extraction filtering works correctly.
"""

import re

def test_scientific_filtering():
    """Test the scientific term filtering logic."""
    print("=== Testing Scientific Term Filtering ===")

    # Scientific terms that should be filtered out
    scientific_terms = [
        "Dictyota",
        "Phaeophyceae",
        "macroalgae",
        "Sargassum",
        "Chlorophyceae",
        "Rhodophyceae",
        "Ulvaceae",
        "Gracilariaceae"
    ]

    # Valid geographical terms that should be preserved
    valid_locations = [
        "Manila",
        "Barangay San Antonio",
        "Los Baños Municipality",
        "Quezon City",
        "University of the Philippines",
        "Laguna Bay"
    ]

    # Filtering logic from location_extractor.py
    def should_filter_scientific_term(location_text):
        text_lower = location_text.lower()

        # Scientific and biological terms
        non_geographical = [
            'species', 'genus', 'family', 'order', 'class', 'phylum', 'kingdom',
            'algae', 'bacteria', 'virus', 'fungi', 'organism', 'specimen', 'sample',
            'aceae', 'idae', 'inae', 'ensis', 'oides', 'phyceae', 'mycota',
            'study', 'research', 'analysis', 'experiment', 'method', 'technique', 'protocol',
            'data', 'result', 'conclusion', 'hypothesis', 'theory', 'model'
        ]

        # Check basic keyword filtering
        if any(indicator in text_lower for indicator in non_geographical):
            return True

        # Scientific patterns
        scientific_patterns = [
            r'^[a-z]+aceae$',  # Family names ending in -aceae
            r'^[a-z]+phyceae$',  # Algae class names ending in -phyceae
            r'^[a-z]+mycota$',  # Fungal division names ending in -mycota
            r'^[A-Z][a-z]+\s+[a-z]+$',  # Binomial nomenclature (Genus species)
            r'^[a-z]+ensis$',  # Species names ending in -ensis
            r'^[a-z]+oides$',  # Names ending in -oides
        ]

        for pattern in scientific_patterns:
            if re.match(pattern, location_text.strip()):
                return True

        # Single word scientific terms
        if ' ' not in location_text.strip():
            single_word = location_text.strip().lower()

            # Common algae genera and scientific terms
            known_scientific_terms = [
                'dictyota', 'sargassum', 'ulva', 'gracilaria', 'caulerpa', 'halimeda',
                'turbinaria', 'padina', 'codium', 'enteromorpha', 'chaetomorpha',
                'cladophora', 'spirogyra', 'chondrus', 'porphyra', 'gelidium'
            ]

            if single_word in known_scientific_terms:
                return True

            if (len(single_word) > 8 and
                (single_word.endswith(('phyceae', 'mycota', 'aceae', 'ensis', 'oides')) or
                 any(term in single_word for term in ['algae', 'phyto', 'myco', 'bacteria']))):
                return True

            # Additional pattern for scientific names (capitalized single words that look scientific)
            if (location_text[0].isupper() and len(location_text) > 6 and
                not any(indicator in text_lower for indicator in ['san', 'santa', 'santo', 'mount', 'mt'])):
                # Check if it looks like a genus name (no common geographical indicators)
                if not any(geo_indicator in text_lower for geo_indicator in
                          ['city', 'town', 'municipality', 'barangay', 'island', 'river', 'lake', 'hill']):
                    return True

        return False

    print("Testing scientific terms (should be filtered):")
    all_scientific_filtered = True
    for term in scientific_terms:
        should_filter = should_filter_scientific_term(term)
        status = "✓ FILTERED" if should_filter else "✗ NOT FILTERED"
        print(f"  {term}: {status}")
        if not should_filter:
            all_scientific_filtered = False

    print("\nTesting valid locations (should NOT be filtered):")
    all_valid_preserved = True
    for location in valid_locations:
        should_filter = should_filter_scientific_term(location)
        status = "✓ PRESERVED" if not should_filter else "✗ FILTERED"
        print(f"  {location}: {status}")
        if should_filter:
            all_valid_preserved = False

    print(f"\nResults:")
    print(f"Scientific terms filtered correctly: {'✓ PASS' if all_scientific_filtered else '✗ FAIL'}")
    print(f"Valid locations preserved: {'✓ PASS' if all_valid_preserved else '✗ FAIL'}")

    return all_scientific_filtered and all_valid_preserved

def test_location_type_validation():
    """Test location type validation logic."""
    print("\n=== Testing Location Type Validation ===")

    valid_location_types = ['place_name', 'address', 'coordinates', 'landmark', 'region', 'municipality', 'city', 'barangay']

    test_cases = [
        # Valid cases
        ('Manila', 'city', True),
        ('Barangay San Antonio', 'barangay', True),
        ('Los Baños Municipality', 'municipality', True),
        ('University of the Philippines', 'landmark', True),
        ('14.1648, 121.2413', 'coordinates', True),
        ('123 Main Street', 'address', True),
        ('Laguna Province', 'region', True),
        ('Unknown Location', 'place_name', True),

        # Invalid cases
        ('Dictyota', 'species', False),
        ('Phaeophyceae', 'class', False),
        ('', 'city', False),  # Empty text
        ('A', 'city', False),  # Too short
        ('Manila', 'invalid_type', False),  # Invalid type
        ('Manila', '', False),  # Empty type
        ('Manila', None, False),  # None type
    ]

    def validate_location_data(location_text, location_type):
        # Validation logic from db_utils.py
        if not location_type or location_type not in valid_location_types:
            return False

        location_text = location_text.strip() if location_text else ''
        if not location_text or len(location_text) < 2:
            return False

        return True

    all_passed = True
    for location_text, location_type, expected_valid in test_cases:
        is_valid = validate_location_data(location_text, location_type)
        status = "✓ PASS" if is_valid == expected_valid else "✗ FAIL"
        print(f"  '{location_text}' ({location_type}): {status}")
        if is_valid != expected_valid:
            all_passed = False

    print(f"\nLocation type validation: {'✓ PASS' if all_passed else '✗ FAIL'}")
    return all_passed

def main():
    """Run all tests."""
    print("Running simplified tests for location extraction fixes...\n")

    test1_passed = test_scientific_filtering()
    test2_passed = test_location_type_validation()

    print(f"\n=== FINAL RESULTS ===")
    print(f"Scientific term filtering: {'✓ PASS' if test1_passed else '✗ FAIL'}")
    print(f"Location type validation: {'✓ PASS' if test2_passed else '✗ FAIL'}")
    print(f"Overall: {'✓ ALL TESTS PASSED' if test1_passed and test2_passed else '✗ SOME TESTS FAILED'}")

if __name__ == "__main__":
    main()
