"""
Cache Service

This service provides comprehensive caching capabilities for the Document Management System
with support for multiple cache backends, TTL management, and cache invalidation strategies.

Features:
- Redis and in-memory cache backends
- Automatic cache key generation
- TTL (Time To Live) management
- Cache invalidation patterns
- Performance monitoring
- Cache warming strategies

Version: 2.0.0
"""

import os
import json
import time
import hashlib
import logging
import threading
from typing import Any, Optional, Dict, List, Union, Callable
from datetime import datetime, timedelta
from functools import wraps
from dataclasses import dataclass, asdict

try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False

logger = logging.getLogger(__name__)

@dataclass
class CacheStats:
    """Cache statistics tracking"""
    hits: int = 0
    misses: int = 0
    sets: int = 0
    deletes: int = 0
    evictions: int = 0
    memory_usage: int = 0
    
    @property
    def hit_rate(self) -> float:
        """Calculate cache hit rate"""
        total = self.hits + self.misses
        return (self.hits / total * 100) if total > 0 else 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return asdict(self)

class InMemoryCache:
    """In-memory cache implementation with TTL support"""
    
    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self._cache: Dict[str, Dict[str, Any]] = {}
        self._access_times: Dict[str, float] = {}
        self._lock = threading.RLock()
        self.stats = CacheStats()
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        with self._lock:
            if key not in self._cache:
                self.stats.misses += 1
                return None
            
            entry = self._cache[key]
            
            # Check TTL
            if entry.get('expires_at') and time.time() > entry['expires_at']:
                self._delete_key(key)
                self.stats.misses += 1
                return None
            
            # Update access time
            self._access_times[key] = time.time()
            self.stats.hits += 1
            
            return entry['value']
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in cache"""
        with self._lock:
            # Calculate expiration time
            expires_at = None
            if ttl:
                expires_at = time.time() + ttl
            
            # Check if we need to evict items
            if len(self._cache) >= self.max_size and key not in self._cache:
                self._evict_lru()
            
            # Store the value
            self._cache[key] = {
                'value': value,
                'expires_at': expires_at,
                'created_at': time.time()
            }
            self._access_times[key] = time.time()
            self.stats.sets += 1
            
            return True
    
    def delete(self, key: str) -> bool:
        """Delete key from cache"""
        with self._lock:
            if key in self._cache:
                self._delete_key(key)
                self.stats.deletes += 1
                return True
            return False
    
    def clear(self) -> None:
        """Clear all cache entries"""
        with self._lock:
            self._cache.clear()
            self._access_times.clear()
    
    def _delete_key(self, key: str) -> None:
        """Internal method to delete a key"""
        self._cache.pop(key, None)
        self._access_times.pop(key, None)
    
    def _evict_lru(self) -> None:
        """Evict least recently used item"""
        if not self._access_times:
            return
        
        # Find the least recently used key
        lru_key = min(self._access_times.keys(), key=lambda k: self._access_times[k])
        self._delete_key(lru_key)
        self.stats.evictions += 1
    
    def get_stats(self) -> CacheStats:
        """Get cache statistics"""
        with self._lock:
            self.stats.memory_usage = len(self._cache)
            return self.stats

class RedisCache:
    """Redis cache implementation"""
    
    def __init__(self, host: str = 'localhost', port: int = 6379, db: int = 0, 
                 password: Optional[str] = None, prefix: str = 'dms:'):
        if not REDIS_AVAILABLE:
            raise ImportError("Redis library not available. Install with: pip install redis")
        
        self.prefix = prefix
        self.stats = CacheStats()
        
        try:
            self.client = redis.Redis(
                host=host,
                port=port,
                db=db,
                password=password,
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5
            )
            
            # Test connection
            self.client.ping()
            logger.info(f"Connected to Redis at {host}:{port}")
            
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            raise
    
    def _make_key(self, key: str) -> str:
        """Create prefixed cache key"""
        return f"{self.prefix}{key}"
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from Redis cache"""
        try:
            redis_key = self._make_key(key)
            value = self.client.get(redis_key)
            
            if value is None:
                self.stats.misses += 1
                return None
            
            self.stats.hits += 1
            return json.loads(value)
            
        except Exception as e:
            logger.error(f"Redis get error for key {key}: {e}")
            self.stats.misses += 1
            return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in Redis cache"""
        try:
            redis_key = self._make_key(key)
            serialized_value = json.dumps(value, default=str)
            
            if ttl:
                result = self.client.setex(redis_key, ttl, serialized_value)
            else:
                result = self.client.set(redis_key, serialized_value)
            
            if result:
                self.stats.sets += 1
                return True
            return False
            
        except Exception as e:
            logger.error(f"Redis set error for key {key}: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """Delete key from Redis cache"""
        try:
            redis_key = self._make_key(key)
            result = self.client.delete(redis_key)
            
            if result > 0:
                self.stats.deletes += 1
                return True
            return False
            
        except Exception as e:
            logger.error(f"Redis delete error for key {key}: {e}")
            return False
    
    def clear(self) -> None:
        """Clear all cache entries with prefix"""
        try:
            pattern = f"{self.prefix}*"
            keys = self.client.keys(pattern)
            if keys:
                self.client.delete(*keys)
        except Exception as e:
            logger.error(f"Redis clear error: {e}")
    
    def get_stats(self) -> CacheStats:
        """Get cache statistics"""
        try:
            info = self.client.info('memory')
            self.stats.memory_usage = info.get('used_memory', 0)
        except Exception as e:
            logger.error(f"Error getting Redis stats: {e}")
        
        return self.stats

class CacheService:
    """Main cache service with multiple backend support"""
    
    def __init__(self, backend: str = 'memory', **kwargs):
        self.backend_name = backend
        self.default_ttl = kwargs.get('default_ttl', 3600)  # 1 hour default
        
        # Initialize cache backend
        if backend == 'redis' and REDIS_AVAILABLE:
            try:
                self.backend = RedisCache(**kwargs)
                logger.info("Using Redis cache backend")
            except Exception as e:
                logger.warning(f"Redis initialization failed, falling back to memory: {e}")
                self.backend = InMemoryCache(kwargs.get('max_size', 1000))
                self.backend_name = 'memory'
        else:
            self.backend = InMemoryCache(kwargs.get('max_size', 1000))
            self.backend_name = 'memory'
            if backend == 'redis':
                logger.warning("Redis not available, using in-memory cache")
    
    def generate_key(self, *args, **kwargs) -> str:
        """Generate cache key from arguments"""
        # Create a deterministic key from arguments
        key_data = {
            'args': args,
            'kwargs': sorted(kwargs.items()) if kwargs else {}
        }
        
        key_string = json.dumps(key_data, sort_keys=True, default=str)
        key_hash = hashlib.md5(key_string.encode()).hexdigest()
        
        return key_hash
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        return self.backend.get(key)
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in cache"""
        if ttl is None:
            ttl = self.default_ttl
        return self.backend.set(key, value, ttl)
    
    def delete(self, key: str) -> bool:
        """Delete key from cache"""
        return self.backend.delete(key)
    
    def clear(self) -> None:
        """Clear all cache entries"""
        self.backend.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive cache statistics"""
        stats = self.backend.get_stats()
        return {
            'backend': self.backend_name,
            'stats': stats.to_dict(),
            'hit_rate': f"{stats.hit_rate:.2f}%",
            'total_operations': stats.hits + stats.misses + stats.sets + stats.deletes
        }
    
    def cached(self, ttl: Optional[int] = None, key_func: Optional[Callable] = None):
        """Decorator for caching function results"""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                # Generate cache key
                if key_func:
                    cache_key = key_func(*args, **kwargs)
                else:
                    cache_key = f"{func.__name__}:{self.generate_key(*args, **kwargs)}"
                
                # Try to get from cache
                cached_result = self.get(cache_key)
                if cached_result is not None:
                    return cached_result
                
                # Execute function and cache result
                result = func(*args, **kwargs)
                self.set(cache_key, result, ttl)
                
                return result
            
            # Add cache management methods to the wrapped function
            wrapper.cache_clear = lambda: self.delete_pattern(f"{func.__name__}:*")
            wrapper.cache_info = lambda: self.get_stats()
            
            return wrapper
        return decorator
    
    def delete_pattern(self, pattern: str) -> int:
        """Delete keys matching pattern (Redis only)"""
        if hasattr(self.backend, 'client') and hasattr(self.backend.client, 'keys'):
            try:
                keys = self.backend.client.keys(f"{self.backend.prefix}{pattern}")
                if keys:
                    return self.backend.client.delete(*keys)
                return 0
            except Exception as e:
                logger.error(f"Error deleting pattern {pattern}: {e}")
                return 0
        else:
            # For in-memory cache, we'd need to implement pattern matching
            logger.warning("Pattern deletion not supported for in-memory cache")
            return 0
    
    def warm_cache(self, warm_functions: List[Callable]) -> Dict[str, Any]:
        """Warm cache with predefined functions"""
        results = {
            'warmed': 0,
            'failed': 0,
            'functions': []
        }
        
        for func in warm_functions:
            try:
                # Execute function to populate cache
                func()
                results['warmed'] += 1
                results['functions'].append({
                    'name': func.__name__,
                    'status': 'success'
                })
            except Exception as e:
                results['failed'] += 1
                results['functions'].append({
                    'name': func.__name__,
                    'status': 'failed',
                    'error': str(e)
                })
                logger.error(f"Cache warming failed for {func.__name__}: {e}")
        
        return results

# Global cache service instance
_cache_service: Optional[CacheService] = None

def get_cache_service() -> CacheService:
    """Get global cache service instance"""
    global _cache_service
    if _cache_service is None:
        # Initialize with configuration
        cache_backend = os.getenv('CACHE_BACKEND', 'memory')
        cache_config = {
            'default_ttl': int(os.getenv('CACHE_DEFAULT_TTL', '3600')),
            'max_size': int(os.getenv('CACHE_MAX_SIZE', '1000'))
        }
        
        if cache_backend == 'redis':
            cache_config.update({
                'host': os.getenv('REDIS_HOST', 'localhost'),
                'port': int(os.getenv('REDIS_PORT', '6379')),
                'db': int(os.getenv('REDIS_DB', '0')),
                'password': os.getenv('REDIS_PASSWORD'),
                'prefix': os.getenv('REDIS_PREFIX', 'dms:')
            })
        
        _cache_service = CacheService(cache_backend, **cache_config)
    
    return _cache_service

def cached(ttl: Optional[int] = None, key_func: Optional[Callable] = None):
    """Convenience decorator for caching"""
    return get_cache_service().cached(ttl, key_func)
