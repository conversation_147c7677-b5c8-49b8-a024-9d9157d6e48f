"""
API Blueprint

Handles RESTful API endpoints including:
- Query processing
- Model management
- Configuration APIs
- Greeting APIs
- Location APIs
- System utilities

This blueprint contains all API routes that return JSON responses
and provide programmatic access to system functionality.
"""

import os
import json
import logging
from flask import Blueprint, request, jsonify, session
import ollama
from langchain_ollama.embeddings import OllamaEmbeddings
from query import query_category
from blueprints.auth import admin_required, function_permission_required
from greeting_manager import GreetingManager
import db_utils
import geo_utils
import user_management as um

# Configure logging
logger = logging.getLogger(__name__)

# Create blueprint
api_bp = Blueprint('api', __name__, url_prefix='/api')

@api_bp.route('/available-models', methods=['GET'])
def get_available_models():
    """
    API endpoint to get available LLM models for chat interface.
    
    Returns list of available models from Ollama server for
    dynamic model selection in the chat interface.
    
    Returns:
        JSON response with available models
    """
    try:
        # Get models from Ollama
        models_response = ollama.list()
        models = models_response.get('models', [])
        
        # Filter and format models
        available_models = []
        for model in models:
            model_name = model.get('name', '')
            if model_name:
                available_models.append({
                    'name': model_name,
                    'size': model.get('size', 0),
                    'modified_at': model.get('modified_at', ''),
                    'digest': model.get('digest', '')
                })
        
        return jsonify({
            'success': True,
            'models': available_models,
            'count': len(available_models)
        }), 200
        
    except Exception as e:
        logger.error(f"Error fetching available models: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Error fetching models: {str(e)}',
            'models': []
        }), 500

@api_bp.route('/greeting', methods=['POST'])
def get_greeting():
    """
    Get contextual greeting for user. Enhanced for Phase 2.
    
    Provides personalized greetings based on time of day,
    user session history, and engagement patterns.
    
    Returns:
        JSON response with greeting message
    """
    try:
        data = request.get_json()
        client_name = data.get('client_name', 'User')
        device_fingerprint = data.get('device_fingerprint')
        
        # Initialize greeting manager
        greeting_manager = GreetingManager()
        
        # Get contextual greeting
        greeting = greeting_manager.get_contextual_greeting(
            client_name=client_name,
            device_fingerprint=device_fingerprint
        )
        
        return jsonify({
            'success': True,
            'greeting': greeting,
            'timestamp': greeting_manager.get_current_time_info()
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting greeting: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Failed to get greeting',
            'greeting': 'Welcome! How can I help you today?'
        }), 500

@api_bp.route('/greeting_templates', methods=['GET', 'POST'])
@admin_required
@function_permission_required('greeting_management')
def greeting_templates_api():
    """
    API endpoint for greeting templates CRUD operations.
    
    Handles GET requests to retrieve templates and POST requests
    to create new greeting templates.
    
    Returns:
        JSON response with template data or creation status
    """
    greeting_manager = GreetingManager()
    
    if request.method == 'GET':
        try:
            template_type = request.args.get('type')
            templates = greeting_manager.get_greeting_templates(template_type)
            return jsonify({"success": True, "templates": templates}), 200
        except Exception as e:
            logger.error(f"Error getting greeting templates: {str(e)}")
            return jsonify({"success": False, "error": str(e)}), 500
    
    elif request.method == 'POST':
        try:
            data = request.get_json()
            
            # Validate required fields
            required_fields = ['template_type', 'content', 'context']
            for field in required_fields:
                if field not in data:
                    return jsonify({"success": False, "error": f"Missing field: {field}"}), 400
            
            # Create template
            template_id = greeting_manager.create_greeting_template(
                template_type=data['template_type'],
                content=data['content'],
                context=data['context'],
                conditions=data.get('conditions', {}),
                priority=data.get('priority', 1),
                is_active=data.get('is_active', True)
            )
            
            return jsonify({
                "success": True,
                "template_id": template_id,
                "message": "Template created successfully"
            }), 201
            
        except Exception as e:
            logger.error(f"Error creating greeting template: {str(e)}")
            return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/greeting_templates/<int:template_id>', methods=['PUT', 'DELETE'])
@admin_required
@function_permission_required('greeting_management')
def greeting_template_api(template_id):
    """
    API endpoint for individual greeting template operations.
    
    Handles PUT requests to update templates and DELETE requests
    to remove templates.
    
    Args:
        template_id: ID of the template to modify
        
    Returns:
        JSON response with operation status
    """
    greeting_manager = GreetingManager()
    
    if request.method == 'PUT':
        try:
            data = request.get_json()
            
            # Update template
            success = greeting_manager.update_greeting_template(
                template_id=template_id,
                template_type=data.get('template_type'),
                content=data.get('content'),
                context=data.get('context'),
                conditions=data.get('conditions'),
                priority=data.get('priority'),
                is_active=data.get('is_active')
            )
            
            if success:
                return jsonify({
                    "success": True,
                    "message": "Template updated successfully"
                }), 200
            else:
                return jsonify({
                    "success": False,
                    "error": "Template not found or update failed"
                }), 404
                
        except Exception as e:
            logger.error(f"Error updating greeting template {template_id}: {str(e)}")
            return jsonify({"success": False, "error": str(e)}), 500
    
    elif request.method == 'DELETE':
        try:
            # Delete template
            success = greeting_manager.delete_greeting_template(template_id)
            
            if success:
                return jsonify({
                    "success": True,
                    "message": "Template deleted successfully"
                }), 200
            else:
                return jsonify({
                    "success": False,
                    "error": "Template not found or delete failed"
                }), 404
                
        except Exception as e:
            logger.error(f"Error deleting greeting template {template_id}: {str(e)}")
            return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/settings/vision_embedding', methods=['GET'])
def get_vision_embedding_setting():
    """
    Get the global vision model during embedding setting.
    
    Returns the current configuration for vision model usage
    during document embedding processes.
    
    Returns:
        JSON response with vision embedding setting
    """
    try:
        # Get setting from environment or default models file
        use_vision = os.getenv('USE_VISION_MODEL_DURING_EMBEDDING', 'false').lower() == 'true'
        
        return jsonify({
            'success': True,
            'use_vision_embedding': use_vision
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting vision embedding setting: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Failed to get vision embedding setting'
        }), 500

@api_bp.route('/model-performance-analysis', methods=['GET'])
@admin_required
@function_permission_required('model_performance_analysis')
def get_model_performance_analysis():
    """
    Get model performance analysis data.
    
    Provides analytics on model usage, response times,
    and performance metrics for administrative review.
    
    Returns:
        JSON response with performance analysis data
    """
    try:
        # Get date range from request
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        
        # Get performance data
        performance_data = db_utils.get_model_performance_data(
            start_date=start_date,
            end_date=end_date
        )
        
        return jsonify({
            'success': True,
            'performance_data': performance_data,
            'date_range': {
                'start_date': start_date,
                'end_date': end_date
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting model performance analysis: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Failed to get performance analysis'
        }), 500

@api_bp.route('/cleanup-malformed-urls', methods=['POST'])
@admin_required
def cleanup_malformed_urls_api():
    """
    API endpoint to clean up malformed URLs in the database.
    
    Performs database maintenance to fix or remove malformed URLs
    and returns detailed results of the cleanup operation.
    
    Returns:
        JSON response with cleanup results
    """
    try:
        # Import the cleanup function
        import db_content_utils as db
        
        # Run the cleanup
        results = db.clean_malformed_urls()
        
        logger.info(f"URL cleanup completed via API: {results}")
        
        return jsonify({
            "success": True,
            "message": "URL cleanup completed successfully",
            "results": results
        }), 200
        
    except Exception as e:
        logger.error(f"Error during URL cleanup via API: {str(e)}")
        return jsonify({
            "success": False,
            "error": f"Failed to clean up URLs: {str(e)}"
        }), 500

# Location API Routes
@api_bp.route('/locations')
@um.function_permission_required('ai_analytics')
def api_locations():
    """
    API endpoint to get location data.

    Returns extracted geographical locations from documents
    with optional category filtering.

    Returns:
        JSON response with location data
    """
    try:
        category = request.args.get('category')

        if category:
            from db_utils import get_locations_by_category
            locations = get_locations_by_category(category)
        else:
            from db_utils import get_all_extracted_locations
            locations = get_all_extracted_locations(include_sources=True)

        return jsonify({
            'success': True,
            'locations': locations,
            'count': len(locations)
        })
    except Exception as e:
        logger.error(f"Error retrieving locations via API: {str(e)}")
        return jsonify({'error': 'Failed to retrieve locations'}), 500

@api_bp.route('/location_statistics')
@um.function_permission_required('ai_analytics')
def api_location_statistics():
    """
    API endpoint to get location statistics.

    Provides statistical analysis of extracted locations
    including counts by type, region, and document.

    Returns:
        JSON response with location statistics
    """
    try:
        from db_utils import get_location_statistics

        statistics = get_location_statistics()

        return jsonify({
            'success': True,
            'statistics': statistics
        })
    except Exception as e:
        logger.error(f"Error retrieving location statistics: {str(e)}")
        return jsonify({'error': 'Failed to retrieve location statistics'}), 500

@api_bp.route('/locations/<int:location_id>', methods=['DELETE'])
@um.function_permission_required('ai_analytics')
def api_delete_location(location_id):
    """
    API endpoint to delete a single location.

    Args:
        location_id: ID of the location to delete

    Returns:
        JSON response with deletion status
    """
    try:
        from db_utils import delete_location_by_id

        success = delete_location_by_id(location_id)

        if success:
            return jsonify({
                'success': True,
                'message': 'Location deleted successfully'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Failed to delete location'
            }), 500

    except Exception as e:
        logger.error(f"Error deleting location {location_id}: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Error deleting location: {str(e)}'
        }), 500

@api_bp.route('/locations/bulk-delete', methods=['POST'])
@um.function_permission_required('ai_analytics')
def api_bulk_delete_locations():
    """
    API endpoint to delete multiple locations.

    Accepts a list of location IDs and deletes them in bulk.

    Returns:
        JSON response with bulk deletion results
    """
    try:
        data = request.get_json()
        location_ids = data.get('location_ids', [])

        if not location_ids:
            return jsonify({
                'success': False,
                'message': 'No location IDs provided'
            }), 400

        from db_utils import bulk_delete_locations

        deleted_count, failed_count = bulk_delete_locations(location_ids)

        return jsonify({
            'success': True,
            'message': f'Deleted {deleted_count} locations, {failed_count} failed',
            'deleted_count': deleted_count,
            'failed_count': failed_count
        })

    except Exception as e:
        logger.error(f"Error bulk deleting locations: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Error bulk deleting locations: {str(e)}'
        }), 500

@api_bp.route('/extract-locations', methods=['POST'])
def api_extract_locations():
    """
    API endpoint to extract locations from text.

    Uses NLP to extract geographical locations from provided text
    and returns structured location data.

    Returns:
        JSON response with extracted locations
    """
    try:
        data = request.get_json()
        text = data.get('text', '')

        if not text:
            return jsonify({
                'success': False,
                'error': 'No text provided'
            }), 400

        # Import location extraction utilities
        import geo_utils

        # Extract locations from text
        locations = geo_utils.extract_locations_from_text(text)

        return jsonify({
            'success': True,
            'locations': locations,
            'count': len(locations)
        })

    except Exception as e:
        logger.error(f"Error extracting locations from text: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Error extracting locations: {str(e)}'
        }), 500

# Query processing route
@api_bp.route('/query/<category>', methods=['POST'])
def query_route(category):
    """
    Process user queries and return AI responses.

    This is the main query processing endpoint that handles
    user questions and returns AI-generated responses with sources.

    Args:
        category: Document category to query

    Returns:
        JSON response with answer, sources, and metadata
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({"error": "No data provided"}), 400

        question = data.get('question', '').strip()
        if not question:
            return jsonify({"error": "Question is required"}), 400

        # Get session information
        from session_utils import get_or_create_session
        session_id, session_start, device_fingerprint = get_or_create_session()

        # Get additional parameters
        client_name = data.get('client_name', 'Anonymous')
        anti_hallucination_mode = data.get('anti_hallucination_mode', 'strict')
        current_model = data.get('current_model', 'llama3.1:8b-instruct-q4_K_M')
        current_embedding = data.get('current_embedding', 'mxbai-embed-large:latest')
        current_vision = data.get('current_vision', 'llama3.2-vision:11b-instruct-q4_K_M')

        # Process the query
        result = query_category(
            category=category,
            question=question,
            anti_hallucination_mode=anti_hallucination_mode,
            current_model=current_model,
            current_embedding=current_embedding,
            current_vision=current_vision
        )

        # Extract response components
        answer = result.get('answer', '')
        sources = result.get('sources', [])
        images = result.get('images', [])
        pdf_links = result.get('pdf_links', [])
        metadata = result.get('metadata', {})
        url_images = result.get('url_images', [])
        pdf_images = result.get('pdf_images', [])
        document_thumbnails = result.get('document_thumbnails', [])

        # Save chat history
        chat_id = db_utils.save_chat_history(
            category, question, answer, sources, images, pdf_links, metadata,
            url_images, pdf_images, client_name, session_id, session_start,
            device_fingerprint, document_thumbnails, anti_hallucination_mode,
            current_model, current_embedding, current_vision
        )

        # Save analytics data
        if chat_id:
            ip_address, city, region, country, latitude, longitude = geo_utils.get_location_for_analytics()
            db_utils.save_analytics_data(
                chat_id, category, len(question), len(answer),
                metadata.get('processing_time', 0), ip_address, city, region,
                country, latitude, longitude, device_fingerprint, client_name,
                current_model, current_embedding, current_vision
            )

        return jsonify({
            "answer": answer,
            "sources": sources,
            "images": images,
            "pdf_links": pdf_links,
            "metadata": metadata,
            "url_images": url_images,
            "pdf_images": pdf_images,
            "document_thumbnails": document_thumbnails,
            "session_id": session_id,
            "session_start": session_start
        }), 200

    except Exception as e:
        logger.error(f"Error processing query: {str(e)}")
        return jsonify({
            "error": "Query processing failed",
            "details": str(e),
            "metadata": {"error": str(e)},
            "session_id": session.get('session_id'),
            "session_start": session.get('session_start')
        }), 500
